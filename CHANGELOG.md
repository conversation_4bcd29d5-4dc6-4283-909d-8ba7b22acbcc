## <small>1.0.71 (2025-08-21)</small>

- fix: do not use BrowserTime as spa_load start tag ([ded13bb](https://github.com/boxtoys/Aegis/commit/ded13bb))

## <small>1.0.70 (2025-08-13)</small>

- fix: **vue** variables are compressed ([8256a11](https://github.com/boxtoys/Aegis/commit/8256a11))
- fix: 修复onSPA_LOAD中脚本加载逻辑，确保正确添加待处理脚本 ([ed161cb](https://github.com/boxtoys/Aegis/commit/ed161cb))
- fix: 修复onSPA_LOAD中CSS异步加载和背景图片检测的问题 ([839a368](https://github.com/boxtoys/Aegis/commit/839a368))
- fix: 在 onSPA_LOAD 测试中添加 **SPA_LOAD_READY_CALLBACK** 调用，以确保回调逻辑在不同场景下正常执行 ([d9373bd](https://github.com/boxtoys/Aegis/commit/d9373bd))
- fix: abort reason not consistent across platforms ([13e3989](https://github.com/boxtoys/Aegis/commit/13e3989))
- fix: api test case timeout ([90d2ef1](https://github.com/boxtoys/Aegis/commit/90d2ef1))
- fix: build ts error ([1eefd63](https://github.com/boxtoys/Aegis/commit/1eefd63))
- fix: case error cannot exit mock server ([caadff3](https://github.com/boxtoys/Aegis/commit/caadff3))
- fix: change protocol words ([5098b22](https://github.com/boxtoys/Aegis/commit/5098b22))
- fix: circular dependencies ([4e5183b](https://github.com/boxtoys/Aegis/commit/4e5183b))
- fix: cleanupTasks is not a global variable ([ce43dbf](https://github.com/boxtoys/Aegis/commit/ce43dbf))
- fix: cookie path issue in cookie.ts ([950cadd](https://github.com/boxtoys/Aegis/commit/950cadd))
- fix: docs error ([5c6e973](https://github.com/boxtoys/Aegis/commit/5c6e973))
- fix: document.elementFromPoint is not available and does not report logs ([bb8e687](https://github.com/boxtoys/Aegis/commit/bb8e687))
- fix: document.elementFromPoint return null scenes ([29e155b](https://github.com/boxtoys/Aegis/commit/29e155b))
- fix: element className is not string ([4c069b8](https://github.com/boxtoys/Aegis/commit/4c069b8))
- fix: ensure data flow using asynchronous methods ([9a7c3ec](https://github.com/boxtoys/Aegis/commit/9a7c3ec))
- fix: error messages should use console error ([03683db](https://github.com/boxtoys/Aegis/commit/03683db))
- fix: error when the plugin is not available ([a494ecf](https://github.com/boxtoys/Aegis/commit/a494ecf))
- fix: es6 -> es5 ([0028d22](https://github.com/boxtoys/Aegis/commit/0028d22))
- fix: fetch response has been consumed, text() again exception ([4115938](https://github.com/boxtoys/Aegis/commit/4115938))
- fix: fix issue ([38b6598](https://github.com/boxtoys/Aegis/commit/38b6598))
- fix: fix not report CLS ([7bdd2c9](https://github.com/boxtoys/Aegis/commit/7bdd2c9))
- fix: fix reduceHighResTimestamp issue ([04d6b74](https://github.com/boxtoys/Aegis/commit/04d6b74))
- fix: fix reducePrecision issue ([9c4130a](https://github.com/boxtoys/Aegis/commit/9c4130a))
- fix: fix spa timeout issue ([35e492f](https://github.com/boxtoys/Aegis/commit/35e492f))
- fix: fix test case ([1f0cef8](https://github.com/boxtoys/Aegis/commit/1f0cef8))
- fix: fix test case ([197477c](https://github.com/boxtoys/Aegis/commit/197477c))
- fix: fix test case ([5ee4d5a](https://github.com/boxtoys/Aegis/commit/5ee4d5a))
- fix: headers is not defined ([7825c95](https://github.com/boxtoys/Aegis/commit/7825c95))
- fix: hide error message ([53491eb](https://github.com/boxtoys/Aegis/commit/53491eb))
- fix: ignore background img detect on ios ([3b3b49c](https://github.com/boxtoys/Aegis/commit/3b3b49c))
- fix: iife mode same file name packing error ([72a6297](https://github.com/boxtoys/Aegis/commit/72a6297))
- fix: immediate option lost ([e4b3686](https://github.com/boxtoys/Aegis/commit/e4b3686))
- fix: incorrect word ([23b96e0](https://github.com/boxtoys/Aegis/commit/23b96e0))
- fix: js error plugin IIFE build error ([126b8be](https://github.com/boxtoys/Aegis/commit/126b8be))
- fix: local combo build file not found ([692e780](https://github.com/boxtoys/Aegis/commit/692e780))
- fix: multiple instances ([af3a672](https://github.com/boxtoys/Aegis/commit/af3a672))
- fix: no change in file hash ([8b89fd8](https://github.com/boxtoys/Aegis/commit/8b89fd8))
- fix: node v21 presence navigator api ([b8f8902](https://github.com/boxtoys/Aegis/commit/b8f8902))
- fix: normalize pathname ([0c9e9d4](https://github.com/boxtoys/Aegis/commit/0c9e9d4))
- fix: package introduction mode prioritization issue ([6f24e30](https://github.com/boxtoys/Aegis/commit/6f24e30))
- fix: plugins is undefined array, plugins[i].name is error ([491f70e](https://github.com/boxtoys/Aegis/commit/491f70e))
- fix: precollect plugin setup order ([6257428](https://github.com/boxtoys/Aegis/commit/6257428))
- fix: remote combo remove duplicates ([31913a0](https://github.com/boxtoys/Aegis/commit/31913a0))
- fix: remove sub tsconfig json ([9a7d1b8](https://github.com/boxtoys/Aegis/commit/9a7d1b8))
- fix: rollup build error ([50a8d30](https://github.com/boxtoys/Aegis/commit/50a8d30))
- fix: rollup build error: Unterminated string constant ([59b2ac3](https://github.com/boxtoys/Aegis/commit/59b2ac3))
- fix: rollupError: Unterminated string constant ([15c7ae7](https://github.com/boxtoys/Aegis/commit/15c7ae7))
- fix: scenarios where element.tagName is null ([0030f14](https://github.com/boxtoys/Aegis/commit/0030f14))
- fix: spa load reset done ([a5f98f2](https://github.com/boxtoys/Aegis/commit/a5f98f2))
- fix: test cases ([a5e756a](https://github.com/boxtoys/Aegis/commit/a5e756a))
- fix: test cases network error ([3431567](https://github.com/boxtoys/Aegis/commit/3431567))
- fix: ts error ([4b89d52](https://github.com/boxtoys/Aegis/commit/4b89d52))
- fix: ts error ([9e6d75c](https://github.com/boxtoys/Aegis/commit/9e6d75c))
- fix: ts error ([4afa91f](https://github.com/boxtoys/Aegis/commit/4afa91f))
- fix: ts error ([db183c7](https://github.com/boxtoys/Aegis/commit/db183c7))
- fix: ts error ([2c59773](https://github.com/boxtoys/Aegis/commit/2c59773))
- fix: ts type error ([a702773](https://github.com/boxtoys/Aegis/commit/a702773))
- fix: type error ([726e17b](https://github.com/boxtoys/Aegis/commit/726e17b))
- fix: update combo.ts to include version parameter in filterChoices function ([0ce40b7](https://github.com/boxtoys/Aegis/commit/0ce40b7))
- fix: url case issue ([978ec4b](https://github.com/boxtoys/Aegis/commit/978ec4b))
- fix: variable init timing ([54c0203](https://github.com/boxtoys/Aegis/commit/54c0203))
- fix: vue3 vue-router first time using replaceState for jump ([e0b3ee6](https://github.com/boxtoys/Aegis/commit/e0b3ee6))
- fix: xhr InvalidStateError DOMException ([ae04fb8](https://github.com/boxtoys/Aegis/commit/ae04fb8))
- 1.0.18 ([5807f6d](https://github.com/boxtoys/Aegis/commit/5807f6d))
- 1.0.19 ([1a059a1](https://github.com/boxtoys/Aegis/commit/1a059a1))
- 1.0.20 ([da21327](https://github.com/boxtoys/Aegis/commit/da21327))
- 1.0.21 ([aba86a1](https://github.com/boxtoys/Aegis/commit/aba86a1))
- 1.0.22 ([3fb4fce](https://github.com/boxtoys/Aegis/commit/3fb4fce))
- 1.0.23 ([ecb41e4](https://github.com/boxtoys/Aegis/commit/ecb41e4))
- 1.0.24 ([23d9bc8](https://github.com/boxtoys/Aegis/commit/23d9bc8))
- 1.0.25 ([4cfa080](https://github.com/boxtoys/Aegis/commit/4cfa080))
- 1.0.26 ([dbccb19](https://github.com/boxtoys/Aegis/commit/dbccb19))
- 1.0.27 ([0f5cbe7](https://github.com/boxtoys/Aegis/commit/0f5cbe7))
- 1.0.28 ([a011fd8](https://github.com/boxtoys/Aegis/commit/a011fd8))
- 1.0.29 ([5d0feae](https://github.com/boxtoys/Aegis/commit/5d0feae))
- 1.0.30 ([1129fe3](https://github.com/boxtoys/Aegis/commit/1129fe3))
- 1.0.31 ([7e6b011](https://github.com/boxtoys/Aegis/commit/7e6b011))
- 1.0.32 ([721b7e5](https://github.com/boxtoys/Aegis/commit/721b7e5))
- 1.0.33 ([ff582d0](https://github.com/boxtoys/Aegis/commit/ff582d0))
- 1.0.34 ([3a9fbc6](https://github.com/boxtoys/Aegis/commit/3a9fbc6))
- 1.0.35 ([994ddbf](https://github.com/boxtoys/Aegis/commit/994ddbf))
- 1.0.36 ([25dceba](https://github.com/boxtoys/Aegis/commit/25dceba))
- 1.0.37 ([43cdeb9](https://github.com/boxtoys/Aegis/commit/43cdeb9))
- 1.0.38 ([9aac97d](https://github.com/boxtoys/Aegis/commit/9aac97d))
- 1.0.39 ([b33fdf4](https://github.com/boxtoys/Aegis/commit/b33fdf4))
- 1.0.40 ([ff49119](https://github.com/boxtoys/Aegis/commit/ff49119))
- 1.0.41 ([51e2bb3](https://github.com/boxtoys/Aegis/commit/51e2bb3))
- 1.0.42 ([6366c56](https://github.com/boxtoys/Aegis/commit/6366c56))
- 1.0.43 ([36c37b1](https://github.com/boxtoys/Aegis/commit/36c37b1))
- 1.0.44 ([eb75ebb](https://github.com/boxtoys/Aegis/commit/eb75ebb))
- 1.0.45 ([4e7eda1](https://github.com/boxtoys/Aegis/commit/4e7eda1))
- 1.0.46 ([4bcdf6b](https://github.com/boxtoys/Aegis/commit/4bcdf6b))
- 1.0.47 ([cb1394b](https://github.com/boxtoys/Aegis/commit/cb1394b))
- 1.0.48 ([7179f1a](https://github.com/boxtoys/Aegis/commit/7179f1a))
- 1.0.49 ([137718a](https://github.com/boxtoys/Aegis/commit/137718a))
- 1.0.50 ([4062713](https://github.com/boxtoys/Aegis/commit/4062713))
- 1.0.51 ([69d6656](https://github.com/boxtoys/Aegis/commit/69d6656))
- 1.0.52 ([6c2d771](https://github.com/boxtoys/Aegis/commit/6c2d771))
- 1.0.53 ([bd61dd0](https://github.com/boxtoys/Aegis/commit/bd61dd0))
- 1.0.54 ([dce74c9](https://github.com/boxtoys/Aegis/commit/dce74c9))
- 1.0.55 ([cc41c6f](https://github.com/boxtoys/Aegis/commit/cc41c6f))
- 1.0.56 ([167cde5](https://github.com/boxtoys/Aegis/commit/167cde5))
- 1.0.57 ([13ef7bc](https://github.com/boxtoys/Aegis/commit/13ef7bc))
- 1.0.58 ([c38cd95](https://github.com/boxtoys/Aegis/commit/c38cd95))
- 1.0.59 ([d5393f8](https://github.com/boxtoys/Aegis/commit/d5393f8))
- 1.0.60 ([63f6229](https://github.com/boxtoys/Aegis/commit/63f6229))
- 1.0.61 ([3616840](https://github.com/boxtoys/Aegis/commit/3616840))
- 1.0.62 ([fa1d0e9](https://github.com/boxtoys/Aegis/commit/fa1d0e9))
- 1.0.63 ([e4a4209](https://github.com/boxtoys/Aegis/commit/e4a4209))
- 1.0.64 ([a05aef7](https://github.com/boxtoys/Aegis/commit/a05aef7))
- 1.0.66 ([ac42eb9](https://github.com/boxtoys/Aegis/commit/ac42eb9))
- 1.0.66 ([f7c798c](https://github.com/boxtoys/Aegis/commit/f7c798c))
- 1.0.67 ([d6f951d](https://github.com/boxtoys/Aegis/commit/d6f951d))
- 1.0.68 ([2762b5e](https://github.com/boxtoys/Aegis/commit/2762b5e))
- 1.0.69 ([7c8f002](https://github.com/boxtoys/Aegis/commit/7c8f002))
- 白屏插件新增预不合法窗口逻辑 ([d520d18](https://github.com/boxtoys/Aegis/commit/d520d18))
- 新增资源插件transfersize ([84e74be](https://github.com/boxtoys/Aegis/commit/84e74be))
- Initial commit ([c18a50f](https://github.com/boxtoys/Aegis/commit/c18a50f))
- refactor: 优化 onSPA_LOAD 函数中的数组操作，使用 filter 方法替代 splice 以提高代码可读性和性能 ([869fd47](https://github.com/boxtoys/Aegis/commit/869fd47))
- refactor: add event handling ([2df776b](https://github.com/boxtoys/Aegis/commit/2df776b))
- refactor: add findIndex polyfill ([fe4b5f4](https://github.com/boxtoys/Aegis/commit/fe4b5f4))
- refactor: add get plugin method \& plugin export option ([3112fa6](https://github.com/boxtoys/Aegis/commit/3112fa6))
- refactor: add JSONUtils ([a28d794](https://github.com/boxtoys/Aegis/commit/a28d794))
- refactor: add requestIdleCallback polyfill ([10653b5](https://github.com/boxtoys/Aegis/commit/10653b5))
- refactor: added native method check logic to ensure that the original methods are [native code] ([ef5ad06](https://github.com/boxtoys/Aegis/commit/ef5ad06))
- refactor: adjust build policy ([0484666](https://github.com/boxtoys/Aegis/commit/0484666))
- refactor: aegis class and add tests ([24ada6f](https://github.com/boxtoys/Aegis/commit/24ada6f))
- refactor: aegis class and Event interface ([85d525c](https://github.com/boxtoys/Aegis/commit/85d525c))
- refactor: aop replace the apply method with the call method ([b864fda](https://github.com/boxtoys/Aegis/commit/b864fda))
- refactor: aop supports multiple layers of proxies ([736b1ae](https://github.com/boxtoys/Aegis/commit/736b1ae))
- refactor: async detect vue router ([2dddf73](https://github.com/boxtoys/Aegis/commit/2dddf73))
- refactor: blankScreen plugin use pageview plugin mode option ([77cf7e6](https://github.com/boxtoys/Aegis/commit/77cf7e6))
- refactor: change from proxy once to proxy multiple times ([95567e0](https://github.com/boxtoys/Aegis/commit/95567e0))
- refactor: change pointerup to pointerdown ([dd75a54](https://github.com/boxtoys/Aegis/commit/dd75a54))
- refactor: clean istanbul ignore comment ([b8133db](https://github.com/boxtoys/Aegis/commit/b8133db))
- refactor: clear 💩 ([ece02b3](https://github.com/boxtoys/Aegis/commit/ece02b3))
- refactor: clear 💩 ([38bf5bd](https://github.com/boxtoys/Aegis/commit/38bf5bd))
- refactor: clear 💩💩💩 ([b2d98bc](https://github.com/boxtoys/Aegis/commit/b2d98bc))
- refactor: code clean ([0f20df7](https://github.com/boxtoys/Aegis/commit/0f20df7))
- refactor: code optimize ([9ba831d](https://github.com/boxtoys/Aegis/commit/9ba831d))
- refactor: code optimize ([6927a8f](https://github.com/boxtoys/Aegis/commit/6927a8f))
- refactor: code optimize ([b1ba941](https://github.com/boxtoys/Aegis/commit/b1ba941))
- refactor: code optimize ([109151b](https://github.com/boxtoys/Aegis/commit/109151b))
- refactor: code optimize ([5fb805f](https://github.com/boxtoys/Aegis/commit/5fb805f))
- refactor: code optimize ([a1b3f8d](https://github.com/boxtoys/Aegis/commit/a1b3f8d))
- refactor: code optimize ([34d38de](https://github.com/boxtoys/Aegis/commit/34d38de))
- refactor: code optimize ([4914d4e](https://github.com/boxtoys/Aegis/commit/4914d4e))
- refactor: code optimize ([1c9cd95](https://github.com/boxtoys/Aegis/commit/1c9cd95))
- refactor: code optimize ([5b5f9fb](https://github.com/boxtoys/Aegis/commit/5b5f9fb))
- refactor: code optimize ([3e7310a](https://github.com/boxtoys/Aegis/commit/3e7310a))
- refactor: code optimize ([3b7bc94](https://github.com/boxtoys/Aegis/commit/3b7bc94))
- refactor: code optimize ([83a9ed7](https://github.com/boxtoys/Aegis/commit/83a9ed7))
- refactor: code optimize ([f97d8f1](https://github.com/boxtoys/Aegis/commit/f97d8f1))
- refactor: code optimize ([3a9daf8](https://github.com/boxtoys/Aegis/commit/3a9daf8))
- refactor: code optimize ([c6ff78e](https://github.com/boxtoys/Aegis/commit/c6ff78e))
- refactor: code optimize ([739f864](https://github.com/boxtoys/Aegis/commit/739f864))
- refactor: code optimize ([42a8cdf](https://github.com/boxtoys/Aegis/commit/42a8cdf))
- refactor: code optimize ([0dabe67](https://github.com/boxtoys/Aegis/commit/0dabe67))
- refactor: code optimize ([ed20e9d](https://github.com/boxtoys/Aegis/commit/ed20e9d))
- refactor: code optimize ([b94d26d](https://github.com/boxtoys/Aegis/commit/b94d26d))
- refactor: code optimize ([516c267](https://github.com/boxtoys/Aegis/commit/516c267))
- refactor: code optimize ([924c5d8](https://github.com/boxtoys/Aegis/commit/924c5d8))
- refactor: code optimize ([4687dc6](https://github.com/boxtoys/Aegis/commit/4687dc6))
- refactor: code optimize ([fd5771e](https://github.com/boxtoys/Aegis/commit/fd5771e))
- refactor: code optimize ([1db681e](https://github.com/boxtoys/Aegis/commit/1db681e))
- refactor: code optimize ([78fe839](https://github.com/boxtoys/Aegis/commit/78fe839))
- refactor: event callback context and parameters ([e0fa065](https://github.com/boxtoys/Aegis/commit/e0fa065))
- refactor: extract api observer for use by other plugins ([9957f59](https://github.com/boxtoys/Aegis/commit/9957f59))
- refactor: extract detectVueFramework code \& support vue2 and vue3 \& support errorHandler detect ([0fa5e80](https://github.com/boxtoys/Aegis/commit/0fa5e80))
- refactor: extract route observer for use by other plugins ([de2670b](https://github.com/boxtoys/Aegis/commit/de2670b))
- refactor: filterByType function to filterResourceByType in staticResource.ts ([8e8da51](https://github.com/boxtoys/Aegis/commit/8e8da51))
- refactor: getElementUrl function in resourceError.ts ([b4ae0da](https://github.com/boxtoys/Aegis/commit/b4ae0da))
- refactor: getElementUrl function in resourceError.ts to handle different target types ([f4f3c00](https://github.com/boxtoys/Aegis/commit/f4f3c00))
- refactor: getElementUrl function in resourceError.ts to handle different target types ([c8ee4b2](https://github.com/boxtoys/Aegis/commit/c8ee4b2))
- refactor: iife build clean useless code ([7126ca2](https://github.com/boxtoys/Aegis/commit/7126ca2))
- refactor: improve code compression optimization ([3b3f56c](https://github.com/boxtoys/Aegis/commit/3b3f56c))
- refactor: improve UUID readability ([6bc9355](https://github.com/boxtoys/Aegis/commit/6bc9355))
- refactor: isomorphic optimization ([00f9fe7](https://github.com/boxtoys/Aegis/commit/00f9fe7))
- refactor: jsError plugin ([68c6986](https://github.com/boxtoys/Aegis/commit/68c6986))
- refactor: logic for extracting get points ([de3e25c](https://github.com/boxtoys/Aegis/commit/de3e25c))
- refactor: move checkIsIgnored function to common util ([20b090e](https://github.com/boxtoys/Aegis/commit/20b090e))
- refactor: normalize timestamps in resource creation ([149f610](https://github.com/boxtoys/Aegis/commit/149f610))
- refactor: optimize build IIFE package script ([9677214](https://github.com/boxtoys/Aegis/commit/9677214))
- refactor: optimize code ([802ba4c](https://github.com/boxtoys/Aegis/commit/802ba4c))
- refactor: optimize code ([54db2f9](https://github.com/boxtoys/Aegis/commit/54db2f9))
- refactor: optimize device detection and classification ([1f2c671](https://github.com/boxtoys/Aegis/commit/1f2c671))
- refactor: optimize PixelTransport send method for large data ([329f59d](https://github.com/boxtoys/Aegis/commit/329f59d))
- refactor: optimize report SDK error code ([740a6f1](https://github.com/boxtoys/Aegis/commit/740a6f1))
- refactor: optimize store module code ([0033924](https://github.com/boxtoys/Aegis/commit/0033924))
- refactor: optimize the performance of MutationObserver ([d055cbc](https://github.com/boxtoys/Aegis/commit/d055cbc))
- refactor: organize the directory structure ([78be3a9](https://github.com/boxtoys/Aegis/commit/78be3a9))
- refactor: organize the directory structure ([cfe2a4d](https://github.com/boxtoys/Aegis/commit/cfe2a4d))
- refactor: organize the directory structureo ([f1ffab9](https://github.com/boxtoys/Aegis/commit/f1ffab9))
- refactor: perf SPA_LOAD plugin use pageview plugin mode option ([4431493](https://github.com/boxtoys/Aegis/commit/4431493))
- refactor: preload code build process ([2173a45](https://github.com/boxtoys/Aegis/commit/2173a45))
- refactor: refactor code to use polyfills for Object.assign and includes ([58f1a8d](https://github.com/boxtoys/Aegis/commit/58f1a8d))
- refactor: refactor Event class to use generic types ([3e7ab70](https://github.com/boxtoys/Aegis/commit/3e7ab70))
- refactor: refactor event emitter to allow any argument types and return callback status ([cd8bc17](https://github.com/boxtoys/Aegis/commit/cd8bc17))
- refactor: refactor the request functions, unify the request parameter structure ([b9fadc9](https://github.com/boxtoys/Aegis/commit/b9fadc9))
- refactor: refactored the aop function, use a stack of proxies for method interception ([59a044a](https://github.com/boxtoys/Aegis/commit/59a044a))
- refactor: remove isPolyfill function at compile time ([61d7385](https://github.com/boxtoys/Aegis/commit/61d7385))
- refactor: remove runtime esModule detect, use static method and noModule property to detect ([8b483ab](https://github.com/boxtoys/Aegis/commit/8b483ab))
- refactor: remove skip from test for loading the same resource multiple times ([063b97c](https://github.com/boxtoys/Aegis/commit/063b97c))
- refactor: remove ts rest syntax polyfill ([1a7ef29](https://github.com/boxtoys/Aegis/commit/1a7ef29))
- refactor: remove xhr onerror handler ([8fdd7ad](https://github.com/boxtoys/Aegis/commit/8fdd7ad))
- refactor: rename inteface ([6b5757d](https://github.com/boxtoys/Aegis/commit/6b5757d))
- refactor: replace polyfill api ([c41d651](https://github.com/boxtoys/Aegis/commit/c41d651))
- refactor: request code optimize ([3f6286d](https://github.com/boxtoys/Aegis/commit/3f6286d))
- refactor: resource metrics data optimize ([012b178](https://github.com/boxtoys/Aegis/commit/012b178))
- refactor: resourceError integration and add createTiming function ([0f851bd](https://github.com/boxtoys/Aegis/commit/0f851bd))
- refactor: rewriting ConfigManager using class syntax ([73faa19](https://github.com/boxtoys/Aegis/commit/73faa19))
- refactor: rewriting Event, Aegis using class syntax ([f29f4cf](https://github.com/boxtoys/Aegis/commit/f29f4cf))
- refactor: rewriting Transports using class syntax ([93af6a9](https://github.com/boxtoys/Aegis/commit/93af6a9))
- refactor: standalone builder ([0476fbb](https://github.com/boxtoys/Aegis/commit/0476fbb))
- refactor: staticResource integration and add ignoreTypes option ([18c0ba2](https://github.com/boxtoys/Aegis/commit/18c0ba2))
- refactor: staticResource.spec.ts file ([404bc1e](https://github.com/boxtoys/Aegis/commit/404bc1e))
- refactor: store code optimize ([b2172c8](https://github.com/boxtoys/Aegis/commit/b2172c8))
- refactor: test framework change from jest to vitest ([b498028](https://github.com/boxtoys/Aegis/commit/b498028))
- refactor: the send data uses transports and supports multiple transport ([e4356e9](https://github.com/boxtoys/Aegis/commit/e4356e9))
- refactor: update normalizeTimestamp test descriptions ([2c16a83](https://github.com/boxtoys/Aegis/commit/2c16a83))
- refactor: use ES5 syntax ([9546c78](https://github.com/boxtoys/Aegis/commit/9546c78))
- refactor: use PointerEvent for multi-terminal compatibility ([ed71d96](https://github.com/boxtoys/Aegis/commit/ed71d96))
- refactor: vue detect code optimize ([0c85b7d](https://github.com/boxtoys/Aegis/commit/0c85b7d))
- refactor: when uid is passed in externally, uid is no longer stored ([2c5c84c](https://github.com/boxtoys/Aegis/commit/2c5c84c))
- refactor: wrap Date.now() ([b543838](https://github.com/boxtoys/Aegis/commit/b543838))
- chore: 1.0.0 ([7c85a4c](https://github.com/boxtoys/Aegis/commit/7c85a4c))
- chore: 发布SDK ([a503481](https://github.com/boxtoys/Aegis/commit/a503481))
- chore: 更换目录结构 ([5661e7b](https://github.com/boxtoys/Aegis/commit/5661e7b))
- chore: 锁定ts版本 ([2cef86f](https://github.com/boxtoys/Aegis/commit/2cef86f))
- chore: 修改注释 ([3727b85](https://github.com/boxtoys/Aegis/commit/3727b85))
- chore: add a packaging process to the largePictureInspect plugin ([ea769c4](https://github.com/boxtoys/Aegis/commit/ea769c4))
- chore: add action plugin ([f9b9c3b](https://github.com/boxtoys/Aegis/commit/f9b9c3b))
- chore: add action plugin build process ([3c539ec](https://github.com/boxtoys/Aegis/commit/3c539ec))
- chore: add ajax request ([64551a2](https://github.com/boxtoys/Aegis/commit/64551a2))
- chore: add aop function ([bd2dc4f](https://github.com/boxtoys/Aegis/commit/bd2dc4f))
- chore: add aop use case scenarios ([974d621](https://github.com/boxtoys/Aegis/commit/974d621))
- chore: add assign polyfill ([83940a1](https://github.com/boxtoys/Aegis/commit/83940a1))
- chore: add blank screen detection test with document complete ([6e5041c](https://github.com/boxtoys/Aegis/commit/6e5041c))
- chore: add builder doc ([9a2747b](https://github.com/boxtoys/Aegis/commit/9a2747b))
- chore: add comments ([dbd8a29](https://github.com/boxtoys/Aegis/commit/dbd8a29))
- chore: add comments to magic code ([1d14ddc](https://github.com/boxtoys/Aegis/commit/1d14ddc))
- chore: add common parameters ([1388f18](https://github.com/boxtoys/Aegis/commit/1388f18))
- chore: add config constants and update configManager ([9385f1e](https://github.com/boxtoys/Aegis/commit/9385f1e))
- chore: add cssSupports polyfill ([3d774b9](https://github.com/boxtoys/Aegis/commit/3d774b9))
- chore: add cursorrules ([7c09d52](https://github.com/boxtoys/Aegis/commit/7c09d52))
- chore: add custom builder function for data processing ([e358b55](https://github.com/boxtoys/Aegis/commit/e358b55))
- chore: add dom observer method ([5c6c835](https://github.com/boxtoys/Aegis/commit/5c6c835))
- chore: add encrypted env file ([ad288cf](https://github.com/boxtoys/Aegis/commit/ad288cf))
- chore: add error handling for integrations setup in Aegis ([87094cb](https://github.com/boxtoys/Aegis/commit/87094cb))
- chore: add feature plugin packing process ([8f5fc9d](https://github.com/boxtoys/Aegis/commit/8f5fc9d))
- chore: add feature polyfill ([2bdfaab](https://github.com/boxtoys/Aegis/commit/2bdfaab))
- chore: add functions for handling static resource errors and reporting performance data ([48661a3](https://github.com/boxtoys/Aegis/commit/48661a3))
- chore: add generic field: viewId ([ab1d28f](https://github.com/boxtoys/Aegis/commit/ab1d28f))
- chore: add global variable: BrowserTiming ([6ed4cb7](https://github.com/boxtoys/Aegis/commit/6ed4cb7))
- chore: add htmlTreeAsString util ([61f3472](https://github.com/boxtoys/Aegis/commit/61f3472))
- chore: add http \& bridge plugin build process ([c6ff1f3](https://github.com/boxtoys/Aegis/commit/c6ff1f3))
- chore: add Integrations interface ([cb01e67](https://github.com/boxtoys/Aegis/commit/cb01e67))
- chore: add isNode env check ([b18b1c6](https://github.com/boxtoys/Aegis/commit/b18b1c6))
- chore: add lifecycle hooks ([a91bf57](https://github.com/boxtoys/Aegis/commit/a91bf57))
- chore: add loader stub methods ([a3e1030](https://github.com/boxtoys/Aegis/commit/a3e1030))
- chore: add multiple instance manage ([1281484](https://github.com/boxtoys/Aegis/commit/1281484))
- chore: add network type common field ([fb8c8ef](https://github.com/boxtoys/Aegis/commit/fb8c8ef))
- chore: add perf plugin option ([1713b0a](https://github.com/boxtoys/Aegis/commit/1713b0a))
- chore: add platform judgment code ([70b9c3a](https://github.com/boxtoys/Aegis/commit/70b9c3a))
- chore: add precollect test cases \& build precollect iife plugin ([a1dac62](https://github.com/boxtoys/Aegis/commit/a1dac62))
- chore: add preload logic ([7569367](https://github.com/boxtoys/Aegis/commit/7569367))
- chore: add resource \& resourceError plugin build policy ([f94fcfc](https://github.com/boxtoys/Aegis/commit/f94fcfc))
- chore: add sample rate logic ([5c0b7a3](https://github.com/boxtoys/Aegis/commit/5c0b7a3))
- chore: add sid common field ([cab0d59](https://github.com/boxtoys/Aegis/commit/cab0d59))
- chore: add store fallback ([4e93263](https://github.com/boxtoys/Aegis/commit/4e93263))
- chore: add test cases ([ceceda6](https://github.com/boxtoys/Aegis/commit/ceceda6))
- chore: add toArray polyfill ([97a86f1](https://github.com/boxtoys/Aegis/commit/97a86f1))
- chore: add transport attribute check ([3a70a06](https://github.com/boxtoys/Aegis/commit/3a70a06))
- chore: add types ([2182058](https://github.com/boxtoys/Aegis/commit/2182058))
- chore: add utils ([0164509](https://github.com/boxtoys/Aegis/commit/0164509))
- chore: addition of the history non-existence treatment case ([db329a9](https://github.com/boxtoys/Aegis/commit/db329a9))
- chore: adjust changelog ([5135517](https://github.com/boxtoys/Aegis/commit/5135517))
- chore: adjust changelog ([c05631e](https://github.com/boxtoys/Aegis/commit/c05631e))
- chore: adjust iife compilation result ([7673771](https://github.com/boxtoys/Aegis/commit/7673771))
- chore: aegis core add reportUrl field ([cdabfba](https://github.com/boxtoys/Aegis/commit/cdabfba))
- chore: aegis destroy remove all event ([94e458d](https://github.com/boxtoys/Aegis/commit/94e458d))
- chore: aop method add native method check logic ([058d988](https://github.com/boxtoys/Aegis/commit/058d988))
- chore: aop support restore callback & add resolveURL method ([a9fd72f](https://github.com/boxtoys/Aegis/commit/a9fd72f))
- chore: api plugin payload add baseUrl \& queryString params ([80eeb54](https://github.com/boxtoys/Aegis/commit/80eeb54))
- chore: api plugin payload data add ok field ([68a698f](https://github.com/boxtoys/Aegis/commit/68a698f))
- chore: blankScreen plugin support ignoreUrls parameter ([d89275f](https://github.com/boxtoys/Aegis/commit/d89275f))
- chore: bridge plugin support ignoreUrls parameter ([90e9f50](https://github.com/boxtoys/Aegis/commit/90e9f50))
- chore: browser common add os common field ([891e950](https://github.com/boxtoys/Aegis/commit/891e950))
- chore: build cjs \& esm code ([9073f09](https://github.com/boxtoys/Aegis/commit/9073f09))
- chore: build iife code ([cbeb407](https://github.com/boxtoys/Aegis/commit/cbeb407))
- chore: change browser name ([9b0d21d](https://github.com/boxtoys/Aegis/commit/9b0d21d))
- chore: change browserslist rules ([a17f2cd](https://github.com/boxtoys/Aegis/commit/a17f2cd))
- chore: change custom report payload options ([7f1b004](https://github.com/boxtoys/Aegis/commit/7f1b004))
- chore: change customErrorReportPolicy name to reportPolicy name ([555dd2b](https://github.com/boxtoys/Aegis/commit/555dd2b))
- chore: change env ([2c4a66d](https://github.com/boxtoys/Aegis/commit/2c4a66d))
- chore: change http integration name to api ([be2b84e](https://github.com/boxtoys/Aegis/commit/be2b84e))
- chore: change Integration declare ([fa4ea05](https://github.com/boxtoys/Aegis/commit/fa4ea05))
- chore: change on & off return type ([c9818a2](https://github.com/boxtoys/Aegis/commit/c9818a2))
- chore: change precollect report sri data ([16d05da](https://github.com/boxtoys/Aegis/commit/16d05da))
- chore: change SDK name ([2ecab51](https://github.com/boxtoys/Aegis/commit/2ecab51))
- chore: change test domain ([42a2a8e](https://github.com/boxtoys/Aegis/commit/42a2a8e))
- chore: change type define ([9d35e5a](https://github.com/boxtoys/Aegis/commit/9d35e5a))
- chore: change type name ([a7aff35](https://github.com/boxtoys/Aegis/commit/a7aff35))
- chore: changelog ([1e4d595](https://github.com/boxtoys/Aegis/commit/1e4d595))
- chore: cid depends on pageview pid ([76500d9](https://github.com/boxtoys/Aegis/commit/76500d9))
- chore: clean istanbul ignore comment ([68ac754](https://github.com/boxtoys/Aegis/commit/68ac754))
- chore: clean istanbul ignore comment ([da2ce72](https://github.com/boxtoys/Aegis/commit/da2ce72))
- chore: clean istanbul ignore comment ([b6ed12b](https://github.com/boxtoys/Aegis/commit/b6ed12b))
- chore: clean istanbul ignore comment ([c1caf28](https://github.com/boxtoys/Aegis/commit/c1caf28))
- chore: clean istanbul ignore comment ([1555412](https://github.com/boxtoys/Aegis/commit/1555412))
- chore: clean istanbul ignore comment ([43031ac](https://github.com/boxtoys/Aegis/commit/43031ac))
- chore: clear changelog ([56b9d3d](https://github.com/boxtoys/Aegis/commit/56b9d3d))
- chore: clear code comments ([5401868](https://github.com/boxtoys/Aegis/commit/5401868))
- chore: code clean ([cb1080f](https://github.com/boxtoys/Aegis/commit/cb1080f))
- chore: code format ([eb56558](https://github.com/boxtoys/Aegis/commit/eb56558))
- chore: code optimize ([c1a1087](https://github.com/boxtoys/Aegis/commit/c1a1087))
- chore: code optimize ([3d379cf](https://github.com/boxtoys/Aegis/commit/3d379cf))
- chore: code optimize ([6350963](https://github.com/boxtoys/Aegis/commit/6350963))
- chore: code optimize ([cffc518](https://github.com/boxtoys/Aegis/commit/cffc518))
- chore: code optimize ([bb84208](https://github.com/boxtoys/Aegis/commit/bb84208))
- chore: code optimize ([93188fd](https://github.com/boxtoys/Aegis/commit/93188fd))
- chore: code revert ([9e67686](https://github.com/boxtoys/Aegis/commit/9e67686))
- chore: code review ([146e832](https://github.com/boxtoys/Aegis/commit/146e832))
- chore: code rollback ([d8292e9](https://github.com/boxtoys/Aegis/commit/d8292e9))
- chore: code styling ([8bf5096](https://github.com/boxtoys/Aegis/commit/8bf5096))
- chore: combine multiple test reports ([face121](https://github.com/boxtoys/Aegis/commit/face121))
- chore: combo build add local process ([d561060](https://github.com/boxtoys/Aegis/commit/d561060))
- chore: commented out unused code in blankScreen.spec.ts ([1387311](https://github.com/boxtoys/Aegis/commit/1387311))
- chore: compatibility handling ([97f10d1](https://github.com/boxtoys/Aegis/commit/97f10d1))
- chore: config the changelog ([be4441f](https://github.com/boxtoys/Aegis/commit/be4441f))
- chore: config the device plugin packaging policy ([321cc76](https://github.com/boxtoys/Aegis/commit/321cc76))
- chore: configManager add addTransport \& removeTransport method ([a9b7b37](https://github.com/boxtoys/Aegis/commit/a9b7b37))
- chore: configManager becomes private ([f44f555](https://github.com/boxtoys/Aegis/commit/f44f555))
- chore: cover lines ([b275087](https://github.com/boxtoys/Aegis/commit/b275087))
- chore: cover uitls ([4948e49](https://github.com/boxtoys/Aegis/commit/4948e49))
- chore: delete precollect ts-ignore comment ([f258883](https://github.com/boxtoys/Aegis/commit/f258883))
- chore: device plugin add custom infomation extract func ([ea85620](https://github.com/boxtoys/Aegis/commit/ea85620))
- chore: feature plugin support detect image strategy ([2d51260](https://github.com/boxtoys/Aegis/commit/2d51260))
- chore: feature plugin support remote feature list ([da021e7](https://github.com/boxtoys/Aegis/commit/da021e7))
- chore: format code ([659ae01](https://github.com/boxtoys/Aegis/commit/659ae01))
- chore: format code ([569ccc6](https://github.com/boxtoys/Aegis/commit/569ccc6))
- chore: format doc ([60826c0](https://github.com/boxtoys/Aegis/commit/60826c0))
- chore: from single dom to mutilple dom ([e7f7d97](https://github.com/boxtoys/Aegis/commit/e7f7d97))
- chore: ignore other lockfile ([3eb309e](https://github.com/boxtoys/Aegis/commit/3eb309e))
- chore: ignore types format ([21a6178](https://github.com/boxtoys/Aegis/commit/21a6178))
- chore: iife package change directory ([8309966](https://github.com/boxtoys/Aegis/commit/8309966))
- chore: improve deploy script ([94fa145](https://github.com/boxtoys/Aegis/commit/94fa145))
- chore: improve test config ([8f76e83](https://github.com/boxtoys/Aegis/commit/8f76e83))
- chore: improve types ([b4b4df6](https://github.com/boxtoys/Aegis/commit/b4b4df6))
- chore: improve types ([f0b6911](https://github.com/boxtoys/Aegis/commit/f0b6911))
- chore: init ([c2e6838](https://github.com/boxtoys/Aegis/commit/c2e6838))
- chore: init docs ([b1fc304](https://github.com/boxtoys/Aegis/commit/b1fc304))
- chore: initialization of the project ([e489687](https://github.com/boxtoys/Aegis/commit/e489687))
- chore: jsError build config ([311c033](https://github.com/boxtoys/Aegis/commit/311c033))
- chore: largePictureInspect plugin support dpr \& reportMode options ([25f7186](https://github.com/boxtoys/Aegis/commit/25f7186))
- chore: merge ([ec8b3ff](https://github.com/boxtoys/Aegis/commit/ec8b3ff))
- chore: modify build script ([bcb7336](https://github.com/boxtoys/Aegis/commit/bcb7336))
- chore: modify iife config, remove minify ([ec7d38c](https://github.com/boxtoys/Aegis/commit/ec7d38c))
- chore: on & off callback add instance type ([4bed77b](https://github.com/boxtoys/Aegis/commit/4bed77b))
- chore: optimize code ([7c5874e](https://github.com/boxtoys/Aegis/commit/7c5874e))
- chore: optimize combo script ([1b5d40d](https://github.com/boxtoys/Aegis/commit/1b5d40d))
- chore: optimize mock plugin error display ([d5fb83a](https://github.com/boxtoys/Aegis/commit/d5fb83a))
- chore: optimize script commands ([184d067](https://github.com/boxtoys/Aegis/commit/184d067))
- chore: page unload use visibilitychange event ([987e6c5](https://github.com/boxtoys/Aegis/commit/987e6c5))
- chore: pageview add source field ([1547ab1](https://github.com/boxtoys/Aegis/commit/1547ab1))
- chore: pageview plugin add detect vue-router ([c8c6634](https://github.com/boxtoys/Aegis/commit/c8c6634))
- chore: pageview plugin add initSend option ([46557ea](https://github.com/boxtoys/Aegis/commit/46557ea))
- chore: pageview support custom pageid extractor ([155c72e](https://github.com/boxtoys/Aegis/commit/155c72e))
- chore: plugin not found will output an error ([aae57f2](https://github.com/boxtoys/Aegis/commit/aae57f2))
- chore: preloadCode build process done ([3346141](https://github.com/boxtoys/Aegis/commit/3346141))
- chore: preset static methods to prevent errors when source not loaded ([3c4b5ed](https://github.com/boxtoys/Aegis/commit/3c4b5ed))
- chore: preset transport static methods to prevent errors when source not loaded ([32c609e](https://github.com/boxtoys/Aegis/commit/32c609e))
- chore: processing of SVGElement ([aa83dcf](https://github.com/boxtoys/Aegis/commit/aa83dcf))
- chore: publish ([1337d53](https://github.com/boxtoys/Aegis/commit/1337d53))
- chore: publish ([c9af2fd](https://github.com/boxtoys/Aegis/commit/c9af2fd))
- chore: publish cdn ([5513cd7](https://github.com/boxtoys/Aegis/commit/5513cd7))
- chore: publish cdn files ([25f6df5](https://github.com/boxtoys/Aegis/commit/25f6df5))
- chore: publish CDN files ([af69837](https://github.com/boxtoys/Aegis/commit/af69837))
- chore: publish CDN files ([b6dd0a1](https://github.com/boxtoys/Aegis/commit/b6dd0a1))
- chore: publish CDN files ([2e2f44d](https://github.com/boxtoys/Aegis/commit/2e2f44d))
- chore: publish cdn package ([679409c](https://github.com/boxtoys/Aegis/commit/679409c))
- chore: publish iife files to CDN ([8a46639](https://github.com/boxtoys/Aegis/commit/8a46639))
- chore: publish sdk ([0482127](https://github.com/boxtoys/Aegis/commit/0482127))
- chore: pv plugin add syncCid option & change docs ([a0fbdc8](https://github.com/boxtoys/Aegis/commit/a0fbdc8))
- chore: refactor blank screen setup function ([2b6158c](https://github.com/boxtoys/Aegis/commit/2b6158c))
- chore: refactor blankScreen integration options ([4308b22](https://github.com/boxtoys/Aegis/commit/4308b22))
- chore: refactor blankScreen.ts and add test fixtures ([73afa56](https://github.com/boxtoys/Aegis/commit/73afa56))
- chore: refactor code to improve performance and readability ([1756955](https://github.com/boxtoys/Aegis/commit/1756955))
- chore: refactor: refactor fetch and xhr utils to support custom fetch and xhr ([1b56b46](https://github.com/boxtoys/Aegis/commit/1b56b46))
- chore: remove skip flag ([c90df58](https://github.com/boxtoys/Aegis/commit/c90df58))
- chore: remove useless code ([a8a15c1](https://github.com/boxtoys/Aegis/commit/a8a15c1))
- chore: rename preload to loader ([4beb571](https://github.com/boxtoys/Aegis/commit/4beb571))
- chore: request support retry policy ([af87194](https://github.com/boxtoys/Aegis/commit/af87194))
- chore: resource loading error no message info ([8ae6d87](https://github.com/boxtoys/Aegis/commit/8ae6d87))
- chore: resource release excludes loader.js ([e0aa9fb](https://github.com/boxtoys/Aegis/commit/e0aa9fb))
- chore: review code ([e7ab616](https://github.com/boxtoys/Aegis/commit/e7ab616))
- chore: review code ([d8d7342](https://github.com/boxtoys/Aegis/commit/d8d7342))
- chore: review code ([17e6173](https://github.com/boxtoys/Aegis/commit/17e6173))
- chore: review docs ([496fbfc](https://github.com/boxtoys/Aegis/commit/496fbfc))
- chore: rollup iife build add code comments ([6eeb928](https://github.com/boxtoys/Aegis/commit/6eeb928))
- chore: rollup iife build loader add comments ([17f15ab](https://github.com/boxtoys/Aegis/commit/17f15ab))
- chore: scenarios where the largePictureInspect plugin supports dynamic background images ([9511ffb](https://github.com/boxtoys/Aegis/commit/9511ffb))
- chore: sdk upload address using environment variables ([f19c022](https://github.com/boxtoys/Aegis/commit/f19c022))
- chore: set build target to ES5 ([c125601](https://github.com/boxtoys/Aegis/commit/c125601))
- chore: show combo url log ([4a68b0e](https://github.com/boxtoys/Aegis/commit/4a68b0e))
- chore: show file size after compilation ([c795058](https://github.com/boxtoys/Aegis/commit/c795058))
- chore: spaload support ignoreUrls params ([fe56815](https://github.com/boxtoys/Aegis/commit/fe56815))
- chore: stage code ([4e1724d](https://github.com/boxtoys/Aegis/commit/4e1724d))
- chore: stage code ([1ab8a29](https://github.com/boxtoys/Aegis/commit/1ab8a29))
- chore: stage code ([502849a](https://github.com/boxtoys/Aegis/commit/502849a))
- chore: stage types ([4af987a](https://github.com/boxtoys/Aegis/commit/4af987a))
- chore: stash code ([2407d09](https://github.com/boxtoys/Aegis/commit/2407d09))
- chore: stash code ([84c8781](https://github.com/boxtoys/Aegis/commit/84c8781))
- chore: stash code ([76212cf](https://github.com/boxtoys/Aegis/commit/76212cf))
- chore: stash code ([ac68bbe](https://github.com/boxtoys/Aegis/commit/ac68bbe))
- chore: stash code ([2ec3aa8](https://github.com/boxtoys/Aegis/commit/2ec3aa8))
- chore: stash code ([c5c76c9](https://github.com/boxtoys/Aegis/commit/c5c76c9))
- chore: stash code ([d6912c8](https://github.com/boxtoys/Aegis/commit/d6912c8))
- chore: test coverage ([bc0e296](https://github.com/boxtoys/Aegis/commit/bc0e296))
- chore: test unit ([e70ed05](https://github.com/boxtoys/Aegis/commit/e70ed05))
- chore: the jsError plugin supports the vueCheck option to disable vue.js detection ([6f6cf6a](https://github.com/boxtoys/Aegis/commit/6f6cf6a))
- chore: the pageview plugin supports the vueCheck option to disable vue.js detection ([bd647c1](https://github.com/boxtoys/Aegis/commit/bd647c1))
- chore: the pageview plugin uses absolute paths by default ([cf11425](https://github.com/boxtoys/Aegis/commit/cf11425))
- chore: transfrom const to var ([c482163](https://github.com/boxtoys/Aegis/commit/c482163))
- chore: ts type ([b5e4561](https://github.com/boxtoys/Aegis/commit/b5e4561))
- chore: ts type ([75815f5](https://github.com/boxtoys/Aegis/commit/75815f5))
- chore: unit test ([f593227](https://github.com/boxtoys/Aegis/commit/f593227))
- chore: unit test for jsError ([eb6e94a](https://github.com/boxtoys/Aegis/commit/eb6e94a))
- chore: unit test for jsError ([a5d00f4](https://github.com/boxtoys/Aegis/commit/a5d00f4))
- chore: unit test for utils ([0fef341](https://github.com/boxtoys/Aegis/commit/0fef341))
- chore: update ([3732ce4](https://github.com/boxtoys/Aegis/commit/3732ce4))
- chore: update ([a8ad019](https://github.com/boxtoys/Aegis/commit/a8ad019))
- chore: update 更新 ([916e26f](https://github.com/boxtoys/Aegis/commit/916e26f))
- chore: update browserBuilder to include url in common object ([b0c7631](https://github.com/boxtoys/Aegis/commit/b0c7631))
- chore: update build script ([64419ee](https://github.com/boxtoys/Aegis/commit/64419ee))
- chore: update builder functions to support different platforms ([80b6bc1](https://github.com/boxtoys/Aegis/commit/80b6bc1))
- chore: update changelog ([14b29b3](https://github.com/boxtoys/Aegis/commit/14b29b3))
- chore: update changelog ([5307857](https://github.com/boxtoys/Aegis/commit/5307857))
- chore: update changelog and package.json ([c25efb6](https://github.com/boxtoys/Aegis/commit/c25efb6))
- chore: update CHANGELOG.md to remove outdated version entries and maintain clarity ([bb84016](https://github.com/boxtoys/Aegis/commit/bb84016))
- chore: update dependency management config, remove package-lock.json ([37804a6](https://github.com/boxtoys/Aegis/commit/37804a6))
- chore: update feature text ([79079c8](https://github.com/boxtoys/Aegis/commit/79079c8))
- chore: update import paths and add new files ([ab92584](https://github.com/boxtoys/Aegis/commit/ab92584))
- chore: update package name and description ([e5f4a3e](https://github.com/boxtoys/Aegis/commit/e5f4a3e))
- chore: update README.md with simplified release instructions ([7ff3203](https://github.com/boxtoys/Aegis/commit/7ff3203))
- chore: update staticResource ([db45555](https://github.com/boxtoys/Aegis/commit/db45555))
- chore: update staticResource ([05f92c3](https://github.com/boxtoys/Aegis/commit/05f92c3))
- chore: update staticResource ([ce3b303](https://github.com/boxtoys/Aegis/commit/ce3b303))
- chore: update staticResource ([65d9761](https://github.com/boxtoys/Aegis/commit/65d9761))
- chore: update staticResource ([610be48](https://github.com/boxtoys/Aegis/commit/610be48))
- chore: update staticResource ([6e02405](https://github.com/boxtoys/Aegis/commit/6e02405))
- chore: update terser configuration in rollup.config.mjs ([a673459](https://github.com/boxtoys/Aegis/commit/a673459))
- chore: update test ([037eb0b](https://github.com/boxtoys/Aegis/commit/037eb0b))
- chore: update ts lib ([15b2afa](https://github.com/boxtoys/Aegis/commit/15b2afa))
- chore: update version ([7e2f379](https://github.com/boxtoys/Aegis/commit/7e2f379))
- chore: update vitest ([99ae88e](https://github.com/boxtoys/Aegis/commit/99ae88e))
- chore: use .nvmrc ([7db24e4](https://github.com/boxtoys/Aegis/commit/7db24e4))
- chore: use BrowserTiming data ([2036628](https://github.com/boxtoys/Aegis/commit/2036628))
- chore: use clone response on fetch exception ([687281a](https://github.com/boxtoys/Aegis/commit/687281a))
- chore: use ES5 syntax ([9f97748](https://github.com/boxtoys/Aegis/commit/9f97748))
- chore: use terser plugin compress code ([7bc596a](https://github.com/boxtoys/Aegis/commit/7bc596a))
- chore: using the interface define AegisInstance ([4ee2f34](https://github.com/boxtoys/Aegis/commit/4ee2f34))
- chore: when using xhr, temporarily do not perform retry logic ([80a3794](https://github.com/boxtoys/Aegis/commit/80a3794))
- feat: 测试case ([9971456](https://github.com/boxtoys/Aegis/commit/9971456))
- feat: 插件 ([56684ea](https://github.com/boxtoys/Aegis/commit/56684ea))
- feat: 插件 ([c620b25](https://github.com/boxtoys/Aegis/commit/c620b25))
- feat: 插件 ([60d85f4](https://github.com/boxtoys/Aegis/commit/60d85f4))
- feat: 插件优化 ([8906729](https://github.com/boxtoys/Aegis/commit/8906729))
- feat: 代码优化 ([9059084](https://github.com/boxtoys/Aegis/commit/9059084))
- feat: 更新 ([85db80f](https://github.com/boxtoys/Aegis/commit/85db80f))
- feat: 更新 ([3ffba0c](https://github.com/boxtoys/Aegis/commit/3ffba0c))
- feat: 更新白屏插件 ([6e1f83a](https://github.com/boxtoys/Aegis/commit/6e1f83a))
- feat: 更新了不管是否存在骨架都进行二次回正检测 ([789500e](https://github.com/boxtoys/Aegis/commit/789500e))
- feat: 添加 **SPA_LOAD_READY_CALLBACK** 回调以支持 SPA 加载完成后的处理逻辑，并优化 onSPA_LOAD 函数中的脚本加载逻辑 ([5fee7cc](https://github.com/boxtoys/Aegis/commit/5fee7cc))
- feat: 完善测试用例 ([437d788](https://github.com/boxtoys/Aegis/commit/437d788))
- feat: 完善LCP测试用例 ([737cf7d](https://github.com/boxtoys/Aegis/commit/737cf7d))
- feat: 新增 README.md ([e6db215](https://github.com/boxtoys/Aegis/commit/e6db215))
- feat: 新增获取设备vendor model ([2b888e3](https://github.com/boxtoys/Aegis/commit/2b888e3))
- feat: 新增device插件 ([3be060b](https://github.com/boxtoys/Aegis/commit/3be060b))
- feat: 新增reourceError ([0d0e054](https://github.com/boxtoys/Aegis/commit/0d0e054))
- feat: 修改 ([0f88aa9](https://github.com/boxtoys/Aegis/commit/0f88aa9))
- feat: 修改transferSize 逻辑 ([3c07e94](https://github.com/boxtoys/Aegis/commit/3c07e94))
- feat: 优化 ([b78126e](https://github.com/boxtoys/Aegis/commit/b78126e))
- feat: 优化了路由监听, 函数命令等 ([ad1d70f](https://github.com/boxtoys/Aegis/commit/ad1d70f))
- feat: add action plugin ([a04de32](https://github.com/boxtoys/Aegis/commit/a04de32))
- feat: add all performance obsever ([cebd49c](https://github.com/boxtoys/Aegis/commit/cebd49c))
- feat: add blankScreen plugins ([6f49739](https://github.com/boxtoys/Aegis/commit/6f49739))
- feat: add bridge observe ([5cba719](https://github.com/boxtoys/Aegis/commit/5cba719))
- feat: add browser timing for spa load ([971f5ba](https://github.com/boxtoys/Aegis/commit/971f5ba))
- feat: add detect browser feature plugin ([2cb04bc](https://github.com/boxtoys/Aegis/commit/2cb04bc))
- feat: add endTime property to SPA load records and update duration calculations ([1eabc97](https://github.com/boxtoys/Aegis/commit/1eabc97))
- feat: add feature plugin doc file ([5e47487](https://github.com/boxtoys/Aegis/commit/5e47487))
- feat: add htmlTreeStringify utility function to staticResource.ts ([462cfd5](https://github.com/boxtoys/Aegis/commit/462cfd5))
- feat: add ignoreUrls in resource ([714e246](https://github.com/boxtoys/Aegis/commit/714e246))
- feat: add new upload ([067b06d](https://github.com/boxtoys/Aegis/commit/067b06d))
- feat: add pageview integration and test ([7093c1d](https://github.com/boxtoys/Aegis/commit/7093c1d))
- feat: add perf plugin ([c6508d4](https://github.com/boxtoys/Aegis/commit/c6508d4))
- feat: add script detect for spa load ([c163606](https://github.com/boxtoys/Aegis/commit/c163606))
- feat: add spa_load ([aada673](https://github.com/boxtoys/Aegis/commit/aada673))
- feat: add static reource file ([ee5450f](https://github.com/boxtoys/Aegis/commit/ee5450f))
- feat: add stop judge ([1935a70](https://github.com/boxtoys/Aegis/commit/1935a70))
- feat: add the immediate attribute to support sending immediately in batch mode ([c4a1a24](https://github.com/boxtoys/Aegis/commit/c4a1a24))
- feat: add TTI report ([894c875](https://github.com/boxtoys/Aegis/commit/894c875))
- feat: api插件测试用例 ([8ea26c2](https://github.com/boxtoys/Aegis/commit/8ea26c2))
- feat: bridge http update ([a336660](https://github.com/boxtoys/Aegis/commit/a336660))
- feat: bridge初始化 ([efdfa47](https://github.com/boxtoys/Aegis/commit/efdfa47))
- feat: chore: update npm script to deploy documentation ([c10a9f3](https://github.com/boxtoys/Aegis/commit/c10a9f3))
- feat: clear ([46b1d73](https://github.com/boxtoys/Aegis/commit/46b1d73))
- feat: config manager completed ([21ba497](https://github.com/boxtoys/Aegis/commit/21ba497))
- feat: document listener AEJSBridgeReady ([e91846e](https://github.com/boxtoys/Aegis/commit/e91846e))
- feat: fix getElementUrl function for SVGAElement ([da70d23](https://github.com/boxtoys/Aegis/commit/da70d23))
- feat: http plugin support customErrorReportPolicy option ([5e7320d](https://github.com/boxtoys/Aegis/commit/5e7320d))
- feat: http测试用例 ([4f20b9c](https://github.com/boxtoys/Aegis/commit/4f20b9c))
- feat: http插件优化 ([bfc0117](https://github.com/boxtoys/Aegis/commit/bfc0117))
- feat: http修改 ([858049f](https://github.com/boxtoys/Aegis/commit/858049f))
- feat: ignore base64url request report ([7b74d85](https://github.com/boxtoys/Aegis/commit/7b74d85))
- feat: implement return value chaining for event handlers ([3e8dc02](https://github.com/boxtoys/Aegis/commit/3e8dc02))
- feat: improve ([1e954de](https://github.com/boxtoys/Aegis/commit/1e954de))
- feat: improve ([48c6343](https://github.com/boxtoys/Aegis/commit/48c6343))
- feat: improve ([5fd641d](https://github.com/boxtoys/Aegis/commit/5fd641d))
- feat: improve ([e2a0de3](https://github.com/boxtoys/Aegis/commit/e2a0de3))
- feat: improve ([7f342c9](https://github.com/boxtoys/Aegis/commit/7f342c9))
- feat: improve ([82549f6](https://github.com/boxtoys/Aegis/commit/82549f6))
- feat: improve ([f3af43d](https://github.com/boxtoys/Aegis/commit/f3af43d))
- feat: improve BrowserTime field ([2603e3d](https://github.com/boxtoys/Aegis/commit/2603e3d))
- feat: improve CLS test case ([c25b04e](https://github.com/boxtoys/Aegis/commit/c25b04e))
- feat: improve docs ([168e11e](https://github.com/boxtoys/Aegis/commit/168e11e))
- feat: improve esm and cjs build ([0e3f432](https://github.com/boxtoys/Aegis/commit/0e3f432))
- feat: improve initial script detect ([7beb80c](https://github.com/boxtoys/Aegis/commit/7beb80c))
- feat: improve node display none case ([e1b59d1](https://github.com/boxtoys/Aegis/commit/e1b59d1))
- feat: improve perf docs ([6af9cb5](https://github.com/boxtoys/Aegis/commit/6af9cb5))
- feat: improve performance docs ([ea5748c](https://github.com/boxtoys/Aegis/commit/ea5748c))
- feat: improve performance init timing ([0efe026](https://github.com/boxtoys/Aegis/commit/0efe026))
- feat: improve performance plugin, simplify perf entry ([aabd73c](https://github.com/boxtoys/Aegis/commit/aabd73c))
- feat: improve spa load ([603c0a9](https://github.com/boxtoys/Aegis/commit/603c0a9))
- feat: improve spa load ([3cfc22a](https://github.com/boxtoys/Aegis/commit/3cfc22a))
- feat: improve spa load ([21f9d3d](https://github.com/boxtoys/Aegis/commit/21f9d3d))
- feat: improve spa_load ([614600b](https://github.com/boxtoys/Aegis/commit/614600b))
- feat: improve SPA_LOAD ([47f6144](https://github.com/boxtoys/Aegis/commit/47f6144))
- feat: improve spaload crossorigin link compatibility ([3e39370](https://github.com/boxtoys/Aegis/commit/3e39370))
- feat: improve test case ([3763e39](https://github.com/boxtoys/Aegis/commit/3763e39))
- feat: improve test case ([a53e63a](https://github.com/boxtoys/Aegis/commit/a53e63a))
- feat: improve test case(add bridge request case) ([fdaed85](https://github.com/boxtoys/Aegis/commit/fdaed85))
- feat: improve tests ([ca2c669](https://github.com/boxtoys/Aegis/commit/ca2c669))
- feat: merge file cli \& upload cdn ([e379705](https://github.com/boxtoys/Aegis/commit/e379705))
- feat: modify spaload check gap, change gap from 100ms to 300ms ([f5479d1](https://github.com/boxtoys/Aegis/commit/f5479d1))
- feat: plugin interface completed ([2d813ca](https://github.com/boxtoys/Aegis/commit/2d813ca))
- feat: reduce per entry ([9c78513](https://github.com/boxtoys/Aegis/commit/9c78513))
- feat: refactor blankScreen integration and fix test case ([851f8cc](https://github.com/boxtoys/Aegis/commit/851f8cc))
- feat: remove console ([0919a9b](https://github.com/boxtoys/Aegis/commit/0919a9b))
- feat: remove spaLoad ios judge ([e1f9e29](https://github.com/boxtoys/Aegis/commit/e1f9e29))
- feat: remove unused code ([20a6bb9](https://github.com/boxtoys/Aegis/commit/20a6bb9))
- feat: remove unused field from TTI ([a88330a](https://github.com/boxtoys/Aegis/commit/a88330a))
- feat: request测试用例 ([9ffa78e](https://github.com/boxtoys/Aegis/commit/9ffa78e))
- feat: request插件 ([c13437c](https://github.com/boxtoys/Aegis/commit/c13437c))
- feat: request插件 ([5f06908](https://github.com/boxtoys/Aegis/commit/5f06908))
- feat: request插件 ([3d309bf](https://github.com/boxtoys/Aegis/commit/3d309bf))
- feat: resource error plugin done ([e1cbae6](https://github.com/boxtoys/Aegis/commit/e1cbae6))
- feat: revert old iife config ([fe3c65a](https://github.com/boxtoys/Aegis/commit/fe3c65a))
- feat: simpilfy LCP entry ([9703415](https://github.com/boxtoys/Aegis/commit/9703415))
- feat: skip blankScreen test and add new tests for Aegis staticResource integration ([c3a8ea5](https://github.com/boxtoys/Aegis/commit/c3a8ea5))
- feat: spa_load add records params ([a2428f0](https://github.com/boxtoys/Aegis/commit/a2428f0))
- feat: stack-parser ([a0844d4](https://github.com/boxtoys/Aegis/commit/a0844d4))
- feat: stash code ([7c5e50f](https://github.com/boxtoys/Aegis/commit/7c5e50f))
- feat: stash code ([65f3d6a](https://github.com/boxtoys/Aegis/commit/65f3d6a))
- feat: stash code ([04db9b6](https://github.com/boxtoys/Aegis/commit/04db9b6))
- feat: suppor largePictureInspect plugin ([0bf8d0b](https://github.com/boxtoys/Aegis/commit/0bf8d0b))
- feat: support CJS \& IIFE bundle ([22223f7](https://github.com/boxtoys/Aegis/commit/22223f7))
- feat: support custom sender ([2211cdb](https://github.com/boxtoys/Aegis/commit/2211cdb))
- feat: support pre collect ([d853810](https://github.com/boxtoys/Aegis/commit/d853810))
- feat: support report sdk error ([832ec1e](https://github.com/boxtoys/Aegis/commit/832ec1e))
- feat: support transports options ([4dc2479](https://github.com/boxtoys/Aegis/commit/4dc2479))
- feat: test unit ([47118e5](https://github.com/boxtoys/Aegis/commit/47118e5))
- feat: update ([409e131](https://github.com/boxtoys/Aegis/commit/409e131))
- feat: update device components ([f376626](https://github.com/boxtoys/Aegis/commit/f376626))
- feat: update spa_load ([b09fad5](https://github.com/boxtoys/Aegis/commit/b09fad5))
- feat: update spa_load and test cases ([bc7af77](https://github.com/boxtoys/Aegis/commit/bc7af77))
- feat(blog): 服务端架构文档0.1 ([209ce2d](https://github.com/boxtoys/Aegis/commit/209ce2d))
- feat(doc): 修改服务端文档内容 ([0d10aa9](https://github.com/boxtoys/Aegis/commit/0d10aa9))
- docs: 更新性能插件文档，添加 SPA_LOAD 指标及其配置选项 ([98c1c6d](https://github.com/boxtoys/Aegis/commit/98c1c6d))
- docs: add a non-proxy xhr onerror comment ([9101b68](https://github.com/boxtoys/Aegis/commit/9101b68))
- docs: add action.md doc ([04298e0](https://github.com/boxtoys/Aegis/commit/04298e0))
- docs: add api plugin doc ([047a657](https://github.com/boxtoys/Aegis/commit/047a657))
- docs: add BlankScreen and Resource Error and pulgiin doc ([5932459](https://github.com/boxtoys/Aegis/commit/5932459))
- docs: add bridge plugin doc ([9b8d0b1](https://github.com/boxtoys/Aegis/commit/9b8d0b1))
- docs: add comments ([232cbf4](https://github.com/boxtoys/Aegis/commit/232cbf4))
- docs: add Console plugin doc ([29dcd63](https://github.com/boxtoys/Aegis/commit/29dcd63))
- docs: add Convention doc ([290a76e](https://github.com/boxtoys/Aegis/commit/290a76e))
- docs: add data structure doc ([709b120](https://github.com/boxtoys/Aegis/commit/709b120))
- docs: add design doc ([fb5aa17](https://github.com/boxtoys/Aegis/commit/fb5aa17))
- docs: add detect vue router comments ([f0a37e7](https://github.com/boxtoys/Aegis/commit/f0a37e7))
- docs: add Device plugin doc ([ac9e42a](https://github.com/boxtoys/Aegis/commit/ac9e42a))
- docs: add documentation for the largePictureInspect plugin ([add4fc6](https://github.com/boxtoys/Aegis/commit/add4fc6))
- docs: add example ([99a0410](https://github.com/boxtoys/Aegis/commit/99a0410))
- docs: add HTTP plugin doc ([77b764a](https://github.com/boxtoys/Aegis/commit/77b764a))
- docs: add intro \& quick-start docs ([2e277c9](https://github.com/boxtoys/Aegis/commit/2e277c9))
- docs: add PageView plugin doc ([f0fa695](https://github.com/boxtoys/Aegis/commit/f0fa695))
- docs: add Pixel plugin doc ([c4aafeb](https://github.com/boxtoys/Aegis/commit/c4aafeb))
- docs: add plugin doc ([376387d](https://github.com/boxtoys/Aegis/commit/376387d))
- docs: add Precollect plugin doc ([3250e58](https://github.com/boxtoys/Aegis/commit/3250e58))
- docs: add preload example ([6b0178b](https://github.com/boxtoys/Aegis/commit/6b0178b))
- docs: add release step ([0d01bfc](https://github.com/boxtoys/Aegis/commit/0d01bfc))
- docs: add response clone comment ([c2858ef](https://github.com/boxtoys/Aegis/commit/c2858ef))
- docs: add sdk config docs ([67abe36](https://github.com/boxtoys/Aegis/commit/67abe36))
- docs: add type definition ([6442207](https://github.com/boxtoys/Aegis/commit/6442207))
- docs: delete comments ([9b9210f](https://github.com/boxtoys/Aegis/commit/9b9210f))
- docs: deploy ([ae2829a](https://github.com/boxtoys/Aegis/commit/ae2829a))
- docs: example enable gzip compress ([48cc6eb](https://github.com/boxtoys/Aegis/commit/48cc6eb))
- docs: example support combo static assets ([ee3025d](https://github.com/boxtoys/Aegis/commit/ee3025d))
- docs: homepage ui ([65c2d82](https://github.com/boxtoys/Aegis/commit/65c2d82))
- docs: init vitepress ([b8e1eb8](https://github.com/boxtoys/Aegis/commit/b8e1eb8))
- docs: jsError ([da97b45](https://github.com/boxtoys/Aegis/commit/da97b45))
- docs: jsError ([a9906e3](https://github.com/boxtoys/Aegis/commit/a9906e3))
- docs: optimize docs style ([66358e2](https://github.com/boxtoys/Aegis/commit/66358e2))
- docs: optimize docs style ([e2256f1](https://github.com/boxtoys/Aegis/commit/e2256f1))
- docs: pageview \& jsError plugin docs add vueCheck option description ([a85f744](https://github.com/boxtoys/Aegis/commit/a85f744))
- docs: publish ([d728a86](https://github.com/boxtoys/Aegis/commit/d728a86))
- docs: review ([16ea99e](https://github.com/boxtoys/Aegis/commit/16ea99e))
- docs: review ([01ddba9](https://github.com/boxtoys/Aegis/commit/01ddba9))
- docs: review ([b8417c2](https://github.com/boxtoys/Aegis/commit/b8417c2))
- docs: review ([cc1b487](https://github.com/boxtoys/Aegis/commit/cc1b487))
- docs: sourcemap upload ([f097041](https://github.com/boxtoys/Aegis/commit/f097041))
- docs: sync of document with code ([8bfe5d8](https://github.com/boxtoys/Aegis/commit/8bfe5d8))
- docs: update documentation links in README.md ([5b2d440](https://github.com/boxtoys/Aegis/commit/5b2d440))
- docs: update documentation links in README.md ([7f1165f](https://github.com/boxtoys/Aegis/commit/7f1165f))
- docs: update http document ([166366a](https://github.com/boxtoys/Aegis/commit/166366a))
- docs: update pageview plugin document ([ad59be3](https://github.com/boxtoys/Aegis/commit/ad59be3))
- docs: update README.md with simplified release instructions ([90d38ef](https://github.com/boxtoys/Aegis/commit/90d38ef))
- docs: update: resourceErro.md ([38a10cb](https://github.com/boxtoys/Aegis/commit/38a10cb))
- test: add 'correct request, but response.text() exception' cases ([564040e](https://github.com/boxtoys/Aegis/commit/564040e))
- test: add Aegis test cases ([6dd6cdb](https://github.com/boxtoys/Aegis/commit/6dd6cdb))
- test: add bridge test cases ([26a4097](https://github.com/boxtoys/Aegis/commit/26a4097))
- test: add cases ([4588890](https://github.com/boxtoys/Aegis/commit/4588890))
- test: add cases ([56eed8f](https://github.com/boxtoys/Aegis/commit/56eed8f))
- test: add ConfigManager test cases ([9489386](https://github.com/boxtoys/Aegis/commit/9489386))
- test: add DOMException case ([c1b354a](https://github.com/boxtoys/Aegis/commit/c1b354a))
- test: add error cases ([1db964a](https://github.com/boxtoys/Aegis/commit/1db964a))
- test: add http test cases ([a7da4b3](https://github.com/boxtoys/Aegis/commit/a7da4b3))
- test: add integrations tearDown case ([0c9786f](https://github.com/boxtoys/Aegis/commit/0c9786f))
- test: add istanbul ignore comment ([09bb2ae](https://github.com/boxtoys/Aegis/commit/09bb2ae))
- test: add loader case ([17f459b](https://github.com/boxtoys/Aegis/commit/17f459b))
- test: add multiple aegis case ([0b87389](https://github.com/boxtoys/Aegis/commit/0b87389))
- test: add multiple instance case ([0bcd030](https://github.com/boxtoys/Aegis/commit/0bcd030))
- test: add mutiple aegis instance cases ([381b06e](https://github.com/boxtoys/Aegis/commit/381b06e))
- test: add Object.assign polyfill test case ([76b1d47](https://github.com/boxtoys/Aegis/commit/76b1d47))
- test: add pageview test cases ([d7d653e](https://github.com/boxtoys/Aegis/commit/d7d653e))
- test: add pageview test cases for spy calls and assertions ([fa4f334](https://github.com/boxtoys/Aegis/commit/fa4f334))
- test: add promise error is custom event sence ([9523714](https://github.com/boxtoys/Aegis/commit/9523714))
- test: add request test cases ([802c497](https://github.com/boxtoys/Aegis/commit/802c497))
- test: add server mock for unit testing ([01934ea](https://github.com/boxtoys/Aegis/commit/01934ea))
- test: add storage test cases ([ad1166f](https://github.com/boxtoys/Aegis/commit/ad1166f))
- test: add test cases ([f97d919](https://github.com/boxtoys/Aegis/commit/f97d919))
- test: add transports example ([86c003f](https://github.com/boxtoys/Aegis/commit/86c003f))
- test: add unit tests for utils and core classes ([9a6fdfc](https://github.com/boxtoys/Aegis/commit/9a6fdfc))
- test: add xhr test cases ([ce01cb2](https://github.com/boxtoys/Aegis/commit/ce01cb2))
- test: change case assets ([c150ecc](https://github.com/boxtoys/Aegis/commit/c150ecc))
- test: change example code ([e4681b4](https://github.com/boxtoys/Aegis/commit/e4681b4))
- test: cleanup logs ([57e2839](https://github.com/boxtoys/Aegis/commit/57e2839))
- test: completing test cases ([5652a17](https://github.com/boxtoys/Aegis/commit/5652a17))
- test: fix formatting of noop function in utils.test.ts ([69a175f](https://github.com/boxtoys/Aegis/commit/69a175f))
- test: fix test case writing ([3c9dc9e](https://github.com/boxtoys/Aegis/commit/3c9dc9e))
- test: hide the console error log ([69a023a](https://github.com/boxtoys/Aegis/commit/69a023a))
- test: hide vitest unhandled error info ([1e5102a](https://github.com/boxtoys/Aegis/commit/1e5102a))
- test: optimize ([d1d3312](https://github.com/boxtoys/Aegis/commit/d1d3312))
- test: organize the directory structure ([bab4a12](https://github.com/boxtoys/Aegis/commit/bab4a12))
- test: remove sinon mock package ([c8a2356](https://github.com/boxtoys/Aegis/commit/c8a2356))
- test: remove useless code ([fbf164b](https://github.com/boxtoys/Aegis/commit/fbf164b))
- test: test cases split ([c37dff3](https://github.com/boxtoys/Aegis/commit/c37dff3))
- test: update package.json with new test and mock scripts ([038b077](https://github.com/boxtoys/Aegis/commit/038b077))
- test: update test cases for issues caused by vitest upgrade ([623a170](https://github.com/boxtoys/Aegis/commit/623a170))
- test: update test script and server shutdown handling ([cd784fa](https://github.com/boxtoys/Aegis/commit/cd784fa))
- test: vitest can't test unhandledrejection scenario ([6f48fc8](https://github.com/boxtoys/Aegis/commit/6f48fc8))
- perf: code optimize ([c12701f](https://github.com/boxtoys/Aegis/commit/c12701f))
- perf: compressing the built code ([d18cd87](https://github.com/boxtoys/Aegis/commit/d18cd87))
- revert: code optimize ([ccc3da1](https://github.com/boxtoys/Aegis/commit/ccc3da1))
- revert: organize the directory structure ([8b756d6](https://github.com/boxtoys/Aegis/commit/8b756d6))
- style: format code ([5266ce7](https://github.com/boxtoys/Aegis/commit/5266ce7))

## <small>1.0.69 (2025-08-08)</small>

- refactor: remove runtime esModule detect, use static method and noModule property to detect ([8b483ab](https://github.com/boxtoys/Aegis/commit/8b483ab))

## <small>1.0.68 (2025-08-07)</small>

- chore: 1.0.0 ([7c85a4c](https://github.com/boxtoys/Aegis/commit/7c85a4c))
- chore: 发布SDK ([a503481](https://github.com/boxtoys/Aegis/commit/a503481))
- chore: 更换目录结构 ([5661e7b](https://github.com/boxtoys/Aegis/commit/5661e7b))
- chore: 锁定ts版本 ([2cef86f](https://github.com/boxtoys/Aegis/commit/2cef86f))
- chore: 修改注释 ([3727b85](https://github.com/boxtoys/Aegis/commit/3727b85))
- chore: add a packaging process to the largePictureInspect plugin ([ea769c4](https://github.com/boxtoys/Aegis/commit/ea769c4))
- chore: add action plugin ([f9b9c3b](https://github.com/boxtoys/Aegis/commit/f9b9c3b))
- chore: add action plugin build process ([3c539ec](https://github.com/boxtoys/Aegis/commit/3c539ec))
- chore: add ajax request ([64551a2](https://github.com/boxtoys/Aegis/commit/64551a2))
- chore: add aop function ([bd2dc4f](https://github.com/boxtoys/Aegis/commit/bd2dc4f))
- chore: add aop use case scenarios ([974d621](https://github.com/boxtoys/Aegis/commit/974d621))
- chore: add assign polyfill ([83940a1](https://github.com/boxtoys/Aegis/commit/83940a1))
- chore: add blank screen detection test with document complete ([6e5041c](https://github.com/boxtoys/Aegis/commit/6e5041c))
- chore: add builder doc ([9a2747b](https://github.com/boxtoys/Aegis/commit/9a2747b))
- chore: add comments ([dbd8a29](https://github.com/boxtoys/Aegis/commit/dbd8a29))
- chore: add comments to magic code ([1d14ddc](https://github.com/boxtoys/Aegis/commit/1d14ddc))
- chore: add common parameters ([1388f18](https://github.com/boxtoys/Aegis/commit/1388f18))
- chore: add config constants and update configManager ([9385f1e](https://github.com/boxtoys/Aegis/commit/9385f1e))
- chore: add cssSupports polyfill ([3d774b9](https://github.com/boxtoys/Aegis/commit/3d774b9))
- chore: add cursorrules ([7c09d52](https://github.com/boxtoys/Aegis/commit/7c09d52))
- chore: add custom builder function for data processing ([e358b55](https://github.com/boxtoys/Aegis/commit/e358b55))
- chore: add dom observer method ([5c6c835](https://github.com/boxtoys/Aegis/commit/5c6c835))
- chore: add encrypted env file ([ad288cf](https://github.com/boxtoys/Aegis/commit/ad288cf))
- chore: add error handling for integrations setup in Aegis ([87094cb](https://github.com/boxtoys/Aegis/commit/87094cb))
- chore: add feature plugin packing process ([8f5fc9d](https://github.com/boxtoys/Aegis/commit/8f5fc9d))
- chore: add feature polyfill ([2bdfaab](https://github.com/boxtoys/Aegis/commit/2bdfaab))
- chore: add functions for handling static resource errors and reporting performance data ([48661a3](https://github.com/boxtoys/Aegis/commit/48661a3))
- chore: add generic field: viewId ([ab1d28f](https://github.com/boxtoys/Aegis/commit/ab1d28f))
- chore: add global variable: BrowserTiming ([6ed4cb7](https://github.com/boxtoys/Aegis/commit/6ed4cb7))
- chore: add htmlTreeAsString util ([61f3472](https://github.com/boxtoys/Aegis/commit/61f3472))
- chore: add http \& bridge plugin build process ([c6ff1f3](https://github.com/boxtoys/Aegis/commit/c6ff1f3))
- chore: add Integrations interface ([cb01e67](https://github.com/boxtoys/Aegis/commit/cb01e67))
- chore: add isNode env check ([b18b1c6](https://github.com/boxtoys/Aegis/commit/b18b1c6))
- chore: add lifecycle hooks ([a91bf57](https://github.com/boxtoys/Aegis/commit/a91bf57))
- chore: add loader stub methods ([a3e1030](https://github.com/boxtoys/Aegis/commit/a3e1030))
- chore: add multiple instance manage ([1281484](https://github.com/boxtoys/Aegis/commit/1281484))
- chore: add network type common field ([fb8c8ef](https://github.com/boxtoys/Aegis/commit/fb8c8ef))
- chore: add perf plugin option ([1713b0a](https://github.com/boxtoys/Aegis/commit/1713b0a))
- chore: add platform judgment code ([70b9c3a](https://github.com/boxtoys/Aegis/commit/70b9c3a))
- chore: add precollect test cases \& build precollect iife plugin ([a1dac62](https://github.com/boxtoys/Aegis/commit/a1dac62))
- chore: add preload logic ([7569367](https://github.com/boxtoys/Aegis/commit/7569367))
- chore: add resource \& resourceError plugin build policy ([f94fcfc](https://github.com/boxtoys/Aegis/commit/f94fcfc))
- chore: add sample rate logic ([5c0b7a3](https://github.com/boxtoys/Aegis/commit/5c0b7a3))
- chore: add sid common field ([cab0d59](https://github.com/boxtoys/Aegis/commit/cab0d59))
- chore: add store fallback ([4e93263](https://github.com/boxtoys/Aegis/commit/4e93263))
- chore: add test cases ([ceceda6](https://github.com/boxtoys/Aegis/commit/ceceda6))
- chore: add toArray polyfill ([97a86f1](https://github.com/boxtoys/Aegis/commit/97a86f1))
- chore: add transport attribute check ([3a70a06](https://github.com/boxtoys/Aegis/commit/3a70a06))
- chore: add types ([2182058](https://github.com/boxtoys/Aegis/commit/2182058))
- chore: add utils ([0164509](https://github.com/boxtoys/Aegis/commit/0164509))
- chore: addition of the history non-existence treatment case ([db329a9](https://github.com/boxtoys/Aegis/commit/db329a9))
- chore: adjust changelog ([5135517](https://github.com/boxtoys/Aegis/commit/5135517))
- chore: adjust changelog ([c05631e](https://github.com/boxtoys/Aegis/commit/c05631e))
- chore: adjust iife compilation result ([7673771](https://github.com/boxtoys/Aegis/commit/7673771))
- chore: aegis core add reportUrl field ([cdabfba](https://github.com/boxtoys/Aegis/commit/cdabfba))
- chore: aegis destroy remove all event ([94e458d](https://github.com/boxtoys/Aegis/commit/94e458d))
- chore: aop method add native method check logic ([058d988](https://github.com/boxtoys/Aegis/commit/058d988))
- chore: aop support restore callback & add resolveURL method ([a9fd72f](https://github.com/boxtoys/Aegis/commit/a9fd72f))
- chore: api plugin payload add baseUrl \& queryString params ([80eeb54](https://github.com/boxtoys/Aegis/commit/80eeb54))
- chore: api plugin payload data add ok field ([68a698f](https://github.com/boxtoys/Aegis/commit/68a698f))
- chore: blankScreen plugin support ignoreUrls parameter ([d89275f](https://github.com/boxtoys/Aegis/commit/d89275f))
- chore: bridge plugin support ignoreUrls parameter ([90e9f50](https://github.com/boxtoys/Aegis/commit/90e9f50))
- chore: browser common add os common field ([891e950](https://github.com/boxtoys/Aegis/commit/891e950))
- chore: build cjs \& esm code ([9073f09](https://github.com/boxtoys/Aegis/commit/9073f09))
- chore: build iife code ([cbeb407](https://github.com/boxtoys/Aegis/commit/cbeb407))
- chore: change browser name ([9b0d21d](https://github.com/boxtoys/Aegis/commit/9b0d21d))
- chore: change browserslist rules ([a17f2cd](https://github.com/boxtoys/Aegis/commit/a17f2cd))
- chore: change custom report payload options ([7f1b004](https://github.com/boxtoys/Aegis/commit/7f1b004))
- chore: change customErrorReportPolicy name to reportPolicy name ([555dd2b](https://github.com/boxtoys/Aegis/commit/555dd2b))
- chore: change env ([2c4a66d](https://github.com/boxtoys/Aegis/commit/2c4a66d))
- chore: change http integration name to api ([be2b84e](https://github.com/boxtoys/Aegis/commit/be2b84e))
- chore: change Integration declare ([fa4ea05](https://github.com/boxtoys/Aegis/commit/fa4ea05))
- chore: change on & off return type ([c9818a2](https://github.com/boxtoys/Aegis/commit/c9818a2))
- chore: change precollect report sri data ([16d05da](https://github.com/boxtoys/Aegis/commit/16d05da))
- chore: change SDK name ([2ecab51](https://github.com/boxtoys/Aegis/commit/2ecab51))
- chore: change test domain ([42a2a8e](https://github.com/boxtoys/Aegis/commit/42a2a8e))
- chore: change type define ([9d35e5a](https://github.com/boxtoys/Aegis/commit/9d35e5a))
- chore: change type name ([a7aff35](https://github.com/boxtoys/Aegis/commit/a7aff35))
- chore: changelog ([1e4d595](https://github.com/boxtoys/Aegis/commit/1e4d595))
- chore: cid depends on pageview pid ([76500d9](https://github.com/boxtoys/Aegis/commit/76500d9))
- chore: clean istanbul ignore comment ([68ac754](https://github.com/boxtoys/Aegis/commit/68ac754))
- chore: clean istanbul ignore comment ([da2ce72](https://github.com/boxtoys/Aegis/commit/da2ce72))
- chore: clean istanbul ignore comment ([b6ed12b](https://github.com/boxtoys/Aegis/commit/b6ed12b))
- chore: clean istanbul ignore comment ([c1caf28](https://github.com/boxtoys/Aegis/commit/c1caf28))
- chore: clean istanbul ignore comment ([1555412](https://github.com/boxtoys/Aegis/commit/1555412))
- chore: clean istanbul ignore comment ([43031ac](https://github.com/boxtoys/Aegis/commit/43031ac))
- chore: clear changelog ([56b9d3d](https://github.com/boxtoys/Aegis/commit/56b9d3d))
- chore: clear code comments ([5401868](https://github.com/boxtoys/Aegis/commit/5401868))
- chore: code clean ([cb1080f](https://github.com/boxtoys/Aegis/commit/cb1080f))
- chore: code format ([eb56558](https://github.com/boxtoys/Aegis/commit/eb56558))
- chore: code optimize ([c1a1087](https://github.com/boxtoys/Aegis/commit/c1a1087))
- chore: code optimize ([3d379cf](https://github.com/boxtoys/Aegis/commit/3d379cf))
- chore: code optimize ([6350963](https://github.com/boxtoys/Aegis/commit/6350963))
- chore: code optimize ([cffc518](https://github.com/boxtoys/Aegis/commit/cffc518))
- chore: code optimize ([bb84208](https://github.com/boxtoys/Aegis/commit/bb84208))
- chore: code optimize ([93188fd](https://github.com/boxtoys/Aegis/commit/93188fd))
- chore: code revert ([9e67686](https://github.com/boxtoys/Aegis/commit/9e67686))
- chore: code review ([146e832](https://github.com/boxtoys/Aegis/commit/146e832))
- chore: code rollback ([d8292e9](https://github.com/boxtoys/Aegis/commit/d8292e9))
- chore: code styling ([8bf5096](https://github.com/boxtoys/Aegis/commit/8bf5096))
- chore: combine multiple test reports ([face121](https://github.com/boxtoys/Aegis/commit/face121))
- chore: combo build add local process ([d561060](https://github.com/boxtoys/Aegis/commit/d561060))
- chore: commented out unused code in blankScreen.spec.ts ([1387311](https://github.com/boxtoys/Aegis/commit/1387311))
- chore: compatibility handling ([97f10d1](https://github.com/boxtoys/Aegis/commit/97f10d1))
- chore: config the changelog ([be4441f](https://github.com/boxtoys/Aegis/commit/be4441f))
- chore: config the device plugin packaging policy ([321cc76](https://github.com/boxtoys/Aegis/commit/321cc76))
- chore: configManager add addTransport \& removeTransport method ([a9b7b37](https://github.com/boxtoys/Aegis/commit/a9b7b37))
- chore: configManager becomes private ([f44f555](https://github.com/boxtoys/Aegis/commit/f44f555))
- chore: cover lines ([b275087](https://github.com/boxtoys/Aegis/commit/b275087))
- chore: cover uitls ([4948e49](https://github.com/boxtoys/Aegis/commit/4948e49))
- chore: delete precollect ts-ignore comment ([f258883](https://github.com/boxtoys/Aegis/commit/f258883))
- chore: device plugin add custom infomation extract func ([ea85620](https://github.com/boxtoys/Aegis/commit/ea85620))
- chore: feature plugin support detect image strategy ([2d51260](https://github.com/boxtoys/Aegis/commit/2d51260))
- chore: feature plugin support remote feature list ([da021e7](https://github.com/boxtoys/Aegis/commit/da021e7))
- chore: format code ([659ae01](https://github.com/boxtoys/Aegis/commit/659ae01))
- chore: format code ([569ccc6](https://github.com/boxtoys/Aegis/commit/569ccc6))
- chore: format doc ([60826c0](https://github.com/boxtoys/Aegis/commit/60826c0))
- chore: from single dom to mutilple dom ([e7f7d97](https://github.com/boxtoys/Aegis/commit/e7f7d97))
- chore: ignore other lockfile ([3eb309e](https://github.com/boxtoys/Aegis/commit/3eb309e))
- chore: ignore types format ([21a6178](https://github.com/boxtoys/Aegis/commit/21a6178))
- chore: iife package change directory ([8309966](https://github.com/boxtoys/Aegis/commit/8309966))
- chore: improve deploy script ([94fa145](https://github.com/boxtoys/Aegis/commit/94fa145))
- chore: improve test config ([8f76e83](https://github.com/boxtoys/Aegis/commit/8f76e83))
- chore: improve types ([b4b4df6](https://github.com/boxtoys/Aegis/commit/b4b4df6))
- chore: improve types ([f0b6911](https://github.com/boxtoys/Aegis/commit/f0b6911))
- chore: init ([c2e6838](https://github.com/boxtoys/Aegis/commit/c2e6838))
- chore: init docs ([b1fc304](https://github.com/boxtoys/Aegis/commit/b1fc304))
- chore: initialization of the project ([e489687](https://github.com/boxtoys/Aegis/commit/e489687))
- chore: jsError build config ([311c033](https://github.com/boxtoys/Aegis/commit/311c033))
- chore: largePictureInspect plugin support dpr \& reportMode options ([25f7186](https://github.com/boxtoys/Aegis/commit/25f7186))
- chore: merge ([ec8b3ff](https://github.com/boxtoys/Aegis/commit/ec8b3ff))
- chore: modify build script ([bcb7336](https://github.com/boxtoys/Aegis/commit/bcb7336))
- chore: modify iife config, remove minify ([ec7d38c](https://github.com/boxtoys/Aegis/commit/ec7d38c))
- chore: on & off callback add instance type ([4bed77b](https://github.com/boxtoys/Aegis/commit/4bed77b))
- chore: optimize code ([7c5874e](https://github.com/boxtoys/Aegis/commit/7c5874e))
- chore: optimize combo script ([1b5d40d](https://github.com/boxtoys/Aegis/commit/1b5d40d))
- chore: optimize mock plugin error display ([d5fb83a](https://github.com/boxtoys/Aegis/commit/d5fb83a))
- chore: optimize script commands ([184d067](https://github.com/boxtoys/Aegis/commit/184d067))
- chore: page unload use visibilitychange event ([987e6c5](https://github.com/boxtoys/Aegis/commit/987e6c5))
- chore: pageview add source field ([1547ab1](https://github.com/boxtoys/Aegis/commit/1547ab1))
- chore: pageview plugin add detect vue-router ([c8c6634](https://github.com/boxtoys/Aegis/commit/c8c6634))
- chore: pageview plugin add initSend option ([46557ea](https://github.com/boxtoys/Aegis/commit/46557ea))
- chore: pageview support custom pageid extractor ([155c72e](https://github.com/boxtoys/Aegis/commit/155c72e))
- chore: plugin not found will output an error ([aae57f2](https://github.com/boxtoys/Aegis/commit/aae57f2))
- chore: preloadCode build process done ([3346141](https://github.com/boxtoys/Aegis/commit/3346141))
- chore: preset static methods to prevent errors when source not loaded ([3c4b5ed](https://github.com/boxtoys/Aegis/commit/3c4b5ed))
- chore: preset transport static methods to prevent errors when source not loaded ([32c609e](https://github.com/boxtoys/Aegis/commit/32c609e))
- chore: processing of SVGElement ([aa83dcf](https://github.com/boxtoys/Aegis/commit/aa83dcf))
- chore: publish ([1337d53](https://github.com/boxtoys/Aegis/commit/1337d53))
- chore: publish ([c9af2fd](https://github.com/boxtoys/Aegis/commit/c9af2fd))
- chore: publish cdn ([5513cd7](https://github.com/boxtoys/Aegis/commit/5513cd7))
- chore: publish cdn files ([25f6df5](https://github.com/boxtoys/Aegis/commit/25f6df5))
- chore: publish CDN files ([af69837](https://github.com/boxtoys/Aegis/commit/af69837))
- chore: publish CDN files ([b6dd0a1](https://github.com/boxtoys/Aegis/commit/b6dd0a1))
- chore: publish CDN files ([2e2f44d](https://github.com/boxtoys/Aegis/commit/2e2f44d))
- chore: publish cdn package ([679409c](https://github.com/boxtoys/Aegis/commit/679409c))
- chore: publish iife files to CDN ([8a46639](https://github.com/boxtoys/Aegis/commit/8a46639))
- chore: publish sdk ([0482127](https://github.com/boxtoys/Aegis/commit/0482127))
- chore: pv plugin add syncCid option & change docs ([a0fbdc8](https://github.com/boxtoys/Aegis/commit/a0fbdc8))
- chore: refactor blank screen setup function ([2b6158c](https://github.com/boxtoys/Aegis/commit/2b6158c))
- chore: refactor blankScreen integration options ([4308b22](https://github.com/boxtoys/Aegis/commit/4308b22))
- chore: refactor blankScreen.ts and add test fixtures ([73afa56](https://github.com/boxtoys/Aegis/commit/73afa56))
- chore: refactor code to improve performance and readability ([1756955](https://github.com/boxtoys/Aegis/commit/1756955))
- chore: refactor: refactor fetch and xhr utils to support custom fetch and xhr ([1b56b46](https://github.com/boxtoys/Aegis/commit/1b56b46))
- chore: remove skip flag ([c90df58](https://github.com/boxtoys/Aegis/commit/c90df58))
- chore: remove useless code ([a8a15c1](https://github.com/boxtoys/Aegis/commit/a8a15c1))
- chore: rename preload to loader ([4beb571](https://github.com/boxtoys/Aegis/commit/4beb571))
- chore: request support retry policy ([af87194](https://github.com/boxtoys/Aegis/commit/af87194))
- chore: resource loading error no message info ([8ae6d87](https://github.com/boxtoys/Aegis/commit/8ae6d87))
- chore: resource release excludes loader.js ([e0aa9fb](https://github.com/boxtoys/Aegis/commit/e0aa9fb))
- chore: review code ([e7ab616](https://github.com/boxtoys/Aegis/commit/e7ab616))
- chore: review code ([d8d7342](https://github.com/boxtoys/Aegis/commit/d8d7342))
- chore: review code ([17e6173](https://github.com/boxtoys/Aegis/commit/17e6173))
- chore: review docs ([496fbfc](https://github.com/boxtoys/Aegis/commit/496fbfc))
- chore: rollup iife build add code comments ([6eeb928](https://github.com/boxtoys/Aegis/commit/6eeb928))
- chore: rollup iife build loader add comments ([17f15ab](https://github.com/boxtoys/Aegis/commit/17f15ab))
- chore: scenarios where the largePictureInspect plugin supports dynamic background images ([9511ffb](https://github.com/boxtoys/Aegis/commit/9511ffb))
- chore: sdk upload address using environment variables ([f19c022](https://github.com/boxtoys/Aegis/commit/f19c022))
- chore: set build target to ES5 ([c125601](https://github.com/boxtoys/Aegis/commit/c125601))
- chore: show combo url log ([4a68b0e](https://github.com/boxtoys/Aegis/commit/4a68b0e))
- chore: show file size after compilation ([c795058](https://github.com/boxtoys/Aegis/commit/c795058))
- chore: spaload support ignoreUrls params ([fe56815](https://github.com/boxtoys/Aegis/commit/fe56815))
- chore: stage code ([4e1724d](https://github.com/boxtoys/Aegis/commit/4e1724d))
- chore: stage code ([1ab8a29](https://github.com/boxtoys/Aegis/commit/1ab8a29))
- chore: stage code ([502849a](https://github.com/boxtoys/Aegis/commit/502849a))
- chore: stage types ([4af987a](https://github.com/boxtoys/Aegis/commit/4af987a))
- chore: stash code ([2407d09](https://github.com/boxtoys/Aegis/commit/2407d09))
- chore: stash code ([84c8781](https://github.com/boxtoys/Aegis/commit/84c8781))
- chore: stash code ([76212cf](https://github.com/boxtoys/Aegis/commit/76212cf))
- chore: stash code ([ac68bbe](https://github.com/boxtoys/Aegis/commit/ac68bbe))
- chore: stash code ([2ec3aa8](https://github.com/boxtoys/Aegis/commit/2ec3aa8))
- chore: stash code ([c5c76c9](https://github.com/boxtoys/Aegis/commit/c5c76c9))
- chore: stash code ([d6912c8](https://github.com/boxtoys/Aegis/commit/d6912c8))
- chore: test coverage ([bc0e296](https://github.com/boxtoys/Aegis/commit/bc0e296))
- chore: test unit ([e70ed05](https://github.com/boxtoys/Aegis/commit/e70ed05))
- chore: the jsError plugin supports the vueCheck option to disable vue.js detection ([6f6cf6a](https://github.com/boxtoys/Aegis/commit/6f6cf6a))
- chore: the pageview plugin supports the vueCheck option to disable vue.js detection ([bd647c1](https://github.com/boxtoys/Aegis/commit/bd647c1))
- chore: the pageview plugin uses absolute paths by default ([cf11425](https://github.com/boxtoys/Aegis/commit/cf11425))
- chore: transfrom const to var ([c482163](https://github.com/boxtoys/Aegis/commit/c482163))
- chore: ts type ([b5e4561](https://github.com/boxtoys/Aegis/commit/b5e4561))
- chore: ts type ([75815f5](https://github.com/boxtoys/Aegis/commit/75815f5))
- chore: unit test ([f593227](https://github.com/boxtoys/Aegis/commit/f593227))
- chore: unit test for jsError ([eb6e94a](https://github.com/boxtoys/Aegis/commit/eb6e94a))
- chore: unit test for jsError ([a5d00f4](https://github.com/boxtoys/Aegis/commit/a5d00f4))
- chore: unit test for utils ([0fef341](https://github.com/boxtoys/Aegis/commit/0fef341))
- chore: update ([3732ce4](https://github.com/boxtoys/Aegis/commit/3732ce4))
- chore: update ([a8ad019](https://github.com/boxtoys/Aegis/commit/a8ad019))
- chore: update 更新 ([916e26f](https://github.com/boxtoys/Aegis/commit/916e26f))
- chore: update browserBuilder to include url in common object ([b0c7631](https://github.com/boxtoys/Aegis/commit/b0c7631))
- chore: update build script ([64419ee](https://github.com/boxtoys/Aegis/commit/64419ee))
- chore: update builder functions to support different platforms ([80b6bc1](https://github.com/boxtoys/Aegis/commit/80b6bc1))
- chore: update changelog ([14b29b3](https://github.com/boxtoys/Aegis/commit/14b29b3))
- chore: update changelog ([5307857](https://github.com/boxtoys/Aegis/commit/5307857))
- chore: update changelog and package.json ([c25efb6](https://github.com/boxtoys/Aegis/commit/c25efb6))
- chore: update CHANGELOG.md to remove outdated version entries and maintain clarity ([bb84016](https://github.com/boxtoys/Aegis/commit/bb84016))
- chore: update dependency management config, remove package-lock.json ([37804a6](https://github.com/boxtoys/Aegis/commit/37804a6))
- chore: update feature text ([79079c8](https://github.com/boxtoys/Aegis/commit/79079c8))
- chore: update import paths and add new files ([ab92584](https://github.com/boxtoys/Aegis/commit/ab92584))
- chore: update package name and description ([e5f4a3e](https://github.com/boxtoys/Aegis/commit/e5f4a3e))
- chore: update README.md with simplified release instructions ([7ff3203](https://github.com/boxtoys/Aegis/commit/7ff3203))
- chore: update staticResource ([db45555](https://github.com/boxtoys/Aegis/commit/db45555))
- chore: update staticResource ([05f92c3](https://github.com/boxtoys/Aegis/commit/05f92c3))
- chore: update staticResource ([ce3b303](https://github.com/boxtoys/Aegis/commit/ce3b303))
- chore: update staticResource ([65d9761](https://github.com/boxtoys/Aegis/commit/65d9761))
- chore: update staticResource ([610be48](https://github.com/boxtoys/Aegis/commit/610be48))
- chore: update staticResource ([6e02405](https://github.com/boxtoys/Aegis/commit/6e02405))
- chore: update terser configuration in rollup.config.mjs ([a673459](https://github.com/boxtoys/Aegis/commit/a673459))
- chore: update test ([037eb0b](https://github.com/boxtoys/Aegis/commit/037eb0b))
- chore: update ts lib ([15b2afa](https://github.com/boxtoys/Aegis/commit/15b2afa))
- chore: update version ([7e2f379](https://github.com/boxtoys/Aegis/commit/7e2f379))
- chore: update vitest ([99ae88e](https://github.com/boxtoys/Aegis/commit/99ae88e))
- chore: use .nvmrc ([7db24e4](https://github.com/boxtoys/Aegis/commit/7db24e4))
- chore: use BrowserTiming data ([2036628](https://github.com/boxtoys/Aegis/commit/2036628))
- chore: use clone response on fetch exception ([687281a](https://github.com/boxtoys/Aegis/commit/687281a))
- chore: use ES5 syntax ([9f97748](https://github.com/boxtoys/Aegis/commit/9f97748))
- chore: use terser plugin compress code ([7bc596a](https://github.com/boxtoys/Aegis/commit/7bc596a))
- chore: using the interface define AegisInstance ([4ee2f34](https://github.com/boxtoys/Aegis/commit/4ee2f34))
- chore: when using xhr, temporarily do not perform retry logic ([80a3794](https://github.com/boxtoys/Aegis/commit/80a3794))
- fix: **vue** variables are compressed ([8256a11](https://github.com/boxtoys/Aegis/commit/8256a11))
- fix: 在 onSPA_LOAD 测试中添加 **SPA_LOAD_READY_CALLBACK** 调用，以确保回调逻辑在不同场景下正常执行 ([d9373bd](https://github.com/boxtoys/Aegis/commit/d9373bd))
- fix: abort reason not consistent across platforms ([13e3989](https://github.com/boxtoys/Aegis/commit/13e3989))
- fix: api test case timeout ([90d2ef1](https://github.com/boxtoys/Aegis/commit/90d2ef1))
- fix: build ts error ([1eefd63](https://github.com/boxtoys/Aegis/commit/1eefd63))
- fix: case error cannot exit mock server ([caadff3](https://github.com/boxtoys/Aegis/commit/caadff3))
- fix: change protocol words ([5098b22](https://github.com/boxtoys/Aegis/commit/5098b22))
- fix: circular dependencies ([4e5183b](https://github.com/boxtoys/Aegis/commit/4e5183b))
- fix: cleanupTasks is not a global variable ([ce43dbf](https://github.com/boxtoys/Aegis/commit/ce43dbf))
- fix: cookie path issue in cookie.ts ([950cadd](https://github.com/boxtoys/Aegis/commit/950cadd))
- fix: docs error ([5c6e973](https://github.com/boxtoys/Aegis/commit/5c6e973))
- fix: document.elementFromPoint is not available and does not report logs ([bb8e687](https://github.com/boxtoys/Aegis/commit/bb8e687))
- fix: document.elementFromPoint return null scenes ([29e155b](https://github.com/boxtoys/Aegis/commit/29e155b))
- fix: element className is not string ([4c069b8](https://github.com/boxtoys/Aegis/commit/4c069b8))
- fix: ensure data flow using asynchronous methods ([9a7c3ec](https://github.com/boxtoys/Aegis/commit/9a7c3ec))
- fix: error messages should use console error ([03683db](https://github.com/boxtoys/Aegis/commit/03683db))
- fix: error when the plugin is not available ([a494ecf](https://github.com/boxtoys/Aegis/commit/a494ecf))
- fix: es6 -> es5 ([0028d22](https://github.com/boxtoys/Aegis/commit/0028d22))
- fix: fetch response has been consumed, text() again exception ([4115938](https://github.com/boxtoys/Aegis/commit/4115938))
- fix: fix issue ([38b6598](https://github.com/boxtoys/Aegis/commit/38b6598))
- fix: fix not report CLS ([7bdd2c9](https://github.com/boxtoys/Aegis/commit/7bdd2c9))
- fix: fix reduceHighResTimestamp issue ([04d6b74](https://github.com/boxtoys/Aegis/commit/04d6b74))
- fix: fix reducePrecision issue ([9c4130a](https://github.com/boxtoys/Aegis/commit/9c4130a))
- fix: fix spa timeout issue ([35e492f](https://github.com/boxtoys/Aegis/commit/35e492f))
- fix: fix test case ([1f0cef8](https://github.com/boxtoys/Aegis/commit/1f0cef8))
- fix: fix test case ([197477c](https://github.com/boxtoys/Aegis/commit/197477c))
- fix: fix test case ([5ee4d5a](https://github.com/boxtoys/Aegis/commit/5ee4d5a))
- fix: headers is not defined ([7825c95](https://github.com/boxtoys/Aegis/commit/7825c95))
- fix: hide error message ([53491eb](https://github.com/boxtoys/Aegis/commit/53491eb))
- fix: ignore background img detect on ios ([3b3b49c](https://github.com/boxtoys/Aegis/commit/3b3b49c))
- fix: iife mode same file name packing error ([72a6297](https://github.com/boxtoys/Aegis/commit/72a6297))
- fix: immediate option lost ([e4b3686](https://github.com/boxtoys/Aegis/commit/e4b3686))
- fix: incorrect word ([23b96e0](https://github.com/boxtoys/Aegis/commit/23b96e0))
- fix: js error plugin IIFE build error ([126b8be](https://github.com/boxtoys/Aegis/commit/126b8be))
- fix: local combo build file not found ([692e780](https://github.com/boxtoys/Aegis/commit/692e780))
- fix: multiple instances ([af3a672](https://github.com/boxtoys/Aegis/commit/af3a672))
- fix: no change in file hash ([8b89fd8](https://github.com/boxtoys/Aegis/commit/8b89fd8))
- fix: node v21 presence navigator api ([b8f8902](https://github.com/boxtoys/Aegis/commit/b8f8902))
- fix: normalize pathname ([0c9e9d4](https://github.com/boxtoys/Aegis/commit/0c9e9d4))
- fix: package introduction mode prioritization issue ([6f24e30](https://github.com/boxtoys/Aegis/commit/6f24e30))
- fix: plugins is undefined array, plugins[i].name is error ([491f70e](https://github.com/boxtoys/Aegis/commit/491f70e))
- fix: precollect plugin setup order ([6257428](https://github.com/boxtoys/Aegis/commit/6257428))
- fix: remote combo remove duplicates ([31913a0](https://github.com/boxtoys/Aegis/commit/31913a0))
- fix: remove sub tsconfig json ([9a7d1b8](https://github.com/boxtoys/Aegis/commit/9a7d1b8))
- fix: rollup build error ([50a8d30](https://github.com/boxtoys/Aegis/commit/50a8d30))
- fix: rollup build error: Unterminated string constant ([59b2ac3](https://github.com/boxtoys/Aegis/commit/59b2ac3))
- fix: rollupError: Unterminated string constant ([15c7ae7](https://github.com/boxtoys/Aegis/commit/15c7ae7))
- fix: scenarios where element.tagName is null ([0030f14](https://github.com/boxtoys/Aegis/commit/0030f14))
- fix: spa load reset done ([a5f98f2](https://github.com/boxtoys/Aegis/commit/a5f98f2))
- fix: test cases ([a5e756a](https://github.com/boxtoys/Aegis/commit/a5e756a))
- fix: test cases network error ([3431567](https://github.com/boxtoys/Aegis/commit/3431567))
- fix: ts error ([4b89d52](https://github.com/boxtoys/Aegis/commit/4b89d52))
- fix: ts error ([9e6d75c](https://github.com/boxtoys/Aegis/commit/9e6d75c))
- fix: ts error ([4afa91f](https://github.com/boxtoys/Aegis/commit/4afa91f))
- fix: ts error ([db183c7](https://github.com/boxtoys/Aegis/commit/db183c7))
- fix: ts error ([2c59773](https://github.com/boxtoys/Aegis/commit/2c59773))
- fix: ts type error ([a702773](https://github.com/boxtoys/Aegis/commit/a702773))
- fix: type error ([726e17b](https://github.com/boxtoys/Aegis/commit/726e17b))
- fix: update combo.ts to include version parameter in filterChoices function ([0ce40b7](https://github.com/boxtoys/Aegis/commit/0ce40b7))
- fix: url case issue ([978ec4b](https://github.com/boxtoys/Aegis/commit/978ec4b))
- fix: variable init timing ([54c0203](https://github.com/boxtoys/Aegis/commit/54c0203))
- fix: vue3 vue-router first time using replaceState for jump ([e0b3ee6](https://github.com/boxtoys/Aegis/commit/e0b3ee6))
- fix: xhr InvalidStateError DOMException ([ae04fb8](https://github.com/boxtoys/Aegis/commit/ae04fb8))
- feat: 测试case ([9971456](https://github.com/boxtoys/Aegis/commit/9971456))
- feat: 插件 ([56684ea](https://github.com/boxtoys/Aegis/commit/56684ea))
- feat: 插件 ([c620b25](https://github.com/boxtoys/Aegis/commit/c620b25))
- feat: 插件 ([60d85f4](https://github.com/boxtoys/Aegis/commit/60d85f4))
- feat: 插件优化 ([8906729](https://github.com/boxtoys/Aegis/commit/8906729))
- feat: 代码优化 ([9059084](https://github.com/boxtoys/Aegis/commit/9059084))
- feat: 更新 ([85db80f](https://github.com/boxtoys/Aegis/commit/85db80f))
- feat: 更新 ([3ffba0c](https://github.com/boxtoys/Aegis/commit/3ffba0c))
- feat: 更新白屏插件 ([6e1f83a](https://github.com/boxtoys/Aegis/commit/6e1f83a))
- feat: 更新了不管是否存在骨架都进行二次回正检测 ([789500e](https://github.com/boxtoys/Aegis/commit/789500e))
- feat: 添加 **SPA_LOAD_READY_CALLBACK** 回调以支持 SPA 加载完成后的处理逻辑，并优化 onSPA_LOAD 函数中的脚本加载逻辑 ([5fee7cc](https://github.com/boxtoys/Aegis/commit/5fee7cc))
- feat: 完善测试用例 ([437d788](https://github.com/boxtoys/Aegis/commit/437d788))
- feat: 完善LCP测试用例 ([737cf7d](https://github.com/boxtoys/Aegis/commit/737cf7d))
- feat: 新增 README.md ([e6db215](https://github.com/boxtoys/Aegis/commit/e6db215))
- feat: 新增获取设备vendor model ([2b888e3](https://github.com/boxtoys/Aegis/commit/2b888e3))
- feat: 新增device插件 ([3be060b](https://github.com/boxtoys/Aegis/commit/3be060b))
- feat: 新增reourceError ([0d0e054](https://github.com/boxtoys/Aegis/commit/0d0e054))
- feat: 修改 ([0f88aa9](https://github.com/boxtoys/Aegis/commit/0f88aa9))
- feat: 修改transferSize 逻辑 ([3c07e94](https://github.com/boxtoys/Aegis/commit/3c07e94))
- feat: 优化 ([b78126e](https://github.com/boxtoys/Aegis/commit/b78126e))
- feat: 优化了路由监听, 函数命令等 ([ad1d70f](https://github.com/boxtoys/Aegis/commit/ad1d70f))
- feat: add action plugin ([a04de32](https://github.com/boxtoys/Aegis/commit/a04de32))
- feat: add all performance obsever ([cebd49c](https://github.com/boxtoys/Aegis/commit/cebd49c))
- feat: add blankScreen plugins ([6f49739](https://github.com/boxtoys/Aegis/commit/6f49739))
- feat: add bridge observe ([5cba719](https://github.com/boxtoys/Aegis/commit/5cba719))
- feat: add browser timing for spa load ([971f5ba](https://github.com/boxtoys/Aegis/commit/971f5ba))
- feat: add detect browser feature plugin ([2cb04bc](https://github.com/boxtoys/Aegis/commit/2cb04bc))
- feat: add endTime property to SPA load records and update duration calculations ([1eabc97](https://github.com/boxtoys/Aegis/commit/1eabc97))
- feat: add feature plugin doc file ([5e47487](https://github.com/boxtoys/Aegis/commit/5e47487))
- feat: add htmlTreeStringify utility function to staticResource.ts ([462cfd5](https://github.com/boxtoys/Aegis/commit/462cfd5))
- feat: add ignoreUrls in resource ([714e246](https://github.com/boxtoys/Aegis/commit/714e246))
- feat: add new upload ([067b06d](https://github.com/boxtoys/Aegis/commit/067b06d))
- feat: add pageview integration and test ([7093c1d](https://github.com/boxtoys/Aegis/commit/7093c1d))
- feat: add perf plugin ([c6508d4](https://github.com/boxtoys/Aegis/commit/c6508d4))
- feat: add script detect for spa load ([c163606](https://github.com/boxtoys/Aegis/commit/c163606))
- feat: add spa_load ([aada673](https://github.com/boxtoys/Aegis/commit/aada673))
- feat: add static reource file ([ee5450f](https://github.com/boxtoys/Aegis/commit/ee5450f))
- feat: add stop judge ([1935a70](https://github.com/boxtoys/Aegis/commit/1935a70))
- feat: add the immediate attribute to support sending immediately in batch mode ([c4a1a24](https://github.com/boxtoys/Aegis/commit/c4a1a24))
- feat: add TTI report ([894c875](https://github.com/boxtoys/Aegis/commit/894c875))
- feat: api插件测试用例 ([8ea26c2](https://github.com/boxtoys/Aegis/commit/8ea26c2))
- feat: bridge http update ([a336660](https://github.com/boxtoys/Aegis/commit/a336660))
- feat: bridge初始化 ([efdfa47](https://github.com/boxtoys/Aegis/commit/efdfa47))
- feat: chore: update npm script to deploy documentation ([c10a9f3](https://github.com/boxtoys/Aegis/commit/c10a9f3))
- feat: clear ([46b1d73](https://github.com/boxtoys/Aegis/commit/46b1d73))
- feat: config manager completed ([21ba497](https://github.com/boxtoys/Aegis/commit/21ba497))
- feat: document listener AEJSBridgeReady ([e91846e](https://github.com/boxtoys/Aegis/commit/e91846e))
- feat: fix getElementUrl function for SVGAElement ([da70d23](https://github.com/boxtoys/Aegis/commit/da70d23))
- feat: http plugin support customErrorReportPolicy option ([5e7320d](https://github.com/boxtoys/Aegis/commit/5e7320d))
- feat: http测试用例 ([4f20b9c](https://github.com/boxtoys/Aegis/commit/4f20b9c))
- feat: http插件优化 ([bfc0117](https://github.com/boxtoys/Aegis/commit/bfc0117))
- feat: http修改 ([858049f](https://github.com/boxtoys/Aegis/commit/858049f))
- feat: ignore base64url request report ([7b74d85](https://github.com/boxtoys/Aegis/commit/7b74d85))
- feat: implement return value chaining for event handlers ([3e8dc02](https://github.com/boxtoys/Aegis/commit/3e8dc02))
- feat: improve ([1e954de](https://github.com/boxtoys/Aegis/commit/1e954de))
- feat: improve ([48c6343](https://github.com/boxtoys/Aegis/commit/48c6343))
- feat: improve ([5fd641d](https://github.com/boxtoys/Aegis/commit/5fd641d))
- feat: improve ([e2a0de3](https://github.com/boxtoys/Aegis/commit/e2a0de3))
- feat: improve ([7f342c9](https://github.com/boxtoys/Aegis/commit/7f342c9))
- feat: improve ([82549f6](https://github.com/boxtoys/Aegis/commit/82549f6))
- feat: improve ([f3af43d](https://github.com/boxtoys/Aegis/commit/f3af43d))
- feat: improve BrowserTime field ([2603e3d](https://github.com/boxtoys/Aegis/commit/2603e3d))
- feat: improve CLS test case ([c25b04e](https://github.com/boxtoys/Aegis/commit/c25b04e))
- feat: improve docs ([168e11e](https://github.com/boxtoys/Aegis/commit/168e11e))
- feat: improve esm and cjs build ([0e3f432](https://github.com/boxtoys/Aegis/commit/0e3f432))
- feat: improve initial script detect ([7beb80c](https://github.com/boxtoys/Aegis/commit/7beb80c))
- feat: improve node display none case ([e1b59d1](https://github.com/boxtoys/Aegis/commit/e1b59d1))
- feat: improve perf docs ([6af9cb5](https://github.com/boxtoys/Aegis/commit/6af9cb5))
- feat: improve performance docs ([ea5748c](https://github.com/boxtoys/Aegis/commit/ea5748c))
- feat: improve performance init timing ([0efe026](https://github.com/boxtoys/Aegis/commit/0efe026))
- feat: improve performance plugin, simplify perf entry ([aabd73c](https://github.com/boxtoys/Aegis/commit/aabd73c))
- feat: improve spa load ([603c0a9](https://github.com/boxtoys/Aegis/commit/603c0a9))
- feat: improve spa load ([3cfc22a](https://github.com/boxtoys/Aegis/commit/3cfc22a))
- feat: improve spa load ([21f9d3d](https://github.com/boxtoys/Aegis/commit/21f9d3d))
- feat: improve spa_load ([614600b](https://github.com/boxtoys/Aegis/commit/614600b))
- feat: improve SPA_LOAD ([47f6144](https://github.com/boxtoys/Aegis/commit/47f6144))
- feat: improve spaload crossorigin link compatibility ([3e39370](https://github.com/boxtoys/Aegis/commit/3e39370))
- feat: improve test case ([3763e39](https://github.com/boxtoys/Aegis/commit/3763e39))
- feat: improve test case ([a53e63a](https://github.com/boxtoys/Aegis/commit/a53e63a))
- feat: improve test case(add bridge request case) ([fdaed85](https://github.com/boxtoys/Aegis/commit/fdaed85))
- feat: improve tests ([ca2c669](https://github.com/boxtoys/Aegis/commit/ca2c669))
- feat: merge file cli \& upload cdn ([e379705](https://github.com/boxtoys/Aegis/commit/e379705))
- feat: modify spaload check gap, change gap from 100ms to 300ms ([f5479d1](https://github.com/boxtoys/Aegis/commit/f5479d1))
- feat: plugin interface completed ([2d813ca](https://github.com/boxtoys/Aegis/commit/2d813ca))
- feat: reduce per entry ([9c78513](https://github.com/boxtoys/Aegis/commit/9c78513))
- feat: refactor blankScreen integration and fix test case ([851f8cc](https://github.com/boxtoys/Aegis/commit/851f8cc))
- feat: remove console ([0919a9b](https://github.com/boxtoys/Aegis/commit/0919a9b))
- feat: remove spaLoad ios judge ([e1f9e29](https://github.com/boxtoys/Aegis/commit/e1f9e29))
- feat: remove unused code ([20a6bb9](https://github.com/boxtoys/Aegis/commit/20a6bb9))
- feat: remove unused field from TTI ([a88330a](https://github.com/boxtoys/Aegis/commit/a88330a))
- feat: request测试用例 ([9ffa78e](https://github.com/boxtoys/Aegis/commit/9ffa78e))
- feat: request插件 ([c13437c](https://github.com/boxtoys/Aegis/commit/c13437c))
- feat: request插件 ([5f06908](https://github.com/boxtoys/Aegis/commit/5f06908))
- feat: request插件 ([3d309bf](https://github.com/boxtoys/Aegis/commit/3d309bf))
- feat: resource error plugin done ([e1cbae6](https://github.com/boxtoys/Aegis/commit/e1cbae6))
- feat: revert old iife config ([fe3c65a](https://github.com/boxtoys/Aegis/commit/fe3c65a))
- feat: simpilfy LCP entry ([9703415](https://github.com/boxtoys/Aegis/commit/9703415))
- feat: skip blankScreen test and add new tests for Aegis staticResource integration ([c3a8ea5](https://github.com/boxtoys/Aegis/commit/c3a8ea5))
- feat: spa_load add records params ([a2428f0](https://github.com/boxtoys/Aegis/commit/a2428f0))
- feat: stack-parser ([a0844d4](https://github.com/boxtoys/Aegis/commit/a0844d4))
- feat: stash code ([7c5e50f](https://github.com/boxtoys/Aegis/commit/7c5e50f))
- feat: stash code ([65f3d6a](https://github.com/boxtoys/Aegis/commit/65f3d6a))
- feat: stash code ([04db9b6](https://github.com/boxtoys/Aegis/commit/04db9b6))
- feat: suppor largePictureInspect plugin ([0bf8d0b](https://github.com/boxtoys/Aegis/commit/0bf8d0b))
- feat: support CJS \& IIFE bundle ([22223f7](https://github.com/boxtoys/Aegis/commit/22223f7))
- feat: support custom sender ([2211cdb](https://github.com/boxtoys/Aegis/commit/2211cdb))
- feat: support pre collect ([d853810](https://github.com/boxtoys/Aegis/commit/d853810))
- feat: support report sdk error ([832ec1e](https://github.com/boxtoys/Aegis/commit/832ec1e))
- feat: support transports options ([4dc2479](https://github.com/boxtoys/Aegis/commit/4dc2479))
- feat: test unit ([47118e5](https://github.com/boxtoys/Aegis/commit/47118e5))
- feat: update ([409e131](https://github.com/boxtoys/Aegis/commit/409e131))
- feat: update device components ([f376626](https://github.com/boxtoys/Aegis/commit/f376626))
- feat: update spa_load ([b09fad5](https://github.com/boxtoys/Aegis/commit/b09fad5))
- feat: update spa_load and test cases ([bc7af77](https://github.com/boxtoys/Aegis/commit/bc7af77))
- feat(blog): 服务端架构文档0.1 ([209ce2d](https://github.com/boxtoys/Aegis/commit/209ce2d))
- feat(doc): 修改服务端文档内容 ([0d10aa9](https://github.com/boxtoys/Aegis/commit/0d10aa9))
- 1.0.18 ([5807f6d](https://github.com/boxtoys/Aegis/commit/5807f6d))
- 1.0.19 ([1a059a1](https://github.com/boxtoys/Aegis/commit/1a059a1))
- 1.0.20 ([da21327](https://github.com/boxtoys/Aegis/commit/da21327))
- 1.0.21 ([aba86a1](https://github.com/boxtoys/Aegis/commit/aba86a1))
- 1.0.22 ([3fb4fce](https://github.com/boxtoys/Aegis/commit/3fb4fce))
- 1.0.23 ([ecb41e4](https://github.com/boxtoys/Aegis/commit/ecb41e4))
- 1.0.24 ([23d9bc8](https://github.com/boxtoys/Aegis/commit/23d9bc8))
- 1.0.25 ([4cfa080](https://github.com/boxtoys/Aegis/commit/4cfa080))
- 1.0.26 ([dbccb19](https://github.com/boxtoys/Aegis/commit/dbccb19))
- 1.0.27 ([0f5cbe7](https://github.com/boxtoys/Aegis/commit/0f5cbe7))
- 1.0.28 ([a011fd8](https://github.com/boxtoys/Aegis/commit/a011fd8))
- 1.0.29 ([5d0feae](https://github.com/boxtoys/Aegis/commit/5d0feae))
- 1.0.30 ([1129fe3](https://github.com/boxtoys/Aegis/commit/1129fe3))
- 1.0.31 ([7e6b011](https://github.com/boxtoys/Aegis/commit/7e6b011))
- 1.0.32 ([721b7e5](https://github.com/boxtoys/Aegis/commit/721b7e5))
- 1.0.33 ([ff582d0](https://github.com/boxtoys/Aegis/commit/ff582d0))
- 1.0.34 ([3a9fbc6](https://github.com/boxtoys/Aegis/commit/3a9fbc6))
- 1.0.35 ([994ddbf](https://github.com/boxtoys/Aegis/commit/994ddbf))
- 1.0.36 ([25dceba](https://github.com/boxtoys/Aegis/commit/25dceba))
- 1.0.37 ([43cdeb9](https://github.com/boxtoys/Aegis/commit/43cdeb9))
- 1.0.38 ([9aac97d](https://github.com/boxtoys/Aegis/commit/9aac97d))
- 1.0.39 ([b33fdf4](https://github.com/boxtoys/Aegis/commit/b33fdf4))
- 1.0.40 ([ff49119](https://github.com/boxtoys/Aegis/commit/ff49119))
- 1.0.41 ([51e2bb3](https://github.com/boxtoys/Aegis/commit/51e2bb3))
- 1.0.42 ([6366c56](https://github.com/boxtoys/Aegis/commit/6366c56))
- 1.0.43 ([36c37b1](https://github.com/boxtoys/Aegis/commit/36c37b1))
- 1.0.44 ([eb75ebb](https://github.com/boxtoys/Aegis/commit/eb75ebb))
- 1.0.45 ([4e7eda1](https://github.com/boxtoys/Aegis/commit/4e7eda1))
- 1.0.46 ([4bcdf6b](https://github.com/boxtoys/Aegis/commit/4bcdf6b))
- 1.0.47 ([cb1394b](https://github.com/boxtoys/Aegis/commit/cb1394b))
- 1.0.48 ([7179f1a](https://github.com/boxtoys/Aegis/commit/7179f1a))
- 1.0.49 ([137718a](https://github.com/boxtoys/Aegis/commit/137718a))
- 1.0.50 ([4062713](https://github.com/boxtoys/Aegis/commit/4062713))
- 1.0.51 ([69d6656](https://github.com/boxtoys/Aegis/commit/69d6656))
- 1.0.52 ([6c2d771](https://github.com/boxtoys/Aegis/commit/6c2d771))
- 1.0.53 ([bd61dd0](https://github.com/boxtoys/Aegis/commit/bd61dd0))
- 1.0.54 ([dce74c9](https://github.com/boxtoys/Aegis/commit/dce74c9))
- 1.0.55 ([cc41c6f](https://github.com/boxtoys/Aegis/commit/cc41c6f))
- 1.0.56 ([167cde5](https://github.com/boxtoys/Aegis/commit/167cde5))
- 1.0.57 ([13ef7bc](https://github.com/boxtoys/Aegis/commit/13ef7bc))
- 1.0.58 ([c38cd95](https://github.com/boxtoys/Aegis/commit/c38cd95))
- 1.0.59 ([d5393f8](https://github.com/boxtoys/Aegis/commit/d5393f8))
- 1.0.60 ([63f6229](https://github.com/boxtoys/Aegis/commit/63f6229))
- 1.0.61 ([3616840](https://github.com/boxtoys/Aegis/commit/3616840))
- 1.0.62 ([fa1d0e9](https://github.com/boxtoys/Aegis/commit/fa1d0e9))
- 1.0.63 ([e4a4209](https://github.com/boxtoys/Aegis/commit/e4a4209))
- 1.0.64 ([a05aef7](https://github.com/boxtoys/Aegis/commit/a05aef7))
- 1.0.66 ([ac42eb9](https://github.com/boxtoys/Aegis/commit/ac42eb9))
- 1.0.66 ([f7c798c](https://github.com/boxtoys/Aegis/commit/f7c798c))
- 1.0.67 ([d6f951d](https://github.com/boxtoys/Aegis/commit/d6f951d))
- 白屏插件新增预不合法窗口逻辑 ([d520d18](https://github.com/boxtoys/Aegis/commit/d520d18))
- 新增资源插件transfersize ([84e74be](https://github.com/boxtoys/Aegis/commit/84e74be))
- Initial commit ([c18a50f](https://github.com/boxtoys/Aegis/commit/c18a50f))
- refactor: 优化 onSPA_LOAD 函数中的数组操作，使用 filter 方法替代 splice 以提高代码可读性和性能 ([869fd47](https://github.com/boxtoys/Aegis/commit/869fd47))
- refactor: add event handling ([2df776b](https://github.com/boxtoys/Aegis/commit/2df776b))
- refactor: add findIndex polyfill ([fe4b5f4](https://github.com/boxtoys/Aegis/commit/fe4b5f4))
- refactor: add get plugin method \& plugin export option ([3112fa6](https://github.com/boxtoys/Aegis/commit/3112fa6))
- refactor: add JSONUtils ([a28d794](https://github.com/boxtoys/Aegis/commit/a28d794))
- refactor: add requestIdleCallback polyfill ([10653b5](https://github.com/boxtoys/Aegis/commit/10653b5))
- refactor: added native method check logic to ensure that the original methods are [native code] ([ef5ad06](https://github.com/boxtoys/Aegis/commit/ef5ad06))
- refactor: adjust build policy ([0484666](https://github.com/boxtoys/Aegis/commit/0484666))
- refactor: aegis class and add tests ([24ada6f](https://github.com/boxtoys/Aegis/commit/24ada6f))
- refactor: aegis class and Event interface ([85d525c](https://github.com/boxtoys/Aegis/commit/85d525c))
- refactor: aop replace the apply method with the call method ([b864fda](https://github.com/boxtoys/Aegis/commit/b864fda))
- refactor: aop supports multiple layers of proxies ([736b1ae](https://github.com/boxtoys/Aegis/commit/736b1ae))
- refactor: async detect vue router ([2dddf73](https://github.com/boxtoys/Aegis/commit/2dddf73))
- refactor: blankScreen plugin use pageview plugin mode option ([77cf7e6](https://github.com/boxtoys/Aegis/commit/77cf7e6))
- refactor: change from proxy once to proxy multiple times ([95567e0](https://github.com/boxtoys/Aegis/commit/95567e0))
- refactor: change pointerup to pointerdown ([dd75a54](https://github.com/boxtoys/Aegis/commit/dd75a54))
- refactor: clean istanbul ignore comment ([b8133db](https://github.com/boxtoys/Aegis/commit/b8133db))
- refactor: clear 💩 ([ece02b3](https://github.com/boxtoys/Aegis/commit/ece02b3))
- refactor: clear 💩 ([38bf5bd](https://github.com/boxtoys/Aegis/commit/38bf5bd))
- refactor: clear 💩💩💩 ([b2d98bc](https://github.com/boxtoys/Aegis/commit/b2d98bc))
- refactor: code clean ([0f20df7](https://github.com/boxtoys/Aegis/commit/0f20df7))
- refactor: code optimize ([9ba831d](https://github.com/boxtoys/Aegis/commit/9ba831d))
- refactor: code optimize ([6927a8f](https://github.com/boxtoys/Aegis/commit/6927a8f))
- refactor: code optimize ([b1ba941](https://github.com/boxtoys/Aegis/commit/b1ba941))
- refactor: code optimize ([109151b](https://github.com/boxtoys/Aegis/commit/109151b))
- refactor: code optimize ([5fb805f](https://github.com/boxtoys/Aegis/commit/5fb805f))
- refactor: code optimize ([a1b3f8d](https://github.com/boxtoys/Aegis/commit/a1b3f8d))
- refactor: code optimize ([34d38de](https://github.com/boxtoys/Aegis/commit/34d38de))
- refactor: code optimize ([4914d4e](https://github.com/boxtoys/Aegis/commit/4914d4e))
- refactor: code optimize ([1c9cd95](https://github.com/boxtoys/Aegis/commit/1c9cd95))
- refactor: code optimize ([5b5f9fb](https://github.com/boxtoys/Aegis/commit/5b5f9fb))
- refactor: code optimize ([3e7310a](https://github.com/boxtoys/Aegis/commit/3e7310a))
- refactor: code optimize ([3b7bc94](https://github.com/boxtoys/Aegis/commit/3b7bc94))
- refactor: code optimize ([83a9ed7](https://github.com/boxtoys/Aegis/commit/83a9ed7))
- refactor: code optimize ([f97d8f1](https://github.com/boxtoys/Aegis/commit/f97d8f1))
- refactor: code optimize ([3a9daf8](https://github.com/boxtoys/Aegis/commit/3a9daf8))
- refactor: code optimize ([c6ff78e](https://github.com/boxtoys/Aegis/commit/c6ff78e))
- refactor: code optimize ([739f864](https://github.com/boxtoys/Aegis/commit/739f864))
- refactor: code optimize ([42a8cdf](https://github.com/boxtoys/Aegis/commit/42a8cdf))
- refactor: code optimize ([0dabe67](https://github.com/boxtoys/Aegis/commit/0dabe67))
- refactor: code optimize ([ed20e9d](https://github.com/boxtoys/Aegis/commit/ed20e9d))
- refactor: code optimize ([b94d26d](https://github.com/boxtoys/Aegis/commit/b94d26d))
- refactor: code optimize ([516c267](https://github.com/boxtoys/Aegis/commit/516c267))
- refactor: code optimize ([924c5d8](https://github.com/boxtoys/Aegis/commit/924c5d8))
- refactor: code optimize ([4687dc6](https://github.com/boxtoys/Aegis/commit/4687dc6))
- refactor: code optimize ([fd5771e](https://github.com/boxtoys/Aegis/commit/fd5771e))
- refactor: code optimize ([1db681e](https://github.com/boxtoys/Aegis/commit/1db681e))
- refactor: code optimize ([78fe839](https://github.com/boxtoys/Aegis/commit/78fe839))
- refactor: event callback context and parameters ([e0fa065](https://github.com/boxtoys/Aegis/commit/e0fa065))
- refactor: extract api observer for use by other plugins ([9957f59](https://github.com/boxtoys/Aegis/commit/9957f59))
- refactor: extract detectVueFramework code \& support vue2 and vue3 \& support errorHandler detect ([0fa5e80](https://github.com/boxtoys/Aegis/commit/0fa5e80))
- refactor: extract route observer for use by other plugins ([de2670b](https://github.com/boxtoys/Aegis/commit/de2670b))
- refactor: filterByType function to filterResourceByType in staticResource.ts ([8e8da51](https://github.com/boxtoys/Aegis/commit/8e8da51))
- refactor: getElementUrl function in resourceError.ts ([b4ae0da](https://github.com/boxtoys/Aegis/commit/b4ae0da))
- refactor: getElementUrl function in resourceError.ts to handle different target types ([f4f3c00](https://github.com/boxtoys/Aegis/commit/f4f3c00))
- refactor: getElementUrl function in resourceError.ts to handle different target types ([c8ee4b2](https://github.com/boxtoys/Aegis/commit/c8ee4b2))
- refactor: iife build clean useless code ([7126ca2](https://github.com/boxtoys/Aegis/commit/7126ca2))
- refactor: improve code compression optimization ([3b3f56c](https://github.com/boxtoys/Aegis/commit/3b3f56c))
- refactor: improve UUID readability ([6bc9355](https://github.com/boxtoys/Aegis/commit/6bc9355))
- refactor: isomorphic optimization ([00f9fe7](https://github.com/boxtoys/Aegis/commit/00f9fe7))
- refactor: jsError plugin ([68c6986](https://github.com/boxtoys/Aegis/commit/68c6986))
- refactor: logic for extracting get points ([de3e25c](https://github.com/boxtoys/Aegis/commit/de3e25c))
- refactor: move checkIsIgnored function to common util ([20b090e](https://github.com/boxtoys/Aegis/commit/20b090e))
- refactor: normalize timestamps in resource creation ([149f610](https://github.com/boxtoys/Aegis/commit/149f610))
- refactor: optimize build IIFE package script ([9677214](https://github.com/boxtoys/Aegis/commit/9677214))
- refactor: optimize code ([802ba4c](https://github.com/boxtoys/Aegis/commit/802ba4c))
- refactor: optimize code ([54db2f9](https://github.com/boxtoys/Aegis/commit/54db2f9))
- refactor: optimize device detection and classification ([1f2c671](https://github.com/boxtoys/Aegis/commit/1f2c671))
- refactor: optimize PixelTransport send method for large data ([329f59d](https://github.com/boxtoys/Aegis/commit/329f59d))
- refactor: optimize report SDK error code ([740a6f1](https://github.com/boxtoys/Aegis/commit/740a6f1))
- refactor: optimize store module code ([0033924](https://github.com/boxtoys/Aegis/commit/0033924))
- refactor: optimize the performance of MutationObserver ([d055cbc](https://github.com/boxtoys/Aegis/commit/d055cbc))
- refactor: organize the directory structure ([78be3a9](https://github.com/boxtoys/Aegis/commit/78be3a9))
- refactor: organize the directory structure ([cfe2a4d](https://github.com/boxtoys/Aegis/commit/cfe2a4d))
- refactor: organize the directory structureo ([f1ffab9](https://github.com/boxtoys/Aegis/commit/f1ffab9))
- refactor: perf SPA_LOAD plugin use pageview plugin mode option ([4431493](https://github.com/boxtoys/Aegis/commit/4431493))
- refactor: preload code build process ([2173a45](https://github.com/boxtoys/Aegis/commit/2173a45))
- refactor: refactor code to use polyfills for Object.assign and includes ([58f1a8d](https://github.com/boxtoys/Aegis/commit/58f1a8d))
- refactor: refactor Event class to use generic types ([3e7ab70](https://github.com/boxtoys/Aegis/commit/3e7ab70))
- refactor: refactor event emitter to allow any argument types and return callback status ([cd8bc17](https://github.com/boxtoys/Aegis/commit/cd8bc17))
- refactor: refactor the request functions, unify the request parameter structure ([b9fadc9](https://github.com/boxtoys/Aegis/commit/b9fadc9))
- refactor: refactored the aop function, use a stack of proxies for method interception ([59a044a](https://github.com/boxtoys/Aegis/commit/59a044a))
- refactor: remove isPolyfill function at compile time ([61d7385](https://github.com/boxtoys/Aegis/commit/61d7385))
- refactor: remove skip from test for loading the same resource multiple times ([063b97c](https://github.com/boxtoys/Aegis/commit/063b97c))
- refactor: remove ts rest syntax polyfill ([1a7ef29](https://github.com/boxtoys/Aegis/commit/1a7ef29))
- refactor: remove xhr onerror handler ([8fdd7ad](https://github.com/boxtoys/Aegis/commit/8fdd7ad))
- refactor: rename inteface ([6b5757d](https://github.com/boxtoys/Aegis/commit/6b5757d))
- refactor: replace polyfill api ([c41d651](https://github.com/boxtoys/Aegis/commit/c41d651))
- refactor: request code optimize ([3f6286d](https://github.com/boxtoys/Aegis/commit/3f6286d))
- refactor: resource metrics data optimize ([012b178](https://github.com/boxtoys/Aegis/commit/012b178))
- refactor: resourceError integration and add createTiming function ([0f851bd](https://github.com/boxtoys/Aegis/commit/0f851bd))
- refactor: rewriting ConfigManager using class syntax ([73faa19](https://github.com/boxtoys/Aegis/commit/73faa19))
- refactor: rewriting Event, Aegis using class syntax ([f29f4cf](https://github.com/boxtoys/Aegis/commit/f29f4cf))
- refactor: rewriting Transports using class syntax ([93af6a9](https://github.com/boxtoys/Aegis/commit/93af6a9))
- refactor: standalone builder ([0476fbb](https://github.com/boxtoys/Aegis/commit/0476fbb))
- refactor: staticResource integration and add ignoreTypes option ([18c0ba2](https://github.com/boxtoys/Aegis/commit/18c0ba2))
- refactor: staticResource.spec.ts file ([404bc1e](https://github.com/boxtoys/Aegis/commit/404bc1e))
- refactor: store code optimize ([b2172c8](https://github.com/boxtoys/Aegis/commit/b2172c8))
- refactor: test framework change from jest to vitest ([b498028](https://github.com/boxtoys/Aegis/commit/b498028))
- refactor: the send data uses transports and supports multiple transport ([e4356e9](https://github.com/boxtoys/Aegis/commit/e4356e9))
- refactor: update normalizeTimestamp test descriptions ([2c16a83](https://github.com/boxtoys/Aegis/commit/2c16a83))
- refactor: use ES5 syntax ([9546c78](https://github.com/boxtoys/Aegis/commit/9546c78))
- refactor: use PointerEvent for multi-terminal compatibility ([ed71d96](https://github.com/boxtoys/Aegis/commit/ed71d96))
- refactor: vue detect code optimize ([0c85b7d](https://github.com/boxtoys/Aegis/commit/0c85b7d))
- refactor: when uid is passed in externally, uid is no longer stored ([2c5c84c](https://github.com/boxtoys/Aegis/commit/2c5c84c))
- refactor: wrap Date.now() ([b543838](https://github.com/boxtoys/Aegis/commit/b543838))
- docs: 更新性能插件文档，添加 SPA_LOAD 指标及其配置选项 ([98c1c6d](https://github.com/boxtoys/Aegis/commit/98c1c6d))
- docs: add a non-proxy xhr onerror comment ([9101b68](https://github.com/boxtoys/Aegis/commit/9101b68))
- docs: add action.md doc ([04298e0](https://github.com/boxtoys/Aegis/commit/04298e0))
- docs: add api plugin doc ([047a657](https://github.com/boxtoys/Aegis/commit/047a657))
- docs: add BlankScreen and Resource Error and pulgiin doc ([5932459](https://github.com/boxtoys/Aegis/commit/5932459))
- docs: add bridge plugin doc ([9b8d0b1](https://github.com/boxtoys/Aegis/commit/9b8d0b1))
- docs: add comments ([232cbf4](https://github.com/boxtoys/Aegis/commit/232cbf4))
- docs: add Console plugin doc ([29dcd63](https://github.com/boxtoys/Aegis/commit/29dcd63))
- docs: add Convention doc ([290a76e](https://github.com/boxtoys/Aegis/commit/290a76e))
- docs: add data structure doc ([709b120](https://github.com/boxtoys/Aegis/commit/709b120))
- docs: add design doc ([fb5aa17](https://github.com/boxtoys/Aegis/commit/fb5aa17))
- docs: add detect vue router comments ([f0a37e7](https://github.com/boxtoys/Aegis/commit/f0a37e7))
- docs: add Device plugin doc ([ac9e42a](https://github.com/boxtoys/Aegis/commit/ac9e42a))
- docs: add documentation for the largePictureInspect plugin ([add4fc6](https://github.com/boxtoys/Aegis/commit/add4fc6))
- docs: add example ([99a0410](https://github.com/boxtoys/Aegis/commit/99a0410))
- docs: add HTTP plugin doc ([77b764a](https://github.com/boxtoys/Aegis/commit/77b764a))
- docs: add intro \& quick-start docs ([2e277c9](https://github.com/boxtoys/Aegis/commit/2e277c9))
- docs: add PageView plugin doc ([f0fa695](https://github.com/boxtoys/Aegis/commit/f0fa695))
- docs: add Pixel plugin doc ([c4aafeb](https://github.com/boxtoys/Aegis/commit/c4aafeb))
- docs: add plugin doc ([376387d](https://github.com/boxtoys/Aegis/commit/376387d))
- docs: add Precollect plugin doc ([3250e58](https://github.com/boxtoys/Aegis/commit/3250e58))
- docs: add preload example ([6b0178b](https://github.com/boxtoys/Aegis/commit/6b0178b))
- docs: add release step ([0d01bfc](https://github.com/boxtoys/Aegis/commit/0d01bfc))
- docs: add response clone comment ([c2858ef](https://github.com/boxtoys/Aegis/commit/c2858ef))
- docs: add sdk config docs ([67abe36](https://github.com/boxtoys/Aegis/commit/67abe36))
- docs: add type definition ([6442207](https://github.com/boxtoys/Aegis/commit/6442207))
- docs: delete comments ([9b9210f](https://github.com/boxtoys/Aegis/commit/9b9210f))
- docs: deploy ([ae2829a](https://github.com/boxtoys/Aegis/commit/ae2829a))
- docs: example enable gzip compress ([48cc6eb](https://github.com/boxtoys/Aegis/commit/48cc6eb))
- docs: example support combo static assets ([ee3025d](https://github.com/boxtoys/Aegis/commit/ee3025d))
- docs: homepage ui ([65c2d82](https://github.com/boxtoys/Aegis/commit/65c2d82))
- docs: init vitepress ([b8e1eb8](https://github.com/boxtoys/Aegis/commit/b8e1eb8))
- docs: jsError ([da97b45](https://github.com/boxtoys/Aegis/commit/da97b45))
- docs: jsError ([a9906e3](https://github.com/boxtoys/Aegis/commit/a9906e3))
- docs: optimize docs style ([66358e2](https://github.com/boxtoys/Aegis/commit/66358e2))
- docs: optimize docs style ([e2256f1](https://github.com/boxtoys/Aegis/commit/e2256f1))
- docs: pageview \& jsError plugin docs add vueCheck option description ([a85f744](https://github.com/boxtoys/Aegis/commit/a85f744))
- docs: publish ([d728a86](https://github.com/boxtoys/Aegis/commit/d728a86))
- docs: review ([16ea99e](https://github.com/boxtoys/Aegis/commit/16ea99e))
- docs: review ([01ddba9](https://github.com/boxtoys/Aegis/commit/01ddba9))
- docs: review ([b8417c2](https://github.com/boxtoys/Aegis/commit/b8417c2))
- docs: review ([cc1b487](https://github.com/boxtoys/Aegis/commit/cc1b487))
- docs: sourcemap upload ([f097041](https://github.com/boxtoys/Aegis/commit/f097041))
- docs: sync of document with code ([8bfe5d8](https://github.com/boxtoys/Aegis/commit/8bfe5d8))
- docs: update documentation links in README.md ([5b2d440](https://github.com/boxtoys/Aegis/commit/5b2d440))
- docs: update documentation links in README.md ([7f1165f](https://github.com/boxtoys/Aegis/commit/7f1165f))
- docs: update http document ([166366a](https://github.com/boxtoys/Aegis/commit/166366a))
- docs: update pageview plugin document ([ad59be3](https://github.com/boxtoys/Aegis/commit/ad59be3))
- docs: update README.md with simplified release instructions ([90d38ef](https://github.com/boxtoys/Aegis/commit/90d38ef))
- docs: update: resourceErro.md ([38a10cb](https://github.com/boxtoys/Aegis/commit/38a10cb))
- test: add 'correct request, but response.text() exception' cases ([564040e](https://github.com/boxtoys/Aegis/commit/564040e))
- test: add Aegis test cases ([6dd6cdb](https://github.com/boxtoys/Aegis/commit/6dd6cdb))
- test: add bridge test cases ([26a4097](https://github.com/boxtoys/Aegis/commit/26a4097))
- test: add cases ([4588890](https://github.com/boxtoys/Aegis/commit/4588890))
- test: add cases ([56eed8f](https://github.com/boxtoys/Aegis/commit/56eed8f))
- test: add ConfigManager test cases ([9489386](https://github.com/boxtoys/Aegis/commit/9489386))
- test: add DOMException case ([c1b354a](https://github.com/boxtoys/Aegis/commit/c1b354a))
- test: add error cases ([1db964a](https://github.com/boxtoys/Aegis/commit/1db964a))
- test: add http test cases ([a7da4b3](https://github.com/boxtoys/Aegis/commit/a7da4b3))
- test: add integrations tearDown case ([0c9786f](https://github.com/boxtoys/Aegis/commit/0c9786f))
- test: add istanbul ignore comment ([09bb2ae](https://github.com/boxtoys/Aegis/commit/09bb2ae))
- test: add loader case ([17f459b](https://github.com/boxtoys/Aegis/commit/17f459b))
- test: add multiple aegis case ([0b87389](https://github.com/boxtoys/Aegis/commit/0b87389))
- test: add multiple instance case ([0bcd030](https://github.com/boxtoys/Aegis/commit/0bcd030))
- test: add mutiple aegis instance cases ([381b06e](https://github.com/boxtoys/Aegis/commit/381b06e))
- test: add Object.assign polyfill test case ([76b1d47](https://github.com/boxtoys/Aegis/commit/76b1d47))
- test: add pageview test cases ([d7d653e](https://github.com/boxtoys/Aegis/commit/d7d653e))
- test: add pageview test cases for spy calls and assertions ([fa4f334](https://github.com/boxtoys/Aegis/commit/fa4f334))
- test: add promise error is custom event sence ([9523714](https://github.com/boxtoys/Aegis/commit/9523714))
- test: add request test cases ([802c497](https://github.com/boxtoys/Aegis/commit/802c497))
- test: add server mock for unit testing ([01934ea](https://github.com/boxtoys/Aegis/commit/01934ea))
- test: add storage test cases ([ad1166f](https://github.com/boxtoys/Aegis/commit/ad1166f))
- test: add test cases ([f97d919](https://github.com/boxtoys/Aegis/commit/f97d919))
- test: add transports example ([86c003f](https://github.com/boxtoys/Aegis/commit/86c003f))
- test: add unit tests for utils and core classes ([9a6fdfc](https://github.com/boxtoys/Aegis/commit/9a6fdfc))
- test: add xhr test cases ([ce01cb2](https://github.com/boxtoys/Aegis/commit/ce01cb2))
- test: change case assets ([c150ecc](https://github.com/boxtoys/Aegis/commit/c150ecc))
- test: change example code ([e4681b4](https://github.com/boxtoys/Aegis/commit/e4681b4))
- test: cleanup logs ([57e2839](https://github.com/boxtoys/Aegis/commit/57e2839))
- test: completing test cases ([5652a17](https://github.com/boxtoys/Aegis/commit/5652a17))
- test: fix formatting of noop function in utils.test.ts ([69a175f](https://github.com/boxtoys/Aegis/commit/69a175f))
- test: fix test case writing ([3c9dc9e](https://github.com/boxtoys/Aegis/commit/3c9dc9e))
- test: hide the console error log ([69a023a](https://github.com/boxtoys/Aegis/commit/69a023a))
- test: hide vitest unhandled error info ([1e5102a](https://github.com/boxtoys/Aegis/commit/1e5102a))
- test: optimize ([d1d3312](https://github.com/boxtoys/Aegis/commit/d1d3312))
- test: organize the directory structure ([bab4a12](https://github.com/boxtoys/Aegis/commit/bab4a12))
- test: remove sinon mock package ([c8a2356](https://github.com/boxtoys/Aegis/commit/c8a2356))
- test: remove useless code ([fbf164b](https://github.com/boxtoys/Aegis/commit/fbf164b))
- test: test cases split ([c37dff3](https://github.com/boxtoys/Aegis/commit/c37dff3))
- test: update package.json with new test and mock scripts ([038b077](https://github.com/boxtoys/Aegis/commit/038b077))
- test: update test cases for issues caused by vitest upgrade ([623a170](https://github.com/boxtoys/Aegis/commit/623a170))
- test: update test script and server shutdown handling ([cd784fa](https://github.com/boxtoys/Aegis/commit/cd784fa))
- test: vitest can't test unhandledrejection scenario ([6f48fc8](https://github.com/boxtoys/Aegis/commit/6f48fc8))
- perf: code optimize ([c12701f](https://github.com/boxtoys/Aegis/commit/c12701f))
- perf: compressing the built code ([d18cd87](https://github.com/boxtoys/Aegis/commit/d18cd87))
- revert: code optimize ([ccc3da1](https://github.com/boxtoys/Aegis/commit/ccc3da1))
- revert: organize the directory structure ([8b756d6](https://github.com/boxtoys/Aegis/commit/8b756d6))
- style: format code ([5266ce7](https://github.com/boxtoys/Aegis/commit/5266ce7))

## <small>1.0.67 (2025-08-06)</small>

- refactor: 优化 onSPA_LOAD 函数中的数组操作，使用 filter 方法替代 splice 以提高代码可读性和性能 ([869fd47](https://github.com/boxtoys/Aegis/commit/869fd47))

## <small>1.0.65 (2025-07-30)</small>

- refactor: refactor the request functions, unify the request parameter structure ([b9fadc9](https://github.com/boxtoys/Aegis/commit/b9fadc9))

## <small>1.0.64 (2025-07-10)</small>

- chore: code optimize ([c1a1087](https://github.com/boxtoys/Aegis/commit/c1a1087))

## <small>1.0.63 (2025-07-10)</small>

- fix: immediate option lost ([e4b3686](https://github.com/boxtoys/Aegis/commit/e4b3686))

## <small>1.0.62 (2025-07-09)</small>

- feat: add the immediate attribute to support sending immediately in batch mode ([c4a1a24](https://github.com/boxtoys/Aegis/commit/c4a1a24))
- chore: page unload use visibilitychange event ([987e6c5](https://github.com/boxtoys/Aegis/commit/987e6c5))

## <small>1.0.61 (2025-06-17)</small>

- chore: when using xhr, temporarily do not perform retry logic ([80a3794](https://github.com/boxtoys/Aegis/commit/80a3794))

## <small>1.0.60 (2025-06-16)</small>

- fix: ensure data flow using asynchronous methods ([9a7c3ec](https://github.com/boxtoys/Aegis/commit/9a7c3ec))
- fix: test cases network error ([3431567](https://github.com/boxtoys/Aegis/commit/3431567))

## <small>1.0.59 (2025-06-13)</small>

- refactor: remove xhr onerror handler ([8fdd7ad](https://github.com/boxtoys/Aegis/commit/8fdd7ad))
- chore: change env ([2c4a66d](https://github.com/boxtoys/Aegis/commit/2c4a66d))

## <small>1.0.58 (2025-06-12)</small>

- chore: code optimize ([3d379cf](https://github.com/boxtoys/Aegis/commit/3d379cf))
- chore: optimize mock plugin error display ([d5fb83a](https://github.com/boxtoys/Aegis/commit/d5fb83a))

## <small>1.0.57 (2025-05-26)</small>

- feat: add endTime property to SPA load records and update duration calculations ([1eabc97](https://github.com/boxtoys/Aegis/commit/1eabc97))

## <small>1.0.56 (2025-05-16)</small>

- fix: plugins is undefined array, plugins[i].name is error ([491f70e](https://github.com/boxtoys/Aegis/commit/491f70e))
- docs: add release step ([0d01bfc](https://github.com/boxtoys/Aegis/commit/0d01bfc))

## <small>1.0.55 (2025-05-13)</small>

- fix: fix test case ([1f0cef8](https://github.com/boxtoys/Aegis/commit/1f0cef8))
- feat: improve BrowserTime field ([2603e3d](https://github.com/boxtoys/Aegis/commit/2603e3d))

## <small>1.0.54 (2025-04-27)</small>

- fix: fix spa timeout issue ([35e492f](https://github.com/boxtoys/Aegis/commit/35e492f))
- chore: format doc ([60826c0](https://github.com/boxtoys/Aegis/commit/60826c0))
- feat: add feature plugin doc file ([5e47487](https://github.com/boxtoys/Aegis/commit/5e47487))

## <small>1.0.53 (2025-03-17)</small>

- chore: add feature polyfill ([2bdfaab](https://github.com/boxtoys/Aegis/commit/2bdfaab))

## <small>1.0.52 (2025-03-10)</small>

- chore: adjust changelog ([5135517](https://github.com/boxtoys/Aegis/commit/5135517))
- chore: browser common add os common field ([891e950](https://github.com/boxtoys/Aegis/commit/891e950))
- chore: feature plugin support detect image strategy ([2d51260](https://github.com/boxtoys/Aegis/commit/2d51260))
- feat: add browser timing for spa load ([971f5ba](https://github.com/boxtoys/Aegis/commit/971f5ba))
- feat: stash code ([7c5e50f](https://github.com/boxtoys/Aegis/commit/7c5e50f))
- feat: update spa_load and test cases ([bc7af77](https://github.com/boxtoys/Aegis/commit/bc7af77))
- test: cleanup logs ([57e2839](https://github.com/boxtoys/Aegis/commit/57e2839))
- fix: multiple instances ([af3a672](https://github.com/boxtoys/Aegis/commit/af3a672))
- 1.0.51 ([69d6656](https://github.com/boxtoys/Aegis/commit/69d6656))

## <small>1.0.51 (2025-03-01)</small>

- chore: feature plugin support detect image strategy ([2d51260](https://github.com/boxtoys/Aegis/commit/2d51260))

## <small>1.0.50 (2025-02-28)</small>

- chore: aegis core add reportUrl field ([cdabfba](https://github.com/boxtoys/Aegis/commit/cdabfba))

## <small>1.0.49 (2025-02-28)</small>

- chore: add cssSupports polyfill ([3d774b9](https://github.com/boxtoys/Aegis/commit/3d774b9))
- chore: add feature plugin packing process ([8f5fc9d](https://github.com/boxtoys/Aegis/commit/8f5fc9d))
- chore: feature plugin support remote feature list ([da021e7](https://github.com/boxtoys/Aegis/commit/da021e7))
- feat: add detect browser feature plugin ([2cb04bc](https://github.com/boxtoys/Aegis/commit/2cb04bc))

## <small>1.0.48 (2025-02-25)</small>

- feat: improve performance init timing ([0efe026](https://github.com/boxtoys/Aegis/commit/0efe026))

## <small>1.0.47 (2025-02-21)</small>

- feat: remove unused code ([20a6bb9](https://github.com/boxtoys/Aegis/commit/20a6bb9))

## <small>1.0.46 (2025-02-21)</small>

- feat: improve spaload crossorigin link compatibility ([3e39370](https://github.com/boxtoys/Aegis/commit/3e39370))
- feat: remove spaLoad ios judge ([e1f9e29](https://github.com/boxtoys/Aegis/commit/e1f9e29))
- chore: add global variable: BrowserTiming ([6ed4cb7](https://github.com/boxtoys/Aegis/commit/6ed4cb7))
- chore: use BrowserTiming data ([2036628](https://github.com/boxtoys/Aegis/commit/2036628))
- refactor: add get plugin method \& plugin export option ([3112fa6](https://github.com/boxtoys/Aegis/commit/3112fa6))
- refactor: blankScreen plugin use pageview plugin mode option ([77cf7e6](https://github.com/boxtoys/Aegis/commit/77cf7e6))
- refactor: perf SPA_LOAD plugin use pageview plugin mode option ([4431493](https://github.com/boxtoys/Aegis/commit/4431493))

## <small>1.0.45 (2025-02-19)</small>

- feat: improve spa load ([603c0a9](https://github.com/boxtoys/Aegis/commit/603c0a9))

## <small>1.0.44 (2025-02-18)</small>

- fix: fix test case ([197477c](https://github.com/boxtoys/Aegis/commit/197477c))
- feat: improve initial script detect ([7beb80c](https://github.com/boxtoys/Aegis/commit/7beb80c))

## <small>1.0.43 (2025-02-18)</small>

- fix: fix issue ([38b6598](https://github.com/boxtoys/Aegis/commit/38b6598))
- fix: fix test case ([5ee4d5a](https://github.com/boxtoys/Aegis/commit/5ee4d5a))
- feat: add script detect for spa load ([c163606](https://github.com/boxtoys/Aegis/commit/c163606))

## <small>1.0.42 (2025-02-13)</small>

- fix: ignore background img detect on ios ([3b3b49c](https://github.com/boxtoys/Aegis/commit/3b3b49c))
- fix: vue3 vue-router first time using replaceState for jump ([e0b3ee6](https://github.com/boxtoys/Aegis/commit/e0b3ee6))
- feat: modify spaload check gap, change gap from 100ms to 300ms ([f5479d1](https://github.com/boxtoys/Aegis/commit/f5479d1))

## <small>1.0.41 (2025-02-12)</small>

- chore: code styling ([8bf5096](https://github.com/boxtoys/Aegis/commit/8bf5096))
- feat: add bridge observe ([5cba719](https://github.com/boxtoys/Aegis/commit/5cba719))
- feat: add stop judge ([1935a70](https://github.com/boxtoys/Aegis/commit/1935a70))
- feat: improve test case(add bridge request case) ([fdaed85](https://github.com/boxtoys/Aegis/commit/fdaed85))

## <small>1.0.40 (2025-01-16)</small>

- chore: refactor: refactor fetch and xhr utils to support custom fetch and xhr ([1b56b46](https://github.com/boxtoys/Aegis/commit/1b56b46))

## <small>1.0.39 (2025-01-06)</small>

- feat: improve spa load ([3cfc22a](https://github.com/boxtoys/Aegis/commit/3cfc22a))

## <small>1.0.38 (2025-01-06)</small>

- feat: improve node display none case ([e1b59d1](https://github.com/boxtoys/Aegis/commit/e1b59d1))
- chore: changelog ([1e4d595](https://github.com/boxtoys/Aegis/commit/1e4d595))

## <small>1.0.37 (2025-01-03)</small>

- feat: improve spa load ([21f9d3d](https://github.com/boxtoys/Aegis/commit/21f9d3d))
- feat: spa_load add records params ([a2428f0](https://github.com/boxtoys/Aegis/commit/a2428f0))
- fix: rollupError: Unterminated string constant ([15c7ae7](https://github.com/boxtoys/Aegis/commit/15c7ae7))
- fix: url case issue ([978ec4b](https://github.com/boxtoys/Aegis/commit/978ec4b))
- chore: adjust changelog ([c05631e](https://github.com/boxtoys/Aegis/commit/c05631e))

## <small>1.0.36 (2025-01-01)</small>

- fix: url case issue ([978ec4b](https://github.com/boxtoys/Aegis/commit/978ec4b))
- chore: adjust changelog ([c05631e](https://github.com/boxtoys/Aegis/commit/c05631e))

## <small>1.0.35 (2025-01-01)</small>

- feat: spa_load add records params ([a2428f0](https://github.com/boxtoys/Aegis/commit/a2428f0))
- fix: rollupError: Unterminated string constant ([15c7ae7](https://github.com/boxtoys/Aegis/commit/15c7ae7))

## <small>1.0.34 (2024-12-31)</small>

- refactor: code optimize ([9ba831d](https://github.com/boxtoys/Aegis/commit/9ba831d))
- feat: improve spa_load ([614600b](https://github.com/boxtoys/Aegis/commit/614600b))

## <small>1.0.33 (2024-12-30)</small>

- feat: ignore base64url request report ([7b74d85](https://github.com/boxtoys/Aegis/commit/7b74d85))
- fix: error when the plugin is not available ([a494ecf](https://github.com/boxtoys/Aegis/commit/a494ecf))
- chore: add aop use case scenarios ([974d621](https://github.com/boxtoys/Aegis/commit/974d621))
- chore: clear changelog ([56b9d3d](https://github.com/boxtoys/Aegis/commit/56b9d3d))

## <small>1.0.32 (2024-12-27)</small>

- refactor: aop supports multiple layers of proxies ([736b1ae](https://github.com/boxtoys/Aegis/commit/736b1ae))
- chore: add test cases ([ceceda6](https://github.com/boxtoys/Aegis/commit/ceceda6))

## <small>1.0.31 (2024-12-24)</small>

- chore: spaload support ignoreUrls params ([fe56815](https://github.com/boxtoys/Aegis/commit/fe56815))
- chore: update build script ([64419ee](https://github.com/boxtoys/Aegis/commit/64419ee))
- test: add test cases ([f97d919](https://github.com/boxtoys/Aegis/commit/f97d919))
- fix: rollup build error ([50a8d30](https://github.com/boxtoys/Aegis/commit/50a8d30))

## <small>1.0.30 (2024-12-23)</small>

- fix: spa load reset done ([a5f98f2](https://github.com/boxtoys/Aegis/commit/a5f98f2))

## <small>1.0.29 (2024-12-23)</small>

- chore: modify build script ([bcb7336](https://github.com/boxtoys/Aegis/commit/bcb7336))

## <small>1.0.28 (2024-12-23)</small>

- feat: add spa_load ([aada673](https://github.com/boxtoys/Aegis/commit/aada673))
- chore: add dom observer method ([5c6c835](https://github.com/boxtoys/Aegis/commit/5c6c835))
- chore: add generic field: viewId ([ab1d28f](https://github.com/boxtoys/Aegis/commit/ab1d28f))
- refactor: extract api observer for use by other plugins ([9957f59](https://github.com/boxtoys/Aegis/commit/9957f59))
- refactor: extract route observer for use by other plugins ([de2670b](https://github.com/boxtoys/Aegis/commit/de2670b))
- refactor: optimize the performance of MutationObserver ([d055cbc](https://github.com/boxtoys/Aegis/commit/d055cbc))
- fix: rollup build error: Unterminated string constant ([59b2ac3](https://github.com/boxtoys/Aegis/commit/59b2ac3))
- fix: ts error ([4b89d52](https://github.com/boxtoys/Aegis/commit/4b89d52))

## <small>1.0.27 (2024-11-25)</small>

- feat: update ([409e131](https://github.com/boxtoys/Aegis/commit/409e131))
- 白屏插件新增预不合法窗口逻辑 ([d520d18](https://github.com/boxtoys/Aegis/commit/d520d18))

## <small>1.0.26 (2024-11-15)</small>

- fix: headers is not defined ([7825c95](https://github.com/boxtoys/Aegis/commit/7825c95))

## <small>1.0.25 (2024-11-13)</small>

- 新增资源插件transfersize ([84e74be](https://github.com/boxtoys/Aegis/commit/84e74be))
- feat: 修改transferSize 逻辑 ([3c07e94](https://github.com/boxtoys/Aegis/commit/3c07e94))

## <small>1.0.24 (2024-11-12)</small>

- feat: 更新 ([85db80f](https://github.com/boxtoys/Aegis/commit/85db80f))
- feat: 更新 ([3ffba0c](https://github.com/boxtoys/Aegis/commit/3ffba0c))
- docs: sourcemap upload ([f097041](https://github.com/boxtoys/Aegis/commit/f097041))

## <small>1.0.23 (2024-11-07)</small>

- chore: add transport attribute check ([3a70a06](https://github.com/boxtoys/Aegis/commit/3a70a06))

## <small>1.0.22 (2024-11-07)</small>

- chore: preset static methods to prevent errors when source not loaded ([3c4b5ed](https://github.com/boxtoys/Aegis/commit/3c4b5ed))
- chore: preset transport static methods to prevent errors when source not loaded ([32c609e](https://github.com/boxtoys/Aegis/commit/32c609e))
- chore: publish ([1337d53](https://github.com/boxtoys/Aegis/commit/1337d53))

## <small>1.0.21 (2024-11-06)</small>

- refactor: optimize build IIFE package script ([9677214](https://github.com/boxtoys/Aegis/commit/9677214))
- refactor: optimize code ([802ba4c](https://github.com/boxtoys/Aegis/commit/802ba4c))
- refactor: optimize code ([54db2f9](https://github.com/boxtoys/Aegis/commit/54db2f9))
- feat: device plugin add vendor model ([2b888e3](https://github.com/boxtoys/Aegis/commit/2b888e3))
- feat: fix getElementUrl function for SVGAElement ([da70d23](https://github.com/boxtoys/Aegis/commit/da70d23))
- fix: docs error ([5c6e973](https://github.com/boxtoys/Aegis/commit/5c6e973))

## <small>1.0.20 (2024-10-25)</small>

- chore: blankScreen plugin support ignoreUrls parameter ([d89275f](https://github.com/boxtoys/Aegis/commit/d89275f))
- chore: bridge plugin support ignoreUrls parameter ([90e9f50](https://github.com/boxtoys/Aegis/commit/90e9f50))
- chore: optimize combo script ([1b5d40d](https://github.com/boxtoys/Aegis/commit/1b5d40d))
- chore: update changelog ([14b29b3](https://github.com/boxtoys/Aegis/commit/14b29b3))

## <small>1.0.19 (2024-10-17)</small>

- chore: the pageview plugin uses absolute paths by default ([cf11425](https://github.com/boxtoys/Aegis/commit/cf11425))
- docs: add type definition ([6442207](https://github.com/boxtoys/Aegis/commit/6442207))
- docs: pageview \& jsError plugin docs add vueCheck option description ([a85f744](https://github.com/boxtoys/Aegis/commit/a85f744))
- chore: the jsError plugin supports the vueCheck option to disable vue.js detection ([6f6cf6a](https://github.com/boxtoys/Aegis/commit/6f6cf6a))
- chore: the pageview plugin supports the vueCheck option to disable vue.js detection ([bd647c1](https://github.com/boxtoys/Aegis/commit/bd647c1))
- chore: largePictureInspect plugin support dpr \& reportMode options ([25f7186](https://github.com/boxtoys/Aegis/commit/25f7186))
- docs: add documentation for the largePictureInspect plugin ([add4fc6](https://github.com/boxtoys/Aegis/commit/add4fc6))
- chore: add a packaging process to the largePictureInspect plugin ([ea769c4](https://github.com/boxtoys/Aegis/commit/ea769c4))
- chore: from single dom to mutilple dom ([e7f7d97](https://github.com/boxtoys/Aegis/commit/e7f7d97))
- chore: scenarios where the largePictureInspect plugin supports dynamic background images ([9511ffb](https://github.com/boxtoys/Aegis/commit/9511ffb))
- feat: support largePictureInspect plugin ([0bf8d0b](https://github.com/boxtoys/Aegis/commit/0bf8d0b))
- chore: sdk upload address using environment variables ([f19c022](https://github.com/boxtoys/Aegis/commit/f19c022))
- chore: update changelog ([5307857](https://github.com/boxtoys/Aegis/commit/5307857))

## <small>1.0.18 (2024-10-12)</small>

- chore: config the changelog ([be4441f](https://github.com/boxtoys/Aegis/commit/be4441f))
- refactor: when uid is passed in externally, uid is no longer stored ([2c5c84c](https://github.com/boxtoys/Aegis/commit/2c5c84c))
