{"name": "@clover/aegis", "author": "2046", "version": "1.0.71", "description": "Front-end Monitoring System SDK", "dependencies": {"@babel/runtime": "7.25.7", "@babel/runtime-corejs3": "7.25.7"}, "devDependencies": {"@babel/core": "7.25.7", "@babel/plugin-transform-runtime": "7.25.7", "@babel/preset-env": "7.25.7", "@commitlint/cli": "17.8.1", "@commitlint/config-conventional": "17.8.1", "@inquirer/prompts": "5.5.0", "@playwright/test": "1.47.2", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "25.0.8", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "15.3.0", "@rollup/plugin-replace": "5.0.7", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-virtual": "3.0.2", "@types/express": "4.17.21", "@types/fs-extra": "11.0.4", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@vitest/browser": "1.6.0", "@vitest/coverage-istanbul": "1.6.0", "@vitest/ui": "1.6.0", "ali-oss": "6.21.0", "axios": "1.7.7", "cli-table3": "0.6.5", "colors": "1.4.0", "combo-handler": "1.1.0", "commitizen": "4.3.1", "compression": "1.7.4", "conventional-changelog-cli": "5.0.0", "cors": "2.8.5", "cz-customizable": "7.2.1", "ejs": "3.1.10", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "express": "4.21.0", "filesize": "10.1.6", "fs-extra": "11.2.0", "gh-pages": "6.1.1", "gzip-size": "7.0.0", "hasha": "6.0.0", "husky": "8.0.3", "lint-staged": "14.0.1", "nyc": "15.1.0", "ora": "8.1.0", "playwright": "1.47.2", "prettier": "3.3.3", "rollup": "3.29.5", "rollup-plugin-delete": "2.1.0", "rollup-plugin-node-externals": "6.1.2", "rollup-plugin-typescript2": "0.35.0", "semver": "7.6.3", "terser": "5.34.1", "tslib": "2.7.0", "tsx": "3.14.0", "typescript": "5.2.2", "vitepress": "1.3.4", "vitest": "1.6.0", "webdriverio": "9.11.0", "zx": "8.1.8"}, "scripts": {"prepare": "husky install", "commit": "npx git-cz", "build:loader": "npx tsx ./scripts/loader.build.ts", "build:cjs": "npx rollup -c ./rollup.config.cjs.mjs", "build:esm": "npx rollup -c ./rollup.config.esm.mjs", "build:iife": "npx rollup -c ./rollup.config.iife.mjs", "build:iife:compress": "COMPRESS=true npx rollup -c ./rollup.config.iife.mjs", "build": "npm run build:cjs && npm run build:esm && npm run build:iife && npm run build:loader", "pretest": "nohup npx tsx ./test/mocks/server.ts >/dev/null 2>&1 &", "test": "rm -rf coverage && npm run test:node && npm run test:browser || true", "test:browser": "npx vitest run --config vitest.config.browser.mjs && mv coverage/browser/coverage-final.json coverage/coverage-browser.json && rm -rf coverage/browser", "test:node": "npx vitest run --config vitest.config.node.mjs && mv coverage/node/coverage-final.json coverage/coverage-node.json && rm -rf coverage/node", "posttest": "npx tsx ./test/mocks/stop.ts && npm run cover:merge", "prestart": "npm run build:iife:compress && npm run build:loader", "start": "npx tsx ./example/server.ts", "combo": "npx tsx --env-file=.env ./scripts/combo.ts", "upload": "npx tsx --env-file=.env ./scripts/upload.ts", "upload:sdk": "npx tsx --env-file=.env ./scripts/upload-sdk.ts", "cover:merge": "npx nyc merge coverage .nyc_output/coverage.json && npx nyc report --reporter=text --reporter=html && rm -rf .nyc_output", "docs:dev": "vitepress dev docs --host 0.0.0.0", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "docs:deploy": "vitepress build docs && npx gh-pages -d docs/.vitepress/dist", "changelog": "npx conventional-changelog -p angular -i CHANGELOG.md -s", "version": "npm run changelog && git add CHANGELOG.md"}, "type": "module", "types": "./types/index.d.ts", "main": "./dist/cjs/index.cjs", "files": ["dist", "types", "docs"], "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.cjs"}}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}}