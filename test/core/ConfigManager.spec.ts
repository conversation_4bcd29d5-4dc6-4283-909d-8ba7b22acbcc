import { describe, expect, test } from 'vitest'
import ConfigManager from '../../src/core/ConfigManager'
import ConsoleTransport from '../../src/transports/console'

describe('ConfigManager', () => {
  test('configManager options validate', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3',
      uid: 'e654c45fd5c34fb2b97c2ed8e8c766f0'
    })

    expect(configManager.getConfigValue('id')).toEqual('a8f9f988a7670995a8098c41e9d38fb3')
    expect(configManager.getConfigValue('uid')).toEqual('e654c45fd5c34fb2b97c2ed8e8c766f0')
    expect(configManager.getConfigValue('env')).toEqual('prod')
    expect(configManager.getConfigValue('sample')).toEqual(1)
    expect(configManager.getConfigValue('release')).toEqual('')
    expect(configManager.getConfigValue('integrations')).toEqual([])

    const configManager2 = new ConfigManager({} as any)

    expect(configManager2.getConfigValue('id')).toEqual('')
  })

  test('configManager updateConfig', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3'
    })

    expect(configManager.getConfigValue('uid')!.length).toEqual(32)

    configManager.updateConfig({
      env: 'test',
      sample: 0.5,
      release: '1.0.0',
      integrations: [],
      uid: 'e654c45fd5c34fb2b97c2ed8e8c766f0'
    })

    expect(configManager.getConfigValue('id')).toEqual('a8f9f988a7670995a8098c41e9d38fb3')
    expect(configManager.getConfigValue('uid')).toEqual('e654c45fd5c34fb2b97c2ed8e8c766f0')
    expect(configManager.getConfigValue('env')).toEqual('test')
    expect(configManager.getConfigValue('sample')).toEqual(0.5)
    expect(configManager.getConfigValue('release')).toEqual('1.0.0')
    expect(configManager.getConfigValue('integrations')).toEqual([])
  })

  test('configManager updateConfig with invalid options', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3',
      uid: 'e654c45fd5c34fb2b97c2ed8e8c766f0'
    })

    configManager.updateConfig('tesst' as any)
    expect(configManager.getConfigValue('id')).toEqual('a8f9f988a7670995a8098c41e9d38fb3')

    configManager.updateConfig({
      id: 'a8f9f988a7670995a8098c41e9d38fb1'
    })
    expect(configManager.getConfigValue('id')).toEqual('a8f9f988a7670995a8098c41e9d38fb3')
  })

  test('configManager add transport', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3'
    })

    const transport = new ConsoleTransport('http://example.com/report.gif')

    configManager.addTransport(transport)

    expect(configManager.getConfigValue('transports')).toEqual([transport])
  })

  test('configManager add transport with invalid transport', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb1'
    })

    configManager.addTransport({} as any)

    expect(configManager.getConfigValue('transports')).toEqual([])
  })

  test('configManager remove transport', () => {
    const configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3'
    })

    const transport = new ConsoleTransport('http://example.com/report.gif')
    const transport2 = new ConsoleTransport('http://example.com/report.gif')

    configManager.addTransport(transport)

    expect(configManager.getConfigValue('transports')).toEqual([transport])

    configManager.removeTransport(transport)
    configManager.removeTransport(transport2)
    configManager.removeTransport(null as any)

    expect(configManager.getConfigValue('transports')).toEqual([])
  })

  test('configManager get platform', () => {
    let configManager = new ConfigManager({
      id: 'a8f9f988a7670995a8098c41e9d38fb3'
    })

    expect(configManager.getPlatform()).toEqual('web')

    configManager = new ConfigManager({
      id: '00f9f988a7670995a8098c41e9d38fb1'
    })

    expect(configManager.getPlatform()).toEqual('web')

    configManager = new ConfigManager({
      id: '01f9f988a7670995a8098c41e9d38fb1'
    })

    expect(configManager.getPlatform()).toEqual('nodejs')

    configManager = new ConfigManager({
      id: '02f9f988a7670995a8098c41e9d38fb1'
    })

    expect(configManager.getPlatform()).toEqual('miniapp')

    configManager = new ConfigManager({
      id: '03f9f988a7670995a8098c41e9d38fb1'
    })

    expect(configManager.getPlatform()).toEqual('rn')

    configManager = new ConfigManager({
      id: '04f9f988a7670995a8098c41e9d38fb1'
    })

    expect(configManager.getPlatform()).toEqual('web')

    configManager = new ConfigManager({
      id: ''
    })

    expect(configManager.getPlatform()).toEqual('web')

    configManager = null as any
  })
})
