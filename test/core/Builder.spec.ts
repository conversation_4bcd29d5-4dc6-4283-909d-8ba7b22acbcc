import { describe, expect, test } from 'vitest'
import { getBrowserCommonId } from '../../src/core/builder'

describe('Builder', () => {
  test('getBrowserCommonId', () => {
    expect(getBrowserCommonId('')).toEqual('')
    expect(getBrowserCommonId('https://')).toEqual('https://')
    expect(getBrowserCommonId('https://example.com')).toEqual('https://example.com')
    expect(getBrowserCommonId('http://192.168.1.1/path')).toEqual('http://192.168.1.1/path')
    expect(getBrowserCommonId('https://example.com#hash')).toEqual('https://example.com#hash')
    expect(getBrowserCommonId('https://example.com?query=test')).toEqual('https://example.com')
    expect(getBrowserCommonId('https://example.com/path#')).toEqual('https://example.com/path#')
    expect(getBrowserCommonId('https://example.com/hello/')).toEqual('https://example.com/hello/')
    expect(getBrowserCommonId('https://example.com/路径/测试')).toEqual('https://example.com/路径/测试')
    expect(getBrowserCommonId('https://example.com:8443/path')).toEqual('https://example.com:8443/path')
    expect(getBrowserCommonId('https://example.com/hello/abc/def')).toEqual('https://example.com/hello/abc/def')
    expect(getBrowserCommonId('https://example.com//path///subpath')).toEqual('https://example.com//path///subpath')
    expect(getBrowserCommonId('https://example.com/path?q=hello%20world&lang=en')).toEqual('https://example.com/path')
    expect(getBrowserCommonId('https://example.com/hello/abc/def?a=b&c=d')).toEqual('https://example.com/hello/abc/def')
    expect(getBrowserCommonId('https://example.com/path?outer=1&inner=a%3Db%26c%3Dd')).toEqual('https://example.com/path')
    expect(getBrowserCommonId('https://example.com/path#section%20name')).toEqual('https://example.com/path#section%20name')
    expect(getBrowserCommonId('https://example.com/path%20with%20spaces')).toEqual('https://example.com/path%20with%20spaces')
    expect(getBrowserCommonId('https://example.com/path#section1#section2')).toEqual('https://example.com/path#section1#section2')
    expect(getBrowserCommonId('https://example.com/hello/abc/def?a=b&c=d#hash')).toEqual('https://example.com/hello/abc/def#hash')
    expect(getBrowserCommonId('https://example.com/hello/abc/def?a=b&c=d#hash/')).toEqual('https://example.com/hello/abc/def#hash/')
    expect(getBrowserCommonId('https://example.com/hello/abc/def?a=b&c=d#hash/?')).toEqual('https://example.com/hello/abc/def#hash/')
    expect(getBrowserCommonId('https://example.com/hello/abc/def#hash/abc/def')).toEqual('https://example.com/hello/abc/def#hash/abc/def')
    expect(getBrowserCommonId('https://example.com/hello/abc/def/#hash/abc/def')).toEqual('https://example.com/hello/abc/def/#hash/abc/def')
    expect(getBrowserCommonId('https://example.com/hello/abc/def/#hash/abc/def/')).toEqual('https://example.com/hello/abc/def/#hash/abc/def/')
    expect(getBrowserCommonId('https://example.com/hello?param=value#/abc/hasdef?query=test')).toEqual('https://example.com/hello#/abc/hasdef')
    expect(getBrowserCommonId('http://[2001:db8:85a3:8d3:1319:8a2e:370:7348]/path')).toEqual('http://[2001:db8:85a3:8d3:1319:8a2e:370:7348]/path')
  })
})
