import Events from '../../src/core/Events'
import { describe, expect, test, vi } from 'vitest'

describe('Events', () => {
  test('on and emit', () => {
    const spy = vi.fn()
    const event = new Events()

    event.on('event', spy)
    event.emit('event')
    expect(spy.mock.calls.length).toEqual(1)

    event.emit('event')
    event.emit('event')
    event.emit('event')
    event.emit('event')
    expect(spy.mock.calls.length).toEqual(5)
  })

  test('on, then unbind all functions', () => {
    const spy = vi.fn()
    const event = new Events()

    event.on('event', spy)
    event.emit('event')
    expect(spy.mock.calls.length).toEqual(1)

    event.off('event')
    event.emit('event')
    expect(spy.mock.calls.length).toEqual(1)
  })

  test('bind two callbacks, unbind only one', () => {
    const spyA = vi.fn()
    const spyB = vi.fn()
    const event = new Events()

    event.on('event', spyA)
    event.on('event', spyB)
    event.emit('event')
    expect(spyA.mock.calls.length).toEqual(1)
    expect(spyB.mock.calls.length).toEqual(1)

    event.off('event', spyA)
    event.emit('event')
    expect(spyA.mock.calls.length).toEqual(1)
    expect(spyB.mock.calls.length).toEqual(2)
  })

  test('unbind a callback in the midst of it firing', () => {
    const spy = vi.fn()
    const event = new Events()

    function callback() {
      spy()
      event.off('event', callback)
    }

    event.on('event', callback)
    event.emit('event')
    event.emit('event')
    event.emit('event')
    expect(spy.mock.calls.length).toEqual(1)
  })

  test('two binds that unbind themselves', () => {
    const spyA = vi.fn()
    const spyB = vi.fn()
    const event = new Events()

    function incrA() {
      spyA()
      event.off('event', incrA)
    }

    function incrB() {
      spyB()
      event.off('event', incrB)
    }

    event.on('event', incrA)
    event.on('event', incrB)
    event.emit('event')
    event.emit('event')
    event.emit('event')

    expect(spyA.mock.calls.length).toEqual(1)
    expect(spyB.mock.calls.length).toEqual(1)
  })

  test('nested emit with unbind', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()
    const event = new Events()

    function incr1() {
      spy1()
      event.off('event', incr1)
      event.emit('event')
    }

    event.on('event', incr1)
    event.on('event', spy2)
    event.emit('event')
    expect(spy1.mock.calls.length).toEqual(1)
    expect(spy2.mock.calls.length).toEqual(2)
  })

  test('callback list is not altered during emit', () => {
    const spy = vi.fn()
    const event = new Events()

    event
      .on('event', () => {
        event.on('event', spy)
      })
      .emit('event')

    expect(spy.mock.calls.length).toEqual(0)

    event
      .off()
      .on('event', () => {
        event.on('event', spy)
      })
      .on('event', spy)
      .emit('event')

    expect(spy.mock.calls.length).toEqual(1)
  })

  test('if no callback is provided, `on` is a noop', () => {
    expect(() => {
      new Events().on('test', <any>null).emit('test')
    }).not.toThrow()
  })

  test('off is chainable', () => {
    const event = new Events()

    expect(event.off()).toEqual(event)

    event.on('event', () => {})
    expect(event.off()).toEqual(event)

    event.on('event', () => {})
    expect(event.off('event')).toEqual(event)
  })

  test('emit returns callback status', async () => {
    const event = new Events()
    const stub1 = vi.fn()
    const stub2 = vi.fn()

    event.on('a', stub1)

    stub1.mockReturnValue(true)
    expect(event.emit('a')).to.equal(true)

    stub1.mockReturnValue(false)
    expect(event.emit('a')).to.equal(false)

    stub1.mockReturnValue('a')
    expect(event.emit('a')).to.equal('a')

    stub1.mockResolvedValue(true)
    expect(event.emit('a')).to.instanceof(Promise)
    expect(await event.emit('a')).to.equal(true)

    event.off()
    event.on('a', stub1)
    event.on('a', stub2)

    stub1.mockReturnValue(true)
    stub2.mockReturnValue(true)
    expect(event.emit('a')).to.equal(true)

    stub1.mockReturnValue(false)
    stub2.mockReturnValue(true)
    expect(event.emit('a')).to.equal(true)

    stub1.mockReturnValue(true)
    stub2.mockReturnValue(false)
    expect(event.emit('a')).to.equal(false)

    stub1.mockReturnValue('1')
    stub2.mockReturnValue('2')
    expect(event.emit('a')).to.equal('2')

    stub1.mockReturnValue(false)
    stub2.mockReturnValue('1')
    expect(event.emit('a')).to.equal('1')

    stub1.mockReturnValue('1')
    stub2.mockReturnValue(false)
    expect(event.emit('a')).to.equal(false)

    event.off('a')
    event.on('a', () => {})
    expect(event.emit('a')).to.equals(undefined)
  })

  test('emit arguments', () => {
    const event = new Events()
    const spy = vi.fn()

    event.on('a', spy)
    event.emit('a', 1, 2)
    expect(spy).toHaveBeenLastCalledWith(1, 2, undefined)

    event.emit('a', 1)
    expect(spy).toHaveBeenLastCalledWith(1, undefined)

    event.emit('a', 1, 2, 3)
    expect(spy).toHaveBeenLastCalledWith(1, 2, 3, undefined)

    event.emit('a', 1, 2, 3, 4, 5)
    expect(spy).toHaveBeenLastCalledWith(1, 2, 3, 4, 5, undefined)

    event.emit('a', 1, 2, 3, 4, 5)
    expect(spy).not.toHaveBeenLastCalledWith(1, 2, 3, 4, 5, 6, undefined)
  })

  test('callback context is the instance', () => {
    const spy = vi.fn()
    const event = new Events()

    event.on('a', spy)
    event.emit('a')

    expect(spy.mock.instances[0]).toEqual(event)
  })

  test('off no events', () => {
    const event = new Events()

    event.on('a', () => {})
    expect(event.off('b')).toEqual(event)
    expect(event.off('b', () => {})).toEqual(event)
  })

  test('should pass the returned value to the next listener', () => {
    const event = new Events<{
      a: (num: number) => number
    }>()

    event.on('a', (num) => {
      return num + 1
    })

    event.on('a', (num, val) => {
      if (val) {
        return val + 2
      } else {
        return num
      }
    })

    expect(event.emit('a', 10)).to.equals(13)
  })

  test('check if event has listeners', () => {
    const event = new Events()

    expect(event.contains('a')).to.equals(false)
    event.on('a', () => {})
    expect(event.contains('a')).to.equals(true)
  })
})
