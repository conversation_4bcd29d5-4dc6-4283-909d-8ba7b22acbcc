import Aegis from '../../src/core/Aegis'
import { now, events } from '../../src/utils'
import { SDK_NAME } from '../../src/constants'
import { isBrowser } from '../../src/utils/is'
import { describe, expect, test, vi } from 'vitest'
import HttpTransport from '../../src/transports/http'
import ConsoleTransport from '../../src/transports/console'
import AbstractTransport from '../../src/transports/transport'
import PixelTransport from '../../src/transports/browser/pixel'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Aegis', () => {
  const config = {
    id: 'test'
  }

  test('Aegis setConfig', () => {
    const aegis = new Aegis(config)

    expect(aegis.__configManager.getConfigValue('id')).toEqual('test')
    expect(aegis.__configManager.getConfigValue('env')).toEqual('prod')

    aegis.setConfig({
      env: 'test'
    })

    expect(aegis.__configManager.getConfigValue('env')).toEqual('test')

    aegis.destroy()
  })

  test('Aegis destroy', async () => {
    const aegis = new Aegis(config)

    expect(aegis.__inited).toEqual(true)
    expect(aegis.destroyed).toEqual(false)
    aegis.destroy()
    expect(aegis.__inited).toEqual(false)
    expect(aegis.destroyed).toEqual(true)

    aegis.destroy()

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    aegis.build({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    aegis.send({
      type: 'custom',
      common: {},
      payload: {
        name: 'test'
      }
    })
    expect(aegis.__inited).toEqual(false)
    expect(aegis.destroyed).toEqual(true)
  })

  test('Aegis report', async () => {
    const mockSend = vi.fn()
    const mockDestroy = vi.fn()

    class mockTransport extends AbstractTransport {
      send = mockSend
      destroy = mockDestroy
    }

    const aegis = new Aegis({
      ...config,
      release: '1.0.0',
      did: 'e654c45fd5c34fb2b97c2ed8e8c766f0',
      transports: [new mockTransport('https://example.com/report')]
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)
    expect(mockSend.mock.calls.length).toEqual(1)
    expect(mockSend.mock.calls[0][0]).toEqual({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        release: '1.0.0',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        os: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        cid: isBrowser ? location.href : '',
        url: isBrowser ? location.href : '',
        network: isBrowser ? '4g' : 'unknown',
        did: 'e654c45fd5c34fb2b97c2ed8e8c766f0',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'hello'
      }
    })

    await wait(100)
    expect(mockSend.mock.calls.length).toEqual(2)
    expect(mockSend.mock.calls[1][0]).toEqual({
      type: 'custom',
      payload: {
        name: 'hello'
      },
      common: {
        id: 'test',
        release: '1.0.0',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        os: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        cid: isBrowser ? location.href : '',
        url: isBrowser ? location.href : '',
        network: isBrowser ? '4g' : 'unknown',
        did: 'e654c45fd5c34fb2b97c2ed8e8c766f0',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    aegis.destroy()
  })

  test('Aegis Immediate Reporting of Custom Metrics', async () => {
    const mockSend = vi.fn()
    const mockDestroy = vi.fn()

    class mockTransport extends AbstractTransport {
      send = mockSend
      destroy = mockDestroy
    }

    const aegis = new Aegis({
      ...config,
      release: '1.0.0',
      did: 'e654c45fd5c34fb2b97c2ed8e8c766f0',
      transports: [new mockTransport('https://example.com/report')]
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      },
      immediate: true
    })

    await wait(100)
    expect(mockSend.mock.calls.length).toEqual(1)
    expect(mockSend.mock.calls[0][0].type).toEqual('custom')
    expect(mockSend.mock.calls[0][0].payload).toEqual({
      name: 'test'
    })
    expect(mockSend.mock.calls[0][0].common).toEqual({
      url: expect.any(String),
      id: 'test',
      release: '1.0.0',
      did: expect.any(String),
      network: expect.any(String),
      env: expect.any(String),
      uid: expect.any(String),
      sid: expect.any(String),
      os: expect.any(String),
      sample: expect.any(Number),
      sdk_name: expect.any(String),
      timestamp: expect.any(Number),
      sdk_version: expect.any(String),
      cid: isBrowser ? location.href : '',
      vid: expect.stringMatching(/^__.*/)
    })
    expect(mockSend.mock.calls[0][0].immediate).toEqual(true)

    aegis.destroy()
  })

  test('custom builder', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis = new Aegis({
      ...config,
      builder: () => {
        return {
          type: 'custom',
          payload: {
            name: 'hello'
          },
          common: {}
        }
      }
    })

    vi.spyOn(aegis, 'send').mockImplementation((result) => {
      expect(result).toEqual({
        type: 'custom',
        payload: {
          name: 'hello'
        },
        common: {}
      })
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    const aegis2 = new Aegis({
      ...config,
      builder: () => {
        return null as any
      }
    })

    const spy = vi.fn()

    vi.spyOn(aegis2, 'send').mockImplementation(spy)

    aegis2.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).not.toHaveBeenCalled()

    aegis2.destroy()
    aegis.destroy()

    vi.restoreAllMocks()
  })

  test('nodejs builder', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: '01f9f988a7670995a8098c41e9d38fb1'
    })

    vi.spyOn(aegis, 'send').mockImplementation((result) => {
      expect(result).toEqual({
        type: 'custom',
        payload: {
          name: 'test'
        },
        immediate: result.immediate,
        common: {
          id: '01f9f988a7670995a8098c41e9d38fb1',
          uid: expect.any(String),
          sdk_name: expect.any(String),
          sdk_version: expect.any(String),
          timestamp: expect.any(Number)
        }
      })
    })

    aegis.report({
      type: 'custom',
      immediate: true,
      payload: {
        name: 'test'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    aegis.destroy()

    vi.restoreAllMocks()
  })

  test('miniapp builder', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: '02f9f988a7670995a8098c41e9d38fb1'
    })

    vi.spyOn(aegis, 'send').mockImplementation((result) => {
      expect(result).toEqual({
        type: 'custom',
        payload: {
          name: 'test'
        },
        immediate: result.immediate,
        common: {
          id: '02f9f988a7670995a8098c41e9d38fb1',
          uid: expect.any(String),
          sdk_name: expect.any(String),
          sdk_version: expect.any(String),
          timestamp: expect.any(Number)
        }
      })
    })

    aegis.report({
      type: 'custom',
      immediate: true,
      payload: {
        name: 'test'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    aegis.destroy()

    vi.restoreAllMocks()
  })

  test('RN builder', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: '033f9f988a7670995a8098c41e9d38fb1'
    })

    vi.spyOn(aegis, 'send').mockImplementation((result) => {
      expect(result).toEqual({
        type: 'custom',
        payload: {
          name: 'test'
        },
        immediate: result.immediate,
        common: {
          id: '033f9f988a7670995a8098c41e9d38fb1',
          uid: expect.any(String),
          sdk_name: expect.any(String),
          sdk_version: expect.any(String),
          timestamp: expect.any(Number)
        }
      })
    })

    aegis.report({
      type: 'custom',
      immediate: true,
      payload: {
        name: 'test'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    aegis.destroy()

    vi.restoreAllMocks()
  })

  test('default builder', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: ''
    })

    vi.spyOn(aegis, 'send').mockImplementation((result) => {
      expect(result).toEqual({
        type: 'custom',
        payload: {
          name: 'test'
        },
        common: {
          id: '',
          sample: 1,
          os: expect.any(String),
          cid: expect.any(String),
          url: expect.any(String),
          sid: expect.any(String),
          env: expect.any(String),
          uid: expect.any(String),
          network: expect.any(String),
          sdk_name: expect.any(String),
          sdk_version: expect.any(String),
          timestamp: expect.any(Number),
          vid: expect.stringMatching(/^__.*/)
        }
      })
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    aegis.destroy()

    vi.restoreAllMocks()
  })

  test('storage unavailable', () => {
    vi.stubGlobal('localStorage', {
      getItem() {
        throw new Error('storage unavailable')
      }
    })

    const aegis = new Aegis({
      ...config,
      SDKReportUrl: 'http://example.com/report.gif'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('Aegis config options is undefined', () => {
    const aegis = new Aegis(undefined as any)

    expect(aegis.__configManager.getConfigValue('id')).toEqual('')
    expect(typeof aegis.__configManager.getConfigValue('uid')).toEqual('string')
    expect(aegis.__configManager.getConfigValue('did')).toEqual('')
    expect(aegis.__configManager.getConfigValue('sample')).toEqual(1)
    expect(aegis.__configManager.getConfigValue('env')).toEqual('prod')
    expect(aegis.__configManager.getConfigValue('release')).toEqual('')
    expect(aegis.__configManager.getConfigValue('transports')).toEqual([])
    expect(aegis.__configManager.getConfigValue('integrations')).toEqual([])
    expect(aegis.__configManager.getConfigValue('builder')).toBeInstanceOf(Function)
    expect(aegis.__configManager.getConfigValue('viewId')).toEqual(expect.stringMatching(/^__.*/))
    aegis.destroy()
  })

  test('multiple Aegis instances', () => {
    const spy = vi.fn()
    vi.spyOn(console, 'warn').mockImplementation(spy)

    const aegis = new Aegis(config)
    const aegis2 = new Aegis(config)

    expect(aegis).not.toBe(aegis2)
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenCalledWith('Multiple instances of [__SDK_NAME__] detected, Please call the `destroy` method in an orderly manner to destroy.')

    vi.spyOn(console, 'error').mockImplementation(spy)

    aegis.destroy()
    expect(aegis.destroyed).toEqual(false)
    expect(aegis2.destroyed).toEqual(false)
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenCalledWith('Destroy failed, the instance is not the last instance, please call the `destroy` method in an orderly manner to destroy.')

    aegis2.destroy()
    aegis.destroy()
    expect(aegis.destroyed).toEqual(true)
    expect(aegis2.destroyed).toEqual(true)

    vi.restoreAllMocks()
  })

  test('sample rate', async () => {
    const originalMath = Math

    vi.stubGlobal('Math', {
      max: originalMath.max,
      floor: originalMath.floor,
      random: () => 0.3
    })

    const spy = vi.fn()
    const aegis = new Aegis({
      ...config,
      sample: 0.5
    })

    vi.spyOn(aegis, 'send').mockImplementation(spy)

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        sample: 0.5,
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        cid: isBrowser ? location.href : '',
        url: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('sample rate is miss', async () => {
    const originalMath = Math

    vi.stubGlobal('Math', {
      max: originalMath.max,
      floor: originalMath.floor,
      random: () => 0.7
    })

    const spy = vi.fn()
    const aegis = new Aegis({
      ...config,
      sample: 0.5
    })

    vi.spyOn(aegis, 'send').mockImplementation(spy)

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('sample rate is 0', async () => {
    const spy = vi.fn()
    const aegis = new Aegis({
      ...config,
      sample: 0
    })

    vi.spyOn(aegis, 'send').mockImplementation(spy)

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
  })

  test('preload static methods', () => {
    const spy = vi.fn()
    vi.spyOn(console, 'warn').mockImplementation(spy)

    const aegis = new Aegis(config)

    expect(Aegis.precollect).toBeDefined()
    expect(Aegis.pageview).toBeDefined()
    expect(Aegis.blankScreen).toBeDefined()
    expect(Aegis.action).toBeDefined()
    expect(Aegis.device).toBeDefined()
    expect(Aegis.api).toBeDefined()
    expect(Aegis.bridge).toBeDefined()
    expect(Aegis.resource).toBeDefined()
    expect(Aegis.resourceError).toBeDefined()
    expect(Aegis.jsError).toBeDefined()
    expect(Aegis.perf).toBeDefined()
    expect(Aegis.largePictureInspect).toBeDefined()
    expect(Aegis.transports.Console).toBeDefined()
    expect(Aegis.transports.Pixel).toBeDefined()
    expect(Aegis.transports.Http).toBeDefined()
    expect(Aegis.largePictureInspect).toBeDefined()

    // @ts-expect-error
    Aegis.precollect()
    // @ts-expect-error
    Aegis.pageview()

    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenCalledWith('[' + SDK_NAME + '] precollect does not exist')
    expect(spy).toHaveBeenCalledWith('[' + SDK_NAME + '] pageview does not exist')

    try {
      // @ts-expect-error
      Aegis.helloPlugin()
    } catch (error) {
      expect(error).toBeInstanceOf(Error)
      expect(spy).toHaveBeenCalledTimes(2)
      expect(error.message.includes('helloPlugin is not a function')).toBeTruthy()
    }

    // @ts-expect-error
    Aegis.transports.Console()
    // @ts-expect-error
    Aegis.transports.Pixel()
    // @ts-expect-error
    Aegis.transports.Http()

    expect(spy).toHaveBeenCalledTimes(5)
    expect(spy).toHaveBeenCalledWith('[' + SDK_NAME + '] Console does not exist')
    expect(spy).toHaveBeenCalledWith('[' + SDK_NAME + '] Pixel does not exist')
    expect(spy).toHaveBeenCalledWith('[' + SDK_NAME + '] Http does not exist')

    try {
      // @ts-expect-error
      Aegis.transports.Hello()
    } catch (error) {
      expect(error).toBeInstanceOf(Error)
      expect(spy).toHaveBeenCalledTimes(5)
      expect(error.message.includes('Hello is not a function')).toBeTruthy()
    }

    aegis.destroy()
    vi.restoreAllMocks()
  })

  test.runIf(isBrowser)('network type', async () => {
    const spy = vi.fn()
    const aegis = new Aegis(config)

    vi.spyOn(aegis, 'send').mockImplementation(spy)

    vi.stubGlobal('navigator', {
      mozConnection: {
        effectiveType: '2g'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: '2g',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      webkitConnection: {
        effectiveType: '3g'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: '3g',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      connection: {
        type: 'wifi'
      }
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: 'wifi',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      connection: null
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(4)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: 'unknown',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', null)

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(5)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: 'unknown',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(6)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        network: '4g',
        os: expect.any(String),
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    aegis.destroy()
  })

  test.runIf(isBrowser)('browser os', async () => {
    const aegis = new Aegis(config)
    const spy = vi.fn()
    vi.spyOn(aegis, 'send').mockImplementation(spy)

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'MacOS',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent:
        'Mozilla/5.0 (Linux; Android 10; SM-G9750 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.19 SP-engine/2.15.0 baiduboxapp/11.19.5.10 (Baidu; P1 10)'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'Android',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Mobile/15E148 Safari/604.1'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'IOS',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(4)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'Windows',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (X11; CrOS x86_64 15633.69.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(5)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'Chrome OS',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(6)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'Ubuntu',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (X11; FreeBSD amd64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(7)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'FreeBSD',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    vi.stubGlobal('navigator', {
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    aegis.report({
      type: 'custom',
      payload: {
        name: 'test'
      }
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(8)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'custom',
      payload: {
        name: 'test'
      },
      common: {
        id: 'test',
        os: 'Linux',
        env: expect.any(String),
        uid: expect.any(String),
        sid: expect.any(String),
        network: expect.any(String),
        sample: expect.any(Number),
        sdk_name: expect.any(String),
        timestamp: expect.any(Number),
        sdk_version: expect.any(String),
        url: isBrowser ? location.href : '',
        cid: isBrowser ? location.href : '',
        vid: expect.stringMatching(/^__.*/)
      }
    })

    vi.unstubAllGlobals()

    aegis.destroy()
  })

  describe('Aegis events', () => {
    test('on and emit', () => {
      const spy = vi.fn()
      const aegis = new Aegis(config)

      aegis.on('provide', spy)
      aegis.emit('provide', 'test')
      expect(spy.mock.calls.length).toEqual(1)

      aegis.emit('provide', 'test')
      aegis.emit('provide', 'test')
      aegis.emit('provide', 'test')
      aegis.emit('provide', 'test')
      expect(spy.mock.calls.length).toEqual(5)

      aegis.destroy()
    })

    test('off', () => {
      const aegis = new Aegis(config)

      expect(aegis.off()).toEqual(aegis)

      aegis.on('init', () => {})
      expect(aegis.off()).toEqual(aegis)

      aegis.on('init', () => {})
      expect(aegis.off('init')).toEqual(aegis)

      aegis.destroy()
    })
  })

  describe('Aegis integrations', () => {
    test('install', () => {
      const spy = vi.fn()

      const aegis = new Aegis({
        ...config,
        integrations: [
          {
            name: 'test',
            setup: spy
          }
        ]
      })

      expect(spy.mock.calls.length).toEqual(1)

      aegis.destroy()
    })

    test('install same integration once', () => {
      const spy = vi.fn()

      try {
        new Aegis({
          ...config,
          integrations: [
            {
              name: 'test',
              setup: spy
            },
            {
              name: 'test',
              setup: spy
            }
          ]
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }

      expect(spy.mock.calls.length).toEqual(1)
    })

    test('provide method', () => {
      const aegis = new Aegis({
        ...config,
        integrations: [
          {
            name: 'test',
            setup: (aegis) => {
              aegis.provide('sayHello', () => {
                return 'hello'
              })

              aegis.provide('calc', (a: number, b: number) => {
                return a + b
              })
            }
          }
        ]
      })

      expect((aegis['sayHello'] as any)()).toEqual('hello')
      expect((aegis['calc'] as any)(1, 2)).toEqual(3)
      aegis.destroy()
    })

    test('tearDown', () => {
      const spy = vi.fn()

      const aegis = new Aegis({
        ...config,
        integrations: [
          {
            name: 'test',
            setup: spy,
            tearDown: spy
          }
        ]
      })

      expect(spy.mock.calls.length).toEqual(1)
      aegis.destroy()
      expect(spy.mock.calls.length).toEqual(2)
    })

    test('integrations is undefined', () => {
      const aegis = new Aegis({
        ...config,
        integrations: undefined
      })

      expect(aegis.__configManager.getConfigValue('integrations')).toEqual(undefined)
      aegis.destroy()
    })

    test('integrations error', () => {
      function errorHandler(error: Error) {
        expect(error).toBeInstanceOf(Error)
        expect(error.message).toEqual('[__SDK_NAME__] Error in integration "test": a is not defined')
      }

      events.on('error', errorHandler)

      const aegis = new Aegis({
        ...config,
        integrations: [
          {
            name: 'test',
            setup: () => {
              // @ts-ignore
              console.log(a)
            }
          }
        ]
      })

      events.off('error', errorHandler)

      aegis.destroy()
    })

    test('handle undefined integration', () => {
      try {
        const aegis = new Aegis({
          ...config,
          integrations: [undefined as any]
        })

        aegis.destroy()
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })

    test('integrations is stub', () => {
      const spy = vi.fn()
      vi.spyOn(console, 'warn').mockImplementation(spy)

      const aegis = new Aegis({
        ...config,
        integrations: [{ name: 'test', isStub: true } as any]
      })

      expect(aegis.getIntegration('test')).toEqual({
        isStub: true,
        name: 'test'
      })

      aegis.destroy()

      expect(spy).toHaveBeenCalledWith('[__SDK_NAME__] Mock integration "test" cannot be installed as a real plugin.')

      vi.restoreAllMocks()
    })

    test('get integration using integrationName', () => {
      const testIntegration = {
        name: 'test',
        setup: () => {}
      }

      const aegis = new Aegis({
        ...config,
        integrations: [testIntegration]
      })

      expect(aegis.getIntegration('test')).toBe(testIntegration)
      expect(aegis.getIntegration('nonexistent')).toBeUndefined()

      aegis.destroy()

      const aegisWithoutIntegrations = new Aegis(config)
      expect(aegisWithoutIntegrations.getIntegration('test')).toBeUndefined()

      aegisWithoutIntegrations.destroy()
    })
  })

  describe('Aegis lifecycle', () => {
    test('onInit', () => {
      vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      const spy = vi.fn()

      const aegis = new Aegis({
        ...config,
        integrations: [
          {
            name: 'test',
            setup: (aegis) => {
              expect(aegis.__inited).toEqual(false)
              aegis.on('init', spy)
            }
          }
        ]
      })

      aegis.on('init', spy)

      expect(aegis.__inited).toEqual(true)
      expect(spy.mock.calls.length).toEqual(2)

      const aegis2 = new Aegis(config)

      aegis2.on('init', spy)
      expect(spy.mock.calls.length).toEqual(3)

      aegis2.destroy()
      aegis.destroy()

      vi.restoreAllMocks()
    })

    test('onBeforeDestroy', () => {
      const spy = vi.fn()
      const aegis = new Aegis(config)

      aegis.on('beforeDestroy', spy)
      aegis.destroy()
      aegis.on('beforeDestroy', spy)
      expect(spy.mock.calls.length).toEqual(2)
    })

    test('onBeforeReport', async () => {
      vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      const spy = vi.fn()
      const aegis = new Aegis(config)

      aegis.on('beforeReport', spy)
      aegis.report({
        type: 'custom',
        payload: {
          name: 'test'
        }
      })

      await wait(100)

      expect(spy).toHaveBeenLastCalledWith(
        {
          type: 'custom',
          payload: {
            name: 'test'
          }
        },
        undefined
      )
      expect(spy.mock.calls.length).toEqual(1)

      const spy2 = vi.fn((data: string) => data)
      const aegis2 = new Aegis(config)

      vi.spyOn(aegis2, 'build').mockImplementation((result) => {
        expect(result).toEqual({
          type: 'custom',
          payload: {
            name: 'hello'
          }
        })
      })

      aegis2.on('beforeReport', spy2 as any)
      aegis2.report({
        type: 'custom',
        payload: {
          name: 'hello'
        }
      })

      await wait(100)

      expect(spy2).toHaveBeenLastCalledWith(
        {
          type: 'custom',
          payload: {
            name: 'hello'
          }
        },
        undefined
      )
      expect(spy2.mock.calls.length).toEqual(1)

      aegis2.destroy()
      aegis.destroy()

      vi.restoreAllMocks
    })

    test('onBeforeBuild', () => {
      vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      const spy = vi.fn()
      const aegis = new Aegis(config)

      aegis.on('beforeBuild', spy)
      aegis.build({
        type: 'custom',
        payload: {
          name: 'test'
        }
      })
      expect(spy).toHaveBeenLastCalledWith(
        {
          type: 'custom',
          payload: {
            name: 'test'
          }
        },
        undefined
      )
      expect(spy.mock.calls.length).toEqual(1)

      const spy2 = vi.fn((data) => data)
      const aegis2 = new Aegis(config)

      vi.spyOn(aegis2, 'send').mockImplementation((result) => {
        expect(result).toEqual({
          type: 'custom',
          payload: {
            name: 'hello'
          },
          common: {
            id: 'test',
            os: expect.any(String),
            env: expect.any(String),
            uid: expect.any(String),
            sid: expect.any(String),
            sample: expect.any(Number),
            network: expect.any(String),
            sdk_name: expect.any(String),
            timestamp: expect.any(Number),
            sdk_version: expect.any(String),
            url: isBrowser ? location.href : '',
            cid: isBrowser ? location.href : '',
            vid: expect.stringMatching(/^__.*/)
          }
        })
      })

      aegis2.on('beforeBuild', spy2)
      aegis2.build({
        type: 'custom',
        payload: {
          name: 'hello'
        }
      })
      expect(spy2).toHaveBeenLastCalledWith(
        {
          type: 'custom',
          payload: {
            name: 'hello'
          }
        },
        undefined
      )
      expect(spy2.mock.calls.length).toEqual(1)

      aegis2.destroy()
      aegis.destroy()

      vi.restoreAllMocks()
    })

    test('onBeforeSend', () => {
      const spy = vi.fn()
      const aegis = new Aegis(config)

      aegis.on('beforeSend', spy)
      aegis.send({
        type: 'custom',
        payload: {
          name: 'test'
        },
        common: {
          timestamp: now(),
          url: isBrowser ? location.href : ''
        }
      })
      expect(spy.mock.calls.length).toEqual(1)
      aegis.destroy()
    })
  })

  describe('Aegis transports', () => {
    test('console transport', async () => {
      const spy = vi.fn()

      vi.spyOn(console, 'log').mockImplementation(spy)

      const aegis = new Aegis({
        ...config,
        transports: [new ConsoleTransport('http://example.com/report.gif')]
      })

      aegis.report({
        type: 'custom',
        payload: {
          name: 'test'
        }
      })

      await wait(100)

      expect(spy).toHaveBeenCalledWith(`${SDK_NAME} SDK ---->`, {
        type: 'custom',
        payload: {
          name: 'test'
        },
        common: {
          id: 'test',
          env: expect.any(String),
          uid: expect.any(String),
          sid: expect.any(String),
          os: expect.any(String),
          sample: expect.any(Number),
          network: expect.any(String),
          sdk_name: expect.any(String),
          timestamp: expect.any(Number),
          sdk_version: expect.any(String),
          url: isBrowser ? location.href : '',
          cid: isBrowser ? location.href : '',
          vid: expect.stringMatching(/^__.*/)
        }
      })

      aegis.destroy()

      vi.restoreAllMocks()
    })

    test.runIf(isBrowser)('pixel transport', async () => {
      let receivedStr = ''
      const reportUrl = 'http://example.com/report.gif'

      const aegis = new Aegis({
        ...config,
        transports: [new PixelTransport(reportUrl)]
      })

      vi.stubGlobal('Image', function (this: any) {
        this._src = ''
        this.__defineSetter__('src', (value: string) => {
          receivedStr = this._src = value
        })
        this.__defineGetter__('src', () => {
          return this._src
        })
      })

      aegis.report({
        type: 'custom',
        payload: {
          name: 'test'
        }
      })

      await wait(100)

      const url = new URL(receivedStr)

      expect(`${url.origin}${url.pathname}`).toEqual(reportUrl)

      expect(JSON.parse(<string>url.searchParams.get('d'))).toEqual({
        type: 'custom',
        payload: {
          name: 'test'
        },
        common: {
          id: 'test',
          cid: location.href,
          url: location.href,
          os: expect.any(String),
          env: expect.any(String),
          uid: expect.any(String),
          sid: expect.any(String),
          sample: expect.any(Number),
          network: expect.any(String),
          sdk_name: expect.any(String),
          timestamp: expect.any(Number),
          sdk_version: expect.any(String),
          vid: expect.stringMatching(/^__.*/)
        }
      })

      aegis.destroy()

      vi.unstubAllGlobals()
    })

    test('http transport', async () => {
      const spy = vi.fn()
      const reportUrl = 'http://example.com/report'
      const transport = new HttpTransport(reportUrl)

      vi.spyOn(transport, '__doRequest' as any).mockImplementation(spy)

      const aegis = new Aegis({
        ...config,
        transports: [transport]
      })

      aegis.report({
        type: 'custom',
        payload: {
          name: 'test'
        }
      })

      await wait(100)

      expect(spy).toHaveBeenCalledWith([
        {
          type: 'custom',
          payload: {
            name: 'test'
          },
          common: {
            id: 'test',
            env: expect.any(String),
            uid: expect.any(String),
            sid: expect.any(String),
            os: expect.any(String),
            sample: expect.any(Number),
            network: expect.any(String),
            sdk_name: expect.any(String),
            timestamp: expect.any(Number),
            sdk_version: expect.any(String),
            url: isBrowser ? location.href : '',
            cid: isBrowser ? location.href : '',
            vid: expect.stringMatching(/^__.*/)
          }
        }
      ])

      aegis.destroy()

      vi.unstubAllGlobals()
    })

    test('get report urls', () => {
      const aegis = new Aegis({
        ...config,
        SDKReportUrl: 'http://example.com/url.gif',
        transports: [new HttpTransport('http://example.com/report'), new PixelTransport('http://example.com/report.gif')]
      })

      expect(aegis.getReportUrls()).toEqual(['http://example.com/url.gif', 'http://example.com/report', 'http://example.com/report.gif'])

      aegis.__configManager.removeTransport(aegis.__configManager.getConfigValue('transports')![0])
      aegis.__configManager.removeTransport(aegis.__configManager.getConfigValue('transports')![0])

      expect(aegis.getReportUrls()).toEqual(['http://example.com/url.gif'])

      aegis.__configManager.addTransport(new ConsoleTransport('http://example.com/console.gif'))

      expect(aegis.getReportUrls()).toEqual(['http://example.com/url.gif', 'http://example.com/console.gif'])

      aegis.destroy()
    })

    test('add report url', () => {
      const aegis = new Aegis(config)

      aegis.addReportUrl('http://example.com/report.gif')

      expect(aegis.getReportUrls()).toEqual(['http://example.com/report.gif'])

      aegis.destroy()
    })

    test('transport is a method but not an instance of AbstractTransport', () => {
      const aegis = new Aegis({
        ...config,
        transports: [(() => {}) as unknown as AbstractTransport]
      })

      expect(aegis.getReportUrls()).toEqual([])
      expect(aegis.send({} as any)).toEqual(undefined)

      aegis.destroy()
    })
  })
})
