export const blankScreen = `
  <div id="app" style="width: 400px; height: 1000px">
  </div>
`

export const skeleton = `
  <div id="app" style="width: 100%; height: 600px">
    <div class="skeleton-wrapper">
      <div class="skeleton-header"></div>
      <div class="skeleton-avatar"></div>
      <div class="skeleton-text"></div>
      <div class="skeleton-text" style="width: 60%;"></div>
      <div class="skeleton-article"></div>
      <div class="skeleton-article" style="width: 95%;"></div>
      <div class="skeleton-article"></div>
      <div class="skeleton-article" style="width: 85%;"></div>
    </div>
  </div>
`

export const nonBlankScreen = `
  <div id="app" style="width: 400px; height: 1000px">
    <div class="test-wrap" style="width: 1000px; height: 400px">test</div>
  </div>
`

export const nonBlankScreenWithSkeleton = `
<div id="app" style="width: 100%; height: 600px">
  <div class="wrap">
    <div class="item">123123</div>
    <div class="item">123123</div>
    <div class="item">123123</div>
    <div class="item">123123</div>
    <div class="item">123123</div>
    <div>123123</div>
  </div>
</div>
`
