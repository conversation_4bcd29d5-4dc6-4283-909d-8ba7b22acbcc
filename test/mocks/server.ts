import cors from 'cors'
import express from 'express'
import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'

const _dirname = dirname(fileURLToPath(import.meta.url))
const app = express()
let sever: any

app.use(
  express.static(join(_dirname, 'assets'), {
    setHeaders: (res) => {
      res.setHeader('Timing-Allow-Origin', '*')
    }
  })
)

app.use(
  cors({
    credentials: true,
    origin: (_, callback) => callback(null, true)
  })
)

app.post('/status/404', (_, res) => {
  res.status(404).send('Not Found')
})

app.post('/post', express.json(), (req, res) => {
  setTimeout(() => {
    res.send(req.body)
  }, 20)
})

app.get('/json', (_, res) => {
  res.json({ a: 1 })
})

app.get('/binary', (_, res) => {
  const buffer = Buffer.from('This is binary data', 'utf-8')
  res.type('application/octet-stream').send(buffer)
})

app.get('/feats', (_, res) => {
  res.json([
    {
      name: 'position',
      type: 'CSS',
      strategy: 'property',
      params: { property: 'position', value: 'sticky' }
    }
  ])
})

app.post('/feats/failed', (_, res) => {
  res.send('hello')
})

app.get('/shutdown', (req, res) => {
  res.send('Server shutting down')

  res.on('finish', () => {
    sever.close(() => {
      console.log('Server stopped')
      process.exit(0)
    })
  })
})

app.get('/spa_load/no_response', (req, res) => {
  setTimeout(() => {
    res.send('')
  }, 300000)
})

app.get('/spa_load/all_resources_loaded', (req, res) => {
  setTimeout(() => {
    res.send('')
  }, 100)
})

app.get('/spa_load/img', (req, res) => {
  const buffer = Buffer.alloc(100 * 1024, 0)
  setTimeout(() => {
    res.type('image/png').send(buffer)
  }, 100)
})

app.get('/spa_load/error', (req, res) => {
  setTimeout(() => {
    res.destroy()
  }, 100)
})

app.get('/spa_load/bg-image', (req, res) => {
  setTimeout(() => {
    const buffer = Buffer.alloc(100 * 1024, 0)
    res.type('image/png').send(buffer)
  }, 100)
})

app.get('/spa_load/bg-image2', (req, res) => {
  setTimeout(() => {
    const buffer = Buffer.alloc(100 * 1024, 0)
    res.type('image/png').send(buffer)
  }, 100)
})

app.get('/spa_load/non-exist-image', (req, res) => {
  res.status(404).send('Not found')
})

app.get('/spa_load/bg-image-not-pending', (req, res) => {
  setTimeout(() => {
    const buffer = Buffer.alloc(100 * 1024, 0)
    res.type('image/png').send(buffer)
  }, 100)
})

app.get('/spa_load/img-not-pending', (req, res) => {
  setTimeout(() => {
    const buffer = Buffer.alloc(100 * 1024, 0)
    res.type('image/png').send(buffer)
  }, 100)
})

sever = app.listen(3333, () => console.log('Server running on port 3333'))
