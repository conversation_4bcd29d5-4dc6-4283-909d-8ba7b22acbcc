import { events } from '../../../src/utils'
import { URIEncode } from '../../../src/utils'
import { describe, expect, test, vi } from 'vitest'
import PixelTransport from '../../../src/transports/browser/pixel'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Pixel Transport', () => {
  test('should be instantiated with a report URL', () => {
    const reportUrl = 'http://example.com/report.gif'
    const transport = new PixelTransport(reportUrl)
    expect(transport).toBeInstanceOf(PixelTransport)
  })

  test('should throw an error if reportUrl does not end with .gif', () => {
    const invalidUrl = 'http://example.com/report.png'
    expect(() => new PixelTransport(invalidUrl)).toThrow('Please use a 1x1 transparent GIF image as it is small enough.')

    const invalidUrl2 = 'http://example.com/report'
    expect(() => new PixelTransport(invalidUrl2)).toThrow('Please use a 1x1 transparent GIF image as it is small enough.')
  })

  test('should send data by creating an Image with the correct src', () => {
    let receivedStr = ''
    const reportUrl = 'http://example.com/report.gif'
    const transport = new PixelTransport(reportUrl)
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }

    vi.stubGlobal('Image', function (this: any) {
      this._src = ''
      this.__defineSetter__('src', (value: string) => {
        receivedStr = this._src = value
      })
      this.__defineGetter__('src', () => {
        return this._src
      })
    })

    transport.send(REPORT_DATA)

    expect(receivedStr).toBe(`${reportUrl}?d=${URIEncode(JSON.stringify(REPORT_DATA))}`)

    vi.unstubAllGlobals()
  })

  test('should send data by creating an Image with the correct src when the URL already has a query string', () => {
    let receivedStr = ''
    const reportUrl = 'http://example.com/report.gif?a=b'
    const transport = new PixelTransport(reportUrl)
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }

    vi.stubGlobal('Image', function (this: any) {
      this._src = ''
      this.__defineSetter__('src', (value: string) => {
        receivedStr = this._src = value
      })
      this.__defineGetter__('src', () => {
        return this._src
      })
    })

    transport.send(REPORT_DATA)

    expect(receivedStr).toBe(`${reportUrl}&d=${URIEncode(JSON.stringify(REPORT_DATA))}`)

    vi.unstubAllGlobals()
  })

  test('should report an error if the image fails to load', async () => {
    const spy = vi.fn()
    const reportUrl = 'http://localhost/report.gif'
    const transport = new PixelTransport(reportUrl)
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }

    const handleError = (error: unknown) => {
      spy((error as Error).message)
    }

    events.on('error', handleError)

    transport.send(REPORT_DATA)

    await wait(100)
    events.off('error', handleError)
    expect(spy).toHaveBeenLastCalledWith(`PixelTransport: Failed to send data to ${reportUrl}`)
  })

  test('should split the data into chunks if it exceeds the max URL length', () => {
    const receivedUrls: string[] = []
    const reportUrl = 'http://example.com/report.gif'
    const transport = new PixelTransport(reportUrl)
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: 'a'.repeat(4000) }, common: {} }

    vi.stubGlobal('Image', function (this: any) {
      this._src = ''
      this.__defineSetter__('src', (value: string) => {
        receivedUrls.push(value)
        this._src = value
      })
      this.__defineGetter__('src', () => {
        return this._src
      })
    })

    transport.send(REPORT_DATA)

    const chunk0 = new URL(receivedUrls[0]).searchParams.get('d')!
    const chunk1 = new URL(receivedUrls[1]).searchParams.get('d')!
    const chunk2 = new URL(receivedUrls[2]).searchParams.get('d')!

    expect(receivedUrls.length).toBe(3)
    expect(chunk0).toBe(JSON.stringify(REPORT_DATA).slice(0, 1500))
    expect(chunk1).toBe(JSON.stringify(REPORT_DATA).slice(1500, 3000))
    expect(chunk2).toBe(JSON.stringify(REPORT_DATA).slice(3000))

    expect(new URL(receivedUrls[0]).searchParams.get('n')).toBe('0')
    expect(new URL(receivedUrls[1]).searchParams.get('n')).toBe('1')
    expect(new URL(receivedUrls[2]).searchParams.get('n')).toBe('2')

    expect(new URL(receivedUrls[0]).searchParams.get('done')).toBe('0')
    expect(new URL(receivedUrls[1]).searchParams.get('done')).toBe('0')
    expect(new URL(receivedUrls[2]).searchParams.get('done')).toBe('1')

    expect(new URL(receivedUrls[0]).searchParams.get('cid')).toBe(new URL(receivedUrls[1]).searchParams.get('cid'))
    expect(new URL(receivedUrls[0]).searchParams.get('cid')).toBe(new URL(receivedUrls[2]).searchParams.get('cid'))

    expect(JSON.parse(decodeURIComponent(chunk0 + chunk1 + chunk2)).payload.name.length).toBe(4000)

    vi.unstubAllGlobals()
  })
})
