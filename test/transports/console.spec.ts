import { SDK_NAME } from '../../src/constants'
import { describe, expect, test, vi } from 'vitest'
import ConsoleTransport from '../../src/transports/console'

describe('Console Transport', () => {
  test('should send data to the console log with the correct prefix', () => {
    const spy = vi.fn()
    const transport = new ConsoleTransport('http://example.com/report.gif')
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }

    vi.spyOn(console, 'log').mockImplementation(spy)

    transport.send(REPORT_DATA)

    expect(spy).toHaveBeenCalledWith(`${SDK_NAME} SDK ---->`, REPORT_DATA)

    vi.restoreAllMocks()
  })
})
