import { events } from '../../src/utils'
import { isBrowser } from '../../src/utils/is'
import { describe, expect, test, vi } from 'vitest'
import HttpTransport from '../../src/transports/http'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Http Transport', () => {
  test('should correctly handle instantiation with reportUrl and options', () => {
    const reportUrl = 'https://sayhello.com/report1'
    const options = { batch: true, batchCount: 5, batchInterval: 1000 }
    const transport = new HttpTransport(reportUrl, options)
    expect(transport).toBeInstanceOf(HttpTransport)
    expect(transport['__options'].retries).toEqual(3)
    expect(transport['__options'].batchCount).toEqual(options.batchCount)
  })

  test('should correctly handle instantiation without options', () => {
    const reportUrl = `http://localhost:3333/post`
    const transport = new HttpTransport(reportUrl, { retries: 0 })
    expect(transport).toBeInstanceOf(HttpTransport)
    expect(transport['__options'].retries).toEqual(0)
    expect(transport['__options'].batchCount).toEqual(10)

    transport.send({ type: 'custom', payload: { name: '123' }, common: {} })
  })

  test('should send single request when batch option is false', () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report3'
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }
    const transport = new HttpTransport(reportUrl, { batch: false })

    vi.spyOn(transport, '__doRequest' as any).mockImplementation(spy)

    transport.send(REPORT_DATA)

    expect(spy).toHaveBeenCalledWith([REPORT_DATA])

    vi.restoreAllMocks()
  })

  test('should batch data and send request when batchCount is reached', () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report4'
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }
    const transport = new HttpTransport(reportUrl, { batch: true, batchCount: 2, batchInterval: 1000 })

    vi.spyOn(transport, '__doRequest' as any).mockImplementation(spy)

    transport.send(REPORT_DATA)

    transport.send(REPORT_DATA)

    expect(spy).toHaveBeenCalledWith([REPORT_DATA, REPORT_DATA])

    vi.restoreAllMocks()
  })

  test('should batch data and send request after batchInterval', async () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report5'
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }
    const transport = new HttpTransport(reportUrl, { batch: true, batchCount: 5, batchInterval: 100 })

    vi.spyOn(transport, '__doRequest' as any).mockImplementation(spy)

    transport.send(REPORT_DATA)

    transport.send(REPORT_DATA)

    expect(spy).not.toHaveBeenCalled()

    await wait(200)

    expect(spy).toHaveBeenCalledWith([REPORT_DATA, REPORT_DATA])

    vi.restoreAllMocks()
  })

  test('cleanups and detaches event listeners on destroy', () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report6'
    const transport = new HttpTransport(reportUrl, { batch: true, batchCount: 5, batchInterval: 100 })

    transport['__cleanupTasks'].add(spy)
    transport.destroy()

    expect(spy).toHaveBeenCalled()
  })

  test('should send data immediately when immediate is true', async () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report5'
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }
    const REPORT_DATA_2: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '456' }, common: {} }
    const transport = new HttpTransport(reportUrl, { batch: true, batchCount: 5, batchInterval: 100 })

    vi.spyOn(transport, '__doRequest' as any).mockImplementation(spy)

    transport.send(REPORT_DATA)

    transport.send(REPORT_DATA_2)

    expect(spy).not.toHaveBeenCalled()

    await wait(200)

    expect(spy).toHaveBeenCalledWith([REPORT_DATA, REPORT_DATA_2])

    transport.send(REPORT_DATA_2, true)

    expect(spy).toHaveBeenCalledWith([REPORT_DATA_2])

    vi.restoreAllMocks()
  })

  test.runIf(isBrowser)('test sendBeacon cases', () => {
    const spy = vi.fn()
    const reportUrl = 'https://sayhello.com/report7'
    const REPORT_DATA: { type: 'custom'; payload: { name: string }; common: {} } = { type: 'custom', payload: { name: '123' }, common: {} }
    const transport = new HttpTransport(reportUrl, { batch: true, batchCount: 5, batchInterval: 100, retries: 0 })

    vi.stubGlobal('navigator', {
      sendBeacon: spy.mockImplementation(() => true)
    })

    transport.send(REPORT_DATA)

    window.dispatchEvent(new Event('unload'))
    window.dispatchEvent(new Event('pagehide'))
    window.dispatchEvent(new Event('beforeunload'))

    expect(spy.mock.calls.length).toEqual(1)
    expect(spy).toHaveBeenCalledWith(reportUrl, JSON.stringify({ d: [REPORT_DATA] }))

    vi.unstubAllGlobals()
  })

  test.skip('requesting a non-existent interface should report an error', async () => {
    const spy = vi.fn()
    const reportUrl = `http://localhost:3333/status/404`
    const handleError = (error: unknown) => {
      spy((error as Error).message)
    }

    events.on('error', handleError)

    const transport = new HttpTransport(reportUrl)
    expect(transport).toBeInstanceOf(HttpTransport)

    transport.send({ type: 'pv', payload: { pid: '123' }, common: {} } as any)

    await wait(15100)
    events.off('error', handleError)
    expect(spy).toHaveBeenLastCalledWith(`HttpTransport: Failed to send data to ${reportUrl}, message: Request failed with status: 404`)
  }, 15100)
})
