import Aegis from '../../../src/core/Aegis'
import pageview from '../../../src/integrations/browser/pageview'
import { describe, expect, test, vi, beforeEach, afterEach } from 'vitest'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('PageView', () => {
  const originalUrl = location.href

  beforeEach(() => {
    expect(history.pushState.toString()).toContain('native code')
    expect(history.replaceState.toString()).toContain('native code')
  })

  afterEach(() => {
    expect(originalUrl).toBe(location.href)
    expect(history.pushState.toString()).toContain('native code')
    expect(history.replaceState.toString()).toContain('native code')
  })

  test('history mode', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview({ mode: 'history' })]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy = vi.spyOn(aegis, 'report')

    aegis.sendPV('/test')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test`,
        source: 'manually'
      }
    })
    expect(aegis.__configManager.getConfigValue('viewId')).toMatch(new RegExp(`^${location.origin}/test_.+`))

    history.pushState({}, '', '/hello')
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'pushState'
      }
    })
    expect(aegis.__configManager.getConfigValue('viewId')).toMatch(new RegExp(`^${location.origin}/hello_.+`))

    history.pushState({}, '', '/world')
    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/world`,
        source: 'pushState'
      }
    })
    expect(aegis.__configManager.getConfigValue('viewId')).toMatch(new RegExp(`^${location.origin}/world_.+`))

    history.back()

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(4)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'history'
      }
    })
    expect(aegis.__configManager.getConfigValue('viewId')).toMatch(new RegExp(`^${location.origin}/hello_.+`))

    history.replaceState({}, '', '/test2')
    expect(spy).toHaveBeenCalledTimes(5)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test2`,
        source: 'replaceState'
      }
    })
    expect(aegis.__configManager.getConfigValue('viewId')).toMatch(new RegExp(`^${location.origin}/test2_.+`))

    history.back()

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(6)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: location.origin + location.pathname,
        source: 'history'
      }
    })

    aegis.destroy()
  })

  test('hash mode', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview({ mode: 'hash' })]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy = vi.spyOn(aegis, 'report')

    aegis.sendPV('/code')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}${location.pathname}#/code`,
        source: 'manually'
      }
    })

    location.hash = '#/hello?a=1'
    await wait(100)
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}${location.pathname}#/hello`,
        source: 'hash'
      }
    })

    history.back()

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}${location.pathname}#/`,
        source: 'hash'
      }
    })

    aegis.destroy()
  })

  test('default mode is history', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview(null as any)]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy = vi.spyOn(aegis, 'report')

    aegis.sendPV('/code')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/code`,
        source: 'manually'
      }
    })

    aegis.destroy()
  })

  test('custom page id extractor', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        pageview({
          extractPid: (url) => {
            return url
          }
        })
      ]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy = vi.spyOn(aegis, 'report')

    aegis.sendPV('test')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test`,
        source: 'manually'
      }
    })

    aegis.sendPV('')
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/`,
        source: 'manually'
      }
    })

    history.pushState({}, '', '/hello')
    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'pushState'
      }
    })

    history.back()

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(4)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: location.href,
        source: 'history'
      }
    })

    aegis.destroy()
  })

  test('initialization is not reported', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview({ initSend: false })]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy = vi.spyOn(aegis, 'report')

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
  })

  test('initialization is reported', async () => {
    let spy = null

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: (aegis) => {
            spy = vi.spyOn(aegis, 'report') as any
          }
        },
        pageview()
      ]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}${location.pathname}`,
        source: 'auto'
      }
    })

    await wait(4100)

    aegis.destroy()
  })

  test('multiple aegis instance', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis1 = new Aegis({
      id: 'test1',
      integrations: [
        pageview({
          mode: 'history',
          extractPid: (url) => {
            return url
          }
        })
      ]
    })

    const aegis2 = new Aegis({
      id: 'test2',
      integrations: [
        pageview({
          mode: 'history',
          extractPid: (url) => {
            return url
          }
        })
      ]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    const spy1 = vi.spyOn(aegis1, 'report')
    const spy2 = vi.spyOn(aegis2, 'report')

    aegis1.sendPV('/test1')
    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test1`,
        source: 'manually'
      }
    })

    aegis2.sendPV('/test2')
    expect(spy2).toHaveBeenCalledTimes(1)
    expect(spy2).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test2`,
        source: 'manually'
      }
    })

    history.pushState({}, '', '/hello')

    expect(spy1).toHaveBeenCalledTimes(2)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'pushState'
      }
    })

    expect(spy2).toHaveBeenCalledTimes(2)
    expect(spy2).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'pushState'
      }
    })

    aegis1.sendPV('/test1')
    expect(spy1).toHaveBeenCalledTimes(3)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test1`,
        source: 'manually'
      }
    })
    expect(spy2).toHaveBeenCalledTimes(2)

    history.back()
    await wait(100)

    aegis2.destroy()
    aegis1.destroy()

    vi.restoreAllMocks()
  })

  test('report data lifecycle', async () => {
    const results: string[] = []
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    aegis.on('beforeReport', (data) => {
      results.push('beforeReport')
      return data
    })

    aegis.on('beforeBuild', (data) => {
      results.push('beforeBuild')
      return data
    })

    aegis.on('beforeSend', (data) => {
      results.push('beforeSend')
      return data
    })

    await wait(100)
    expect(results).toEqual(['beforeReport', 'beforeBuild', 'beforeSend'])

    aegis.destroy()
  })

  test('detect vue router (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $router: {
        options: {
          scrollBehavior: () => {}
        }
      },
      $root: {
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('You have set the Vue-Router scrollBehavior option, in "initSend" mode, which will result in sending more PV data, set the Vue-Router scrollBehavior to null.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('detect vue router with no scroll behavior (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $router: {
        options: {
          scrollBehavior: null
        }
      },
      $root: {
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('detect vue router with no version (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $router: {
        options: {
          scrollBehavior: () => {}
        }
      },
      $root: {}
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('detect vue router (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      version: '3.2.31',
      config: {
        globalProperties: {
          $router: {
            options: {
              scrollBehavior: () => {}
            }
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('You have set the Vue-Router scrollBehavior option, in "initSend" mode, which will result in sending more PV data, set the Vue-Router scrollBehavior to null.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('detect vue router with no scroll behavior (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      version: '3.2.31',
      config: {
        globalProperties: {
          $router: {
            options: {
              scrollBehavior: null
            }
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('detect vue router with no version (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      config: {
        globalProperties: {
          $router: {
            options: {
              scrollBehavior: () => {}
            }
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  test('history api not supported', () => {
    vi.stubGlobal('history', {
      pushState: null
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview()]
    })

    expect(history.pushState).toBeNull()

    const spy = vi.spyOn(aegis, 'report')

    aegis.sendPV('/test')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test`,
        source: 'manually'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('sync cid', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [pageview({ syncCid: true })]
    })

    const spy = vi.spyOn(aegis, 'report')
    const setConfigSpy = vi.spyOn(aegis, 'setConfig')

    aegis.sendPV('/test')
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/test`,
        source: 'manually'
      }
    })
    expect(setConfigSpy).toHaveBeenCalledTimes(2)
    expect(setConfigSpy).toHaveBeenLastCalledWith({ cid: `${location.origin}/test` })

    history.pushState({}, '', '/hello')
    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/hello`,
        source: 'pushState'
      }
    })
    expect(setConfigSpy).toHaveBeenCalledTimes(4)
    expect(setConfigSpy).toHaveBeenLastCalledWith({ cid: `${location.origin}/hello` })

    history.replaceState({}, '', '/world')
    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}/world`,
        source: 'replaceState'
      }
    })
    expect(setConfigSpy).toHaveBeenCalledTimes(6)
    expect(setConfigSpy).toHaveBeenLastCalledWith({ cid: `${location.origin}/world` })

    history.back()

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(4)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'pv',
      payload: {
        pid: `${location.origin}${location.pathname}`,
        source: 'history'
      }
    })
    expect(setConfigSpy).toHaveBeenCalledTimes(8)
    expect(setConfigSpy).toHaveBeenLastCalledWith({ cid: `${location.origin}${location.pathname}` })

    aegis.destroy()
  })

  test('vueCheck is false', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $router: {
        options: {
          scrollBehavior: () => {}
        }
      },
      $root: {
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        pageview({
          vueCheck: false
        })
      ]
    })

    expect(history.pushState.toString()).not.toContain('native code')
    expect(history.replaceState.toString()).not.toContain('native code')

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })
})
