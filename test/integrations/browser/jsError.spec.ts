import Aegis from '../../../src/core/Aegis'
import json from '../../../src/utils/json'
import { describe, it, expect, vi } from 'vitest'
import jsError from '../../../src/integrations/browser/jsError'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('JsError', () => {
  it('trigger error event', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = 'const a = b + 1'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('trigger error event without message and name', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error()
    error.name = ''

    window.dispatchEvent(
      new ErrorEvent('error', {
        filename: 'path/to/file.js',
        lineno: 10,
        colno: 2,
        error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'No error message',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('trigger error event with Object message', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error()
    // @ts-ignore
    error.message = {
      error: new Error('error message')
    }

    window.dispatchEvent(
      new ErrorEvent('error', {
        filename: 'path/to/file.js',
        lineno: 10,
        colno: 2,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('trigger error event, but error is resource error', async () => {
    let errorEventTriggered = false
    const globalErrorListener = () => (errorEventTriggered = true)
    window.addEventListener('error', globalErrorListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const element = document.createElement('img')
    element.src = '/empty.jpg'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).not.toHaveBeenCalled()
    expect(errorEventTriggered).toBe(true)

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('trigger unhandledrejection event', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const element = document.createElement('script')
    element.textContent = `new Promise((_, reject) => {
      const error = new Error('error instance')
      error.code = 'VITEST_PENDING'
      reject(error)
    })`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error instance',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('trigger unhandledrejection event with string reason', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const element = document.createElement('script')
    element.textContent = `new Promise((_, reject) => {
      const str = new String('string reason')
      str.code = 'VITEST_PENDING'
      reject(str)
    })`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'UnhandledPromiseRejection',
        value: 'string reason'
      }
    })

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('trigger unhandledrejection event with object reason', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const element = document.createElement('script')
    element.textContent = `new Promise((_, reject) => {
      const error = { msg: 'object reason' }
      error.code = 'VITEST_PENDING'
      reject(error)
    })`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'UnhandledPromiseRejection',
        value: json.stringify({ msg: 'object reason', code: 'VITEST_PENDING' })
      }
    })

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('trigger unhandledrejection event with error reason', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const element = document.createElement('script')
    element.textContent = `new Promise((_, reject) => {
      const error = new Error('error reason')
      error.code = 'VITEST_PENDING'
      reject(error)
    })`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error reason',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('trigger unhandledrejection event with undefined reason', async () => {
    const eventListener = vi.fn()
    const originalAddEventListener = window.addEventListener
    const originalRemoveEventListener = window.removeEventListener

    window.addEventListener = eventListener
    window.removeEventListener = eventListener

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const handleRejectError = eventListener.mock.calls.find((call) => call[0] === 'unhandledrejection')[1]

    handleRejectError(
      new PromiseRejectionEvent('unhandledrejection', {
        promise: new Promise(() => {})
      })
    )

    aegis.destroy()
    window.addEventListener = originalAddEventListener
    window.removeEventListener = originalRemoveEventListener
  })

  it('trigger unhandledrejection event with custom reason', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const event = new CustomEvent('unhandledrejection', {
      detail: {
        promise: new Promise(function () {}),
        reason: new Error('promiseError')
      }
    }) as any

    event.reason = {
      code: 'VITEST_PENDING'
    }

    window.dispatchEvent(event)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'promiseError',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('captureException API', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    aegis.captureException(new Error('error instance'))

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error instance',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.captureException('string reason' as any)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'string reason'
      }
    })

    aegis.captureException({ msg: 'object reason' } as any)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: json.stringify({ msg: 'object reason' })
      }
    })

    aegis.captureException(null as any)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)

    aegis.destroy()

    aegis.captureException(new Error('error instance'))

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
  })

  it('use captureException API report resource error', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    let errorEventTriggered = false
    const globalErrorListener = (event: ErrorEvent) => {
      errorEventTriggered = true
      aegis.captureException(event)
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('img')
    element.src = '/empty.jpg'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).not.toHaveBeenCalled()
    expect(errorEventTriggered).toBe(true)

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('ignore error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        jsError({
          ignoreErrors: [/ignore_this_error/, 'ignore_that_error']
        })
      ]
    })

    const spy = vi.spyOn(aegis, 'report')

    const errors = [
      {
        error: new Error('ignore_this_error: This error should be ignored'),
        report: false
      },
      {
        error: new Error('ignore_that_error: This error should also be ignored'),
        report: false
      },
      {
        error: new Error('do_not_ignore_the_error: This error should not be ignored'),
        report: true
      }
    ]

    errors.forEach((err) => {
      window.dispatchEvent(
        new ErrorEvent('error', {
          message: err.error.message,
          error: err.error
        })
      )
      err.report ? expect(spy).toHaveBeenCalled() : expect(spy).not.toHaveBeenCalled()
    })

    aegis.destroy()
    vi.restoreAllMocks()
  })

  it('stop console porpogation', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        jsError({
          logErrorsInConsole: false
        })
      ]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = 'const a = b + 1'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('duplicate error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        jsError({
          dedupe: false
        })
      ]
    })

    const spy = vi.spyOn(aegis, 'report')
    let ev: ErrorEvent | null = null

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
      ev = event
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = 'const aa = b + 1'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    if (ev) {
      window.dispatchEvent(ev)
    }

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(2)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('de-duplicate error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')
    let ev: ErrorEvent | null = null

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
      ev = event
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = 'const aaa = b + 1'
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    if (ev) {
      window.dispatchEvent(ev)
    }

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('error event stack is empty', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    window.dispatchEvent(
      new ErrorEvent('error', {
        message: 'error message'
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message'
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('error event message is empty', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    window.dispatchEvent(
      new ErrorEvent('error', {
        filename: 'path/to/file.js',
        lineno: 10,
        colno: 2
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: ''
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('unhandledrejection event stack is empty', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalUnhandledRejectionListener = (event: PromiseRejectionEvent) => event.preventDefault()
    window.addEventListener('unhandledrejection', globalUnhandledRejectionListener, true)

    const element = document.createElement('script')
    element.textContent = `new Promise((_, reject) => {
      const error = new Error('error reason')
      error.code = 'VITEST_PENDING'
      error.stack = ''
      reject(error)
    })`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'promise',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error reason',
        stacktrace: []
      }
    })

    aegis.destroy()
    document.body.removeChild(element)
    window.removeEventListener('unhandledrejection', globalUnhandledRejectionListener, true)
  })

  it('use captureException API report error event, but stack is empty', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const error = new Error('error instance')
    error.stack = ''

    aegis.captureException(error)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error instance',
        stacktrace: []
      }
    })

    aegis.destroy()
  })

  // https://developer.mozilla.org/en-US/docs/Web/API/DOMException
  it('DOMException', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = `document.createElement('<div>')`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'InvalidCharacterError',
        value: `Failed to execute 'createElement' on 'Document': The tag name provided ('<div>') is not a valid name.`,
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
    document.body.removeChild(element)
  })

  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/URIError
  it('URIError', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = `decodeURI('%')`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'URIError',
        value: `URI malformed`,
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
    document.body.removeChild(element)
  })

  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RangeError
  it('RangeError', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = `function loop(x) {
      if (x >= 1000000000000) return;

      // do stuff
      loop(x + 1);
    }
    loop(0);`
    document.body.appendChild(element)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'RangeError',
        value: `Maximum call stack size exceeded`,
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
    document.body.removeChild(element)
  })

  it('multiple aegis instances', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis1 = new Aegis({
      id: 'test1',
      integrations: [jsError()]
    })

    const spy1 = vi.spyOn(aegis1, 'report')

    const aegis2 = new Aegis({
      id: 'test2',
      integrations: [jsError()]
    })

    const spy2 = vi.spyOn(aegis2, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const element = document.createElement('script')
    element.textContent = 'const aaaa = b + 1'
    document.body.appendChild(element)

    await wait(100)

    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    expect(spy2).toHaveBeenCalledTimes(1)
    expect(spy2).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'ReferenceError',
        value: 'b is not defined',
        stacktrace: expect.arrayContaining([
          expect.objectContaining({
            filename: expect.any(String),
            function: expect.any(String),
            lineno: expect.any(Number),
            colno: expect.any(Number)
          })
        ])
      }
    })

    aegis2.destroy()
    aegis1.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
    document.body.removeChild(element)
  })

  it('Chromium: trigger error event with no location', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = 'error\n at Array.forEach (native)'

    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [{ filename: 'native', function: 'Array.forEach' }]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse error with webpack URLs', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `TypeError: Cannot read property 'error' of undefined
          at TESTTESTTEST.eval(webpack:///./src/components/test/test.jsx?:295:108)
          at TESTTESTTEST.render(webpack:///./src/components/test/test.jsx?:272:32)
          at TESTTESTTEST.tryRender(webpack:///./~/react-transform-catch-errors/lib/index.js?:34:31)
          at TESTTESTTEST.proxiedMethod(webpack:///./~/react-proxy/modules/createPrototypeProxy.js?:44:30)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          {
            filename: 'webpack:///./~/react-proxy/modules/createPrototypeProxy.js?',
            function: 'TESTTESTTEST.proxiedMethod',
            lineno: 44,
            colno: 30
          },
          {
            filename: 'webpack:///./~/react-transform-catch-errors/lib/index.js?',
            function: 'TESTTESTTEST.tryRender',
            lineno: 34,
            colno: 31
          },
          {
            filename: 'webpack:///./src/components/test/test.jsx?',
            function: 'TESTTESTTEST.render',
            lineno: 272,
            colno: 32
          },
          {
            filename: 'webpack:///./src/components/test/test.jsx?',
            function: 'TESTTESTTEST.eval',
            lineno: 295,
            colno: 108
          }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse error with webpack error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `
  ChunkLoadError: Loading chunk app_bootstrap_initializeLocale_tsx failed.
  (error: https://s1.sentry-cdn.com/_static/dist/sentry/chunks/app_bootstrap_initializeLocale_tsx.abcdefg.js)
    at (error: (/_static/dist/sentry/chunks/app_bootstrap_initializeLocale_tsx.abcdefg.js))
    at key(webpack/runtime/jsonp chunk loading:27:18)
    at <anonymous> (webpack/runtime/ensure chunk:6:25)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          {
            filename: 'webpack/runtime/ensure chunk',
            function: '?',
            lineno: 6,
            colno: 25
          },
          {
            filename: 'webpack/runtime/jsonp chunk loading',
            function: 'key',
            lineno: 27,
            colno: 18
          },
          {
            filename: '/_static/dist/sentry/chunks/app_bootstrap_initializeLocale_tsx.abcdefg.js',
            function: '?'
          },
          {
            filename: 'https://s1.sentry-cdn.com/_static/dist/sentry/chunks/app_bootstrap_initializeLocale_tsx.abcdefg.js',
            function: '?'
          }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse error with blob URLs', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `Error: test
          at Error (native)
          at s (blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379:31:29146)
          at Object.d [as add] (blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379:31:30039)
          at blob:http%3A//localhost%3A8080/d4eefe0f-361a-4682-b217-76587d9f712a:15:10978
          at blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379:1:6911
          at n.fire (blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379:7:3019)
          at n.handle (blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379:7:2863)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          {
            filename: 'blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379',
            function: 'n.handle',
            lineno: 7,
            colno: 2863
          },
          {
            filename: 'blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379',
            function: 'n.fire',
            lineno: 7,
            colno: 3019
          },
          {
            filename: 'blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379',
            function: '?',
            lineno: 1,
            colno: 6911
          },
          {
            filename: 'blob:http%3A//localhost%3A8080/d4eefe0f-361a-4682-b217-76587d9f712a',
            function: '?',
            lineno: 15,
            colno: 10978
          },
          {
            filename: 'blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379',
            function: 'Object.d [as add]',
            lineno: 31,
            colno: 30039
          },
          {
            filename: 'blob:http%3A//localhost%3A8080/abfc40e9-4742-44ed-9dcd-af8f99a29379',
            function: 's',
            lineno: 31,
            colno: 29146
          },
          { filename: 'native', function: 'Error' }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse nested eval()', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `
      Error: message string
        at baz (eval at foo (eval at speak (http://localhost:8080/file.js:21:17)), <anonymous>:1:30)
        at foo (eval at speak (http://localhost:8080/file.js:21:17), <anonymous>:2:96)
        at eval (eval at speak (http://localhost:8080/file.js), <anonymous>:4:18)
        at Object.speak (http://localhost:8080/file.js:21:17)
        at http://localhost:8080/file.js:31:13`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://localhost:8080/file.js', function: '?', lineno: 31, colno: 13 },
          { filename: 'http://localhost:8080/file.js', function: 'Object.speak', lineno: 21, colno: 17 },
          { filename: 'eval at speak (http://localhost:8080/file.js), <anonymous>', function: 'eval', lineno: 4, colno: 18 },
          { filename: 'http://localhost:8080/file.js', function: 'foo', lineno: 21, colno: 17 },
          { filename: 'http://localhost:8080/file.js', function: 'baz', lineno: 21, colno: 17 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse frames with async urls', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const LONG_STR = 'A'.repeat(1040)
    const error = new Error('error message')
    error.stack = `Error: bad
          at callAnotherThing (http://localhost:5000/:20:16)
          at Object.callback (async http://localhost:5000/:25:7)
          at test (http://localhost:5000/:33:23)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://localhost:5000/', function: 'test', lineno: 33, colno: 23 },
          { filename: 'http://localhost:5000/', function: 'Object.callback', lineno: 25, colno: 7 },
          { filename: 'http://localhost:5000/', function: 'callAnotherThing', lineno: 20, colno: 16 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: drop frames that are over 1kb', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const LONG_STR = 'A'.repeat(1040)
    const error = new Error('error message')
    error.stack = `Error: bad
          at aha (http://localhost:5000/:39:5)
          at Foo.testMethod (http://localhost:5000/${LONG_STR}:44:7)
          at http://localhost:5000/:50:19`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://localhost:5000/', function: '?', lineno: 50, colno: 19 },
          { filename: 'http://localhost:5000/', function: 'aha', lineno: 39, colno: 5 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: drop frames that are over 50 lines', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const LONG_STR = 'A'.repeat(1040)
    const error = new Error('error message')
    error.stack = `Error: bad
          ${Array.from({ length: 60 }, (_, i) => `at http://localhost:5000/:${i}:19`).join('\n')}`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: Array.from({ length: 50 }, (_, i) => ({
          filename: 'http://localhost:5000/',
          function: '?',
          lineno: i,
          colno: 19
        })).reverse()
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Chromium: parse error stack', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `Error: error message
      at functionName (http://path/to/file.js:20:30)
      at http://path/to/file.js:25:40`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://path/to/file.js', function: '?', lineno: 25, colno: 40 },
          { filename: 'http://path/to/file.js', function: 'functionName', lineno: 20, colno: 30 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Firefox: parse error stack', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `functionName@http://path/to/file.js:20:30
      @http://path/to/file.js:25:40`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://path/to/file.js', function: '?', lineno: 25, colno: 40 },
          { filename: 'http://path/to/file.js', function: 'functionName', lineno: 20, colno: 30 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Firefox: parse errors with eval URLs', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `
    baz@http://localhost:8080/file.js > eval:1:30
        foo@http://localhost:8080/file.js line 26 > eval:2:96
        @http://localhost:8080/file.js line 26 > eval:4:18
        speak@http://localhost:8080/file.js:26:17
        @http://localhost:8080/file.js:33:9`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://localhost:8080/file.js', function: '?', lineno: 33, colno: 9 },
          { filename: 'http://localhost:8080/file.js', function: 'speak', lineno: 26, colno: 17 },
          { filename: 'http://localhost:8080/file.js', function: 'eval', lineno: 26 },
          { filename: 'http://localhost:8080/file.js', function: 'foo', lineno: 26 },
          { filename: 'http://localhost:8080/file.js > eval', function: 'baz', lineno: 1, colno: 30 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Safari: parse error stack', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `functionName@http://path/to/file.js:20:30
      global code@http://path/to/file.js:25:40`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          { filename: 'http://path/to/file.js', function: 'global code', lineno: 25, colno: 40 },
          { filename: 'http://path/to/file.js', function: 'functionName', lineno: 20, colno: 30 }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Safari: parse exceptions for safari-extension', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `Error: wat
      at ClipperError@safari-extension:(//3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/commons.js:223036:10)
      at safari-extension:(//3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/topee-content.js:3313:26)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          {
            filename: 'safari-extension://3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/topee-content.js',
            function: '?',
            lineno: 3313,
            colno: 26
          },
          {
            filename: 'safari-extension://3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/commons.js',
            function: 'ClipperError',
            lineno: 223036,
            colno: 10
          }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('Safari: parse exceptions for safari-web-extension', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const globalErrorListener = (event: ErrorEvent) => {
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener, true)

    const error = new Error('error message')
    error.stack = `Error: wat
      at ClipperError@safari-web-extension:(//3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/commons.js:223036:10)
      at safari-web-extension:(//3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/topee-content.js:3313:26)`
    window.dispatchEvent(
      new ErrorEvent('error', {
        message: error.message,
        error: error
      })
    )

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'js_error',
      payload: {
        cate: 'error',
        timestamp: expect.any(Number),
        type: 'Error',
        value: 'error message',
        stacktrace: [
          {
            filename: 'safari-web-extension://3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/topee-content.js',
            function: '?',
            lineno: 3313,
            colno: 26
          },
          {
            filename: 'safari-web-extension://3284871F-A480-4FFC-8BC4-3F362C752446/2665fee0/commons.js',
            function: 'ClipperError',
            lineno: 223036,
            colno: 10
          }
        ]
      }
    })

    aegis.destroy()
    vi.restoreAllMocks()
    window.removeEventListener('error', globalErrorListener, true)
  })

  it('detect vue error handler (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $root: {
        constructor: {
          config: {
            errorHandler: () => {}
          }
        },
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('Detecting that the Vue framework is being used, configure the errorHandler method to report Vue errors.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue error handler with Aegis captureException (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $root: {
        constructor: {
          config: {
            errorHandler: function () {
              window['aegis'].captureException()
            }
          }
        },
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue error handler (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      version: '3.2.31',
      config: {
        errorHandler: () => {}
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('Detecting that the Vue framework is being used, configure the errorHandler method to report Vue errors.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue error handler with Aegis captureException (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      version: '3.2.31',
      config: {
        errorHandler: function () {
          window['aegis'].captureException()
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue with no error handler (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $root: {
        constructor: {
          config: {}
        },
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('Detecting that the Vue framework is being used, configure the errorHandler method to report Vue errors.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue with no error handler (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      version: '3.2.31',
      config: {}
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).toHaveBeenCalledWith('Detecting that the Vue framework is being used, configure the errorHandler method to report Vue errors.')

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue with no version (vue2)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $root: {
        constructor: {
          config: {
            errorHandler: () => {}
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('detect vue with no version (vue3)', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue_app__'] = {
      config: {
        errorHandler: () => {}
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError()]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  it('vueCheck is false', async () => {
    const spy = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spy)

    const element = document.createElement('div')

    element['__vue__'] = {
      $root: {
        constructor: {
          config: {
            errorHandler: () => {}
          }
        },
        $options: {
          _base: {
            version: '2.6.14'
          }
        }
      }
    }

    document.body.appendChild(element)

    const aegis = new Aegis({
      id: 'test',
      integrations: [jsError({ vueCheck: false })]
    })

    await wait(4100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.restoreAllMocks()
    document.body.removeChild(element)
  })

  // same origin: unhandledrejection event capture
  // it('should correctly capture unhandledrejection event', async () => {
  //   const spy = vi.spyOn(aegisInstance, 'report')

  //   window.dispatchEvent(MOCK_ERRORS.promiseRejectionEvent.error)
  //   expect(spy).toHaveBeenLastCalledWith(MOCK_ERRORS.promiseRejectionEvent.report)

  //   window.dispatchEvent(MOCK_ERRORS.promiseRejectionEventWithString.error)
  //   expect(spy).toHaveBeenLastCalledWith(MOCK_ERRORS.promiseRejectionEventWithString.report)
  // })

  // TODO: cross-origin script error capture
  // it('should correctly capture cross-origin script error', () => {})

  // TODO: cross-origin unhandledrejection event capture
  // it('should correctly capture cross-origin unhandledrejection event', () => {})

  // TODO: cross-origin global async error capture
  // it('should correctly capture cross-origin global async error', () => {})

  // it('should correctly dedupe error', () => {
  //   const spy = vi.spyOn(aegisInstance, 'report')

  //   const error = new Error('Test error')
  //   error.stack = 'Error: Test error\n    at Object.<anonymous> (path/to/file.js:10:2)'

  //   const errorEvent = new ErrorEvent('error', {
  //     message: 'Test error event',
  //     filename: 'path/to/file.js',
  //     lineno: 10,
  //     colno: 2,
  //     error: error
  //   })

  //   window.dispatchEvent(errorEvent)
  //   expect(spy).toHaveBeenCalledTimes(1)

  //   window.dispatchEvent(errorEvent)
  //   expect(spy).toHaveBeenCalledTimes(1)
  // })

  // it('should correctly ignore errors', () => {
  //   aegisInstance && aegisInstance.destroy()

  //   const aegis = new Aegis({
  //     id: 'test',
  //     integrations: [
  //       jsError({
  //         ignoreErrors: [/ignore_this_error/, 'ignore_that_error']
  //       })
  //     ]
  //   })

  //   const spy = vi.spyOn(aegis, 'report')

  //   const errors = [
  //     {
  //       error: new Error('ignore_this_error: This error should be ignored'),
  //       report: false
  //     },
  //     {
  //       error: new Error('ignore_that_error: This error should also be ignored'),
  //       report: false
  //     },
  //     {
  //       error: new Error('do_not_ignore_the_error: This error should not be ignored'),
  //       report: true
  //     }
  //   ]

  //   errors.forEach((err) => {
  //     window.dispatchEvent(
  //       new ErrorEvent('error', {
  //         message: err.error.message,
  //         error: err.error
  //       })
  //     )
  //     err.report ? expect(spy).toHaveBeenCalled() : expect(spy).not.toHaveBeenCalled()
  //   })

  //   aegis.destroy()
  // })

  // it('should correctly enable onerror', () => {
  //   aegisInstance && aegisInstance.destroy()

  //   const aegis = new Aegis({
  //     id: 'test',
  //     integrations: [
  //       jsError({
  //         onerror: false
  //       })
  //     ]
  //   })

  //   const spy = vi.spyOn(aegis, 'report')

  //   window.dispatchEvent(MOCK_ERRORS.errorEvent.error)
  //   expect(spy).not.toHaveBeenCalled()

  //   aegis.destroy()
  // })

  // it('should correctly enable onunhandledrejection', () => {
  //   aegisInstance && aegisInstance.destroy()

  //   const aegis = new Aegis({
  //     id: 'test',
  //     integrations: [
  //       jsError({
  //         onunhandledrejection: false
  //       })
  //     ]
  //   })

  //   const spy = vi.spyOn(aegis, 'report')

  //   window.dispatchEvent(MOCK_ERRORS.promiseRejectionEvent.error)
  //   expect(spy).not.toHaveBeenCalled()

  //   aegis.destroy()
  // })

  // it('should correctly enable logErrorsInConsole', () => {
  //   // when logErrorsInConsole is true
  //   if (window.onerror) {
  //     const onerrorSpy = vi.fn(window.onerror)
  //     const res = onerrorSpy('Test error', 'path/to/file.js', 10, 2, baseError)
  //     expect(res).not.toBe(true)
  //     onerrorSpy.mockRestore()
  //   }
  //   aegisInstance && aegisInstance.destroy()

  //   // when logErrorsInConsole is false
  //   const aegis = new Aegis({
  //     id: 'test',
  //     integrations: [
  //       jsError({
  //         logErrorsInConsole: false
  //       })
  //     ]
  //   })

  //   expect(window.onerror).toBeInstanceOf(Function)
  //   window.onerror && expect(window.onerror('Test error', 'path/to/file.js', 10, 2, baseError)).toBe(true)
  //   aegis.destroy()
  // })

  // it('should correctly capture exception', () => {
  //   const spy = vi.spyOn(aegisInstance, 'report')

  //   aegisInstance.captureException(MOCK_ERRORS.errorEvent.error)
  //   expect(spy).toHaveBeenLastCalledWith(MOCK_ERRORS.errorEvent.report)

  //   aegisInstance.captureException(MOCK_ERRORS.promiseRejectionEvent.error)
  //   expect(spy).toHaveBeenLastCalledWith(MOCK_ERRORS.promiseRejectionEvent.report)

  //   aegisInstance.captureException(MOCK_ERRORS.promiseRejectionEventWithString.error)
  //   expect(spy).toHaveBeenLastCalledWith(MOCK_ERRORS.promiseRejectionEventWithString.report)

  //   // Object data
  //   const obj = { msg: 'object data' }
  //   const extra = { customKey: 'customValue' }
  //   const react = { version: '1.0.0', componentStack: 'Error: Test error\n    at Object.<anonymous> (path/to/file.js:10:2)' }
  //   aegisInstance.captureException(obj, extra, react)
  //   expect(spy).toHaveBeenLastCalledWith({
  //     type: 'js',
  //     payload: {
  //       error: { message: json.stringify(obj) },
  //       extra,
  //       react
  //     }
  //   })

  //   // Other data type
  //   const str = new Date()
  //   aegisInstance.captureException(str, extra, react)
  //   expect(spy).toHaveBeenLastCalledWith({
  //     type: 'js',
  //     payload: {
  //       error: { message: str.toString() },
  //       extra,
  //       react
  //     }
  //   })
  // })

  // TODO: option captureGlobalAsync
  // it('should correctly enable captureGlobalAsync', () => {})
})
