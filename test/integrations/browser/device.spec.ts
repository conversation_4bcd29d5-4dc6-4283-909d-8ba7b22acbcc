import Aegis from '../../../src/core/Aegis'
import { describe, expect, it, test, vi } from 'vitest'
import device, { getDeviceVendorAndModel } from '../../../src/integrations/browser/device'

function createAegisAndSpy(options?: any): { aegis: Aegis; spy: any } {
  let spy = null as any
  const aegis = new Aegis({
    id: 'test',
    integrations: [
      {
        name: 'test',
        setup: (_aegis) => {
          spy = vi.spyOn(_aegis, 'report') as any
        }
      },
      device(options)
    ]
  })

  return { aegis, spy }
}

describe('getDeviceVendorAndModel function', () => {
  it('is iPhone device', () => {
    const ua = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'APPLE',
      model: 'iPhone'
    })
  })

  it('is Samsung device', () => {
    const ua =
      'Mozilla/5.0 (Linux; Android 10; SM-G9750 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.19 SP-engine/2.15.0 baiduboxapp/********** (Baidu; P1 10)'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'SAMSUNG',
      model: 'SM-G9750'
    })
  })

  it('is Huawei device', () => {
    const ua =
      'Mozilla/5.0 (Linux; Android 10; MED-AL00 Build/HUAWEIMED-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/97.0.4692.98 Mobile Safari/537.36 T7/13.63 SP-engine/3.3.0 baiduboxapp/********** (Baidu; P1 10) NABar/1.0'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'HUAWEI',
      model: 'MED-AL00'
    })
  })

  it('is vivo device', () => {
    const ua =
      'Mozilla/5.0 (Linux; Android 11; V2046A Build/RP1A.200720.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.188 Mobile Safari/537.36 XWEB/1260097 MMWEBSDK/20240501 MMWEBID/333 MicroMessenger/8.0.50.2701(0x28003257) WeChat/arm64 Weixin NetType/5G Language/zh_CN ABI/arm64'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'VIVO',
      model: 'V2046A'
    })
  })

  it('is Xiaomi device', () => {
    const ua =
      'Mozilla/5.0 (Linux; Android 14; 23127PN0CC Build/UKQ1.230804.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.188 Mobile Safari/537.36 XWEB/1260097 MMWEBSDK/20240501 MMWEBID/8051 MicroMessenger/8.0.50.2701(0x28003257) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'XIAOMI',
      model: '23127PN0CC'
    })
  })

  it('is HONOR device', () => {
    const ua =
      'Mozilla/5.0 (Linux; Android 14; FCP-AN10 Build/HONORFCP-AN10; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.179 Mobile Safari/537.36 SoulApp/5.46.0/RkNQLUFOMTCbLubhjUVQ6A__3533a6a927b764c65e9f53633eb64f2b/10211814/arm64-v8a/a'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'HUAWEI',
      model: 'FCP-AN10'
    })
  })

  it('not match', () => {
    const ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    const result = getDeviceVendorAndModel(ua)
    expect(result).toEqual({
      vendor: 'Unknown',
      model: 'Unknown'
    })
  })

  it('is LG device', () => {
    const testCases = [
      {
        ua: 'Mozilla/5.0 (Linux; Android 5.0; LG-D855 Build/LRX21R.A1445306351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.76 Mobile Safari/537.36',
        expected: { vendor: 'LG', model: 'D855' }
      },
      {
        ua: 'Mozilla/5.0 (Linux; Android 6.0; LG-H815 Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.98 Mobile Safari/537.36',
        expected: { vendor: 'LG', model: 'H815' }
      }
    ]

    testCases.forEach(({ ua, expected }) => {
      const result = getDeviceVendorAndModel(ua)
      expect(result).toEqual(expected)
    })
  })
})

describe('Device', () => {
  test('invalid device', async () => {
    vi.stubGlobal('navigator', { userAgent: undefined, platform: undefined, language: undefined })
    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Unknown',
        browser: 'Unknown',
        browserVer: 'Unknown',
        engine: 'Unknown',
        engineVer: 'Unknown',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'Unknown',
        lang: 'Unknown',
        dpr: 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('pc device', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })
    vi.stubGlobal('devicePixelRatio', undefined)

    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Chrome',
        browserVer: '91.0.4472.124',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('linux device', async () => {
    const userAgent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })
    vi.stubGlobal('devicePixelRatio', undefined)

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Linux',
        browser: 'Chrome',
        browserVer: '*********',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('iphone mobile device', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'

    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })
    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'Safari',
        browserVer: '604.1',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('android mobile device', async () => {
    const userAgent = 'Mozilla/5.0 (Linux; Android 7.1.1; OPPO R9sk) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36'

    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })
    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Android',
        browser: 'Chrome',
        browserVer: '76.0.3809.111',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('iPad device', async () => {
    const userAgent = 'Mozilla/5.0(iPad; U; CPU iPhone OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B314 Safari/531.21.10'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPad', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'Safari',
        browserVer: '531.21.10',
        engine: 'WebKit',
        engineVer: '531.21.10',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'tablet',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPad'
      }
    })

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('Mac OS X os device', async () => {
    const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPad', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'MacOS',
        browser: 'Safari',
        browserVer: '605.1.15',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'Macintosh'
      }
    })

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('Chrome OS os device', async () => {
    const userAgent = 'Mozilla/5.0 (X11; CrOS x86_64 14526.64.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPad', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Chrome OS',
        browser: 'Chrome',
        browserVer: '91.0.4472.114',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('ubuntu device', async () => {
    const userAgent = 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Linux x86_64', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Ubuntu',
        browser: 'Firefox',
        browserVer: '89.0',
        engine: 'Gecko',
        engineVer: '20100101',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('freebsd device', async () => {
    const userAgent = 'Mozilla/5.0 (X11; FreeBSD amd64; rv:89.0) Gecko/20100101 Firefox/89.0'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'FreeBSD amd64', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'FreeBSD',
        browser: 'Firefox',
        browserVer: '89.0',
        engine: 'Gecko',
        engineVer: '20100101',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser engine is Trident', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'IE',
        browserVer: '11.0',
        engine: 'Trident',
        engineVer: '7.0',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser engine is Presto', async () => {
    const userAgent = 'Opera/9.80 (Windows NT 6.1; U; en) Presto/2.8.131 Version/11.11'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Unknown',
        browserVer: 'Unknown',
        engine: 'Presto',
        engineVer: '2.8.131',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Opera', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.146'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Opera',
        browserVer: '77.0.4054.146',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Edge', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/14.14393'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Edge',
        browserVer: '14.14393',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Edge(Chromium)', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Edge (Chromium)',
        browserVer: '91.0.864.59',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Chrome(iOS)', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/91.0.4472.77'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'Chrome (IOS)',
        browserVer: '91.0.4472.77',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Firefox(iOS)', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/31.0'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'Firefox (IOS)',
        browserVer: '31.0',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('browser is Samsung Internet', async () => {
    const userAgent = 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/15.0'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Android',
        browser: 'Samsung Internet',
        browserVer: '15.0',
        engine: 'WebKit',
        engineVer: '537.36',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'SAMSUNG',
        model: 'SM-G991B'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('WeChat browser on iOS', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x1800323d) NetType/WIFI Language/zh_CN'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'zh-CN' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'WeChat',
        browserVer: '8.0.50',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'zh-CN',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('Soul app on iOS', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 SoulBegin-iOS-5.37.0-WIFI-SoulEnd'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'SoulApp',
        browserVer: '5.37.0',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('Generic app-based browser', async () => {
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 XYZBegin-iOS-1.2.3-WIFI-XYZEnd'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'iPhone', language: 'en-US' })

    const { aegis, spy } = createAegisAndSpy()
    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'IOS',
        browser: 'XYZ',
        browserVer: '1.2.3',
        engine: 'WebKit',
        engineVer: '605.1.15',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'mobile',
        lang: 'en-US',
        dpr: window.devicePixelRatio || 1,
        vendor: 'APPLE',
        model: 'iPhone'
      }
    })
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('add extra info', async () => {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    vi.stubGlobal('navigator', { userAgent: userAgent, platform: 'Win32', language: 'en-US' })
    vi.stubGlobal('devicePixelRatio', undefined)

    const { aegis, spy } = createAegisAndSpy({
      extra: (ua) => {
        return {
          version: '1.0.0'
        }
      }
    })

    expect(spy).toHaveBeenLastCalledWith({
      type: 'device',
      payload: {
        os: 'Windows',
        browser: 'Chrome',
        browserVer: '91.0.4472.124',
        engine: 'WebKit',
        engineVer: '537.36',
        version: '1.0.0',
        sr: `${window.screen.width}x${window.screen.height}`,
        device: 'pc',
        lang: 'en-US',
        dpr: 1,
        vendor: 'Unknown',
        model: 'Unknown'
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })
})
