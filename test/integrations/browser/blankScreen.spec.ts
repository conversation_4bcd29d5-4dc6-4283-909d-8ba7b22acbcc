import Aegis from '../../../src/core/Aegis'
import { events } from '../../../src/utils'
import * as fixtures from '../../fixtures/browser/blankScreen'
import blankScreen from '../../../src/integrations/browser/blankScreen'
import { describe, expect, test, vi, beforeEach, afterEach } from 'vitest'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('BlankScreen', async () => {
  const originalReadyState = Object.getOwnPropertyDescriptor(document, 'readyState')

  beforeEach(() => {
    Object.defineProperty(document, 'readyState', {
      value: 'loading',
      configurable: true
    })
  })

  afterEach(() => {
    document.body.innerHTML = ''
    Object.defineProperty(document, 'readyState', originalReadyState || {})
  })

  test('should not report blank screen in small viewport', async () => {
    document.body.innerHTML = fixtures.blankScreen

    const originalInnerWidth = window.innerWidth
    const originalInnerHeight = window.innerHeight

    Object.defineProperties(window, {
      innerWidth: {
        value: 1,
        configurable: true
      },
      innerHeight: {
        value: 1,
        configurable: true
      }
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    Object.defineProperties(window, {
      innerWidth: {
        value: originalInnerWidth,
        configurable: true
      },
      innerHeight: {
        value: originalInnerHeight,
        configurable: true
      }
    })

    vi.unstubAllGlobals()
    aegis.destroy()
  })

  test('elementsFromPoint not available', async () => {
    const originalElementFromPoint = document.elementFromPoint

    document.elementFromPoint = undefined as any

    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spyReport = vi.spyOn(aegis, 'report')

    events.on('error', errorListener)

    window.dispatchEvent(new Event('load'))

    await wait(2100)

    expect(spyReport).not.toHaveBeenCalled()
    expect(spy).toHaveBeenLastCalledWith('document.elementFromPoint is not supported')

    document.elementFromPoint = originalElementFromPoint

    events.off('error', errorListener)

    aegis.destroy()
  })

  test('history api not supported', async () => {
    vi.stubGlobal('history', {
      pushState: null
    })

    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false

    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
    vi.unstubAllGlobals()
  })

  test('blank screen detection, options params is null', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false

    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen(null as any)]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)
    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })

  test('blank screen detection with document complete', async () => {
    document.body.innerHTML = fixtures.blankScreen

    Object.defineProperty(document, 'readyState', {
      value: 'complete',
      configurable: true
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(2100)

    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    aegis.destroy()
  })

  test('blank screen detection', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false

    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })

  test('blank screen detection with skeleton', async () => {
    document.body.innerHTML = fixtures.skeleton

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'], skeleton: true })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(200)

    document.body.innerHTML = fixtures.skeleton

    await wait(2100)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })

  test('non-blank screen detection', async () => {
    document.body.innerHTML = fixtures.nonBlankScreen

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })
    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })

  test('non-blank screen detection with skeleton', async () => {
    document.body.innerHTML = fixtures.skeleton

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'], skeleton: true })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(200)

    document.body.innerHTML = fixtures.nonBlankScreenWithSkeleton
    await wait(2100)
    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })

  test('non-blank screen detection with hash mode', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'], mode: 'hash' })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)

    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    spy.mockClear()

    location.hash = '#/hello?a=1'

    document.body.innerHTML = fixtures.nonBlankScreen

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()

    window.removeEventListener('load', loadEventListener, true)
  })

  test('non-blank screen detection with history mode', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)

    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    spy.mockClear()

    history.pushState({}, '', '/hello')

    document.body.innerHTML = fixtures.nonBlankScreen

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()

    window.removeEventListener('load', loadEventListener, true)
  })

  test('non-blank screen detection with replaceState operation', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false
    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'], mode: 'history' })]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)

    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    spy.mockClear()

    history.replaceState({}, '', '/hello')

    document.body.innerHTML = fixtures.nonBlankScreen

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()

    window.removeEventListener('load', loadEventListener, true)
  })

  test('multiple aegis instances', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    document.body.innerHTML = fixtures.blankScreen

    Object.defineProperty(document, 'readyState', {
      value: 'complete',
      configurable: true
    })

    const aegis1 = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const aegis2 = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy1 = vi.spyOn(aegis1, 'report')
    const spy2 = vi.spyOn(aegis2, 'report')

    await wait(2100)

    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })
    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy2).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    history.pushState({}, '', '/hello')

    await wait(2100)

    expect(spy1).toHaveBeenCalledTimes(2)
    expect(spy1).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: `${location.origin}/hello`
      }
    })
    expect(spy2).toHaveBeenCalledTimes(2)
    expect(spy2).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: `${location.origin}/hello`
      }
    })

    aegis2.destroy()
    aegis1.destroy()
    history.back()

    vi.restoreAllMocks()
  })

  test('elementsFromPoint return null element', async () => {
    const originalElementFromPoint = document.elementFromPoint

    document.elementFromPoint = () => null

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(2100)

    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    document.elementFromPoint = originalElementFromPoint

    aegis.destroy()
  })

  test('blank screen detection with ignoreUrls', async () => {
    document.body.innerHTML = fixtures.blankScreen

    Object.defineProperty(document, 'readyState', {
      value: 'complete',
      configurable: true
    })

    history.pushState({}, '', '/api')

    const aegis = new Aegis({
      id: 'test',
      integrations: [blankScreen({ rootSelector: ['html', 'body', '#app'], ignoreUrls: ['/api'] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(2100)

    expect(spy).not.toHaveBeenCalled()

    history.back()

    aegis.destroy()
  })

  test('use pageview mode', async () => {
    document.body.innerHTML = fixtures.blankScreen

    let pageLoaded = false

    const loadEventListener = () => (pageLoaded = true)

    window.addEventListener('load', loadEventListener, true)

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        blankScreen({ rootSelector: ['html', 'body', '#app'] }),
        {
          name: 'pageview',
          options: {
            mode: 'history'
          },
          setup: vi.fn(),
          tearDown: vi.fn()
        }
      ]
    })

    const spy = vi.spyOn(aegis, 'report')

    expect(pageLoaded).toBe(false)

    window.dispatchEvent(new Event('load'))

    expect(pageLoaded).toBe(true)

    await wait(2100)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'blank',
      payload: {
        url: window.location.href
      }
    })

    aegis.destroy()
    window.removeEventListener('load', loadEventListener, true)
  })
})
