import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import feature from '../../../src/integrations/browser/feature'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Feature', () => {
  test('run detection when idle', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [feature()]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('run detection when immediate', async () => {
    let spy = null

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: (aegis) => {
            spy = vi.spyOn(aegis, 'report') as any
          }
        },
        feature({ immediate: true })
      ]
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('skip disabled features', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        feature({
          features: [
            { name: 'skippedFeature', skip: true, type: 'API', strategy: 'property', params: { property: 'document' } },
            { name: 'jxl', skip: true, type: 'IMAGE', strategy: 'format', async: true, params: { base64: 'data:image/jxl;base64,/woIELASCAgQAFwASxLFgkWAHL0xqnCBCV0qDp901Te/5QM=' } }
          ]
        })
      ]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('check params legality', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        feature({
          features: [
            { name: 'testFeature', type: 'API', strategy: 'property', params: { value: 'document' } },
            { name: 'testFeature2', type: 'API', strategy: 'ctor', params: { value: 'document' } },
            { name: 'testFeature3', type: 'CSS', strategy: 'property', params: { value: 'document' } },
            { name: 'testFeature4', type: 'CSS', strategy: 'property', params: null as any },
            { name: 'testFeature5', type: 'API', strategy: 'property', params: { property: 'hello', context: 'world' } },
            { name: 'testFeature6', type: 'API', strategy: 'property', params: { property: 'hello', context: 'world.xxx' } },
            { name: 'testFeature7', type: 'API', strategy: 'ctor', params: { class: 'hello' } },
            { name: 'testFeature8', type: 'IMAGE', async: true, strategy: 'format', params: { value: 'hello' } },
            { name: 'testFeature9', type: 'IMAGE', async: true, strategy: 'format', params: { base64: 'hello' } },
            { name: 'testFeature10', type: 'IMAGE', async: true, strategy: 'format', params: null as any },
            { name: 'testFeature11', type: 'IMAGE', async: true, strategy: 'format', params: null as any }
          ]
        })
      ]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'testFeature', type: 'API', supported: false },
          { name: 'testFeature2', type: 'API', supported: false },
          { name: 'testFeature3', type: 'CSS', supported: false },
          { name: 'testFeature5', type: 'API', supported: false },
          { name: 'testFeature6', type: 'API', supported: false },
          { name: 'testFeature7', type: 'API', supported: false },
          { name: 'testFeature8', type: 'IMAGE', supported: false },
          { name: 'webp', type: 'IMAGE', supported: true },
          { name: 'testFeature9', type: 'IMAGE', supported: false }
        ]
      }
    })

    aegis.destroy()
  })

  test('custom features', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [feature({ features: [{ name: 'webGPU', type: 'API', strategy: 'property', params: { property: 'gpu', context: 'navigator' } }] })]
    })

    const spy = vi.spyOn(aegis, 'report')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webGPU', type: 'API', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('use remote features', async () => {
    let spy = null
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: (aegis) => {
            spy = vi.spyOn(aegis, 'report') as any
          }
        },
        feature({ remoteUrl: 'http://localhost:3333/feats' })
      ]
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'position', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('use remote features, but response is not json', async () => {
    let spy = null
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: (aegis) => {
            spy = vi.spyOn(aegis, 'report') as any
          }
        },
        feature({ remoteUrl: 'http://localhost:3333/feats/failed' })
      ]
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })

  test('use remote features, but load failed', async () => {
    let spy = null
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: (aegis) => {
            spy = vi.spyOn(aegis, 'report') as any
          }
        },
        feature({ remoteUrl: 'http://localhost:3333/features.json' })
      ]
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith({
      type: 'feature',
      payload: {
        features: [
          { name: 'promise', type: 'API', supported: true },
          { name: 'fetch', type: 'API', supported: true },
          { name: 'performance', type: 'API', supported: true },
          { name: 'requestAnimationFrame', type: 'API', supported: true },
          { name: 'MutationObserver', type: 'API', supported: true },
          { name: 'Blob', type: 'API', supported: true },
          { name: 'IntersectionObserver', type: 'API', supported: true },
          { name: 'ResizeObserver', type: 'API', supported: true },
          { name: 'serviceWorker', type: 'API', supported: true },
          { name: 'cryptoSubtleDigest', type: 'API', supported: true },
          { name: 'WebAssembly', type: 'API', supported: true },
          { name: 'URL', type: 'API', supported: true },
          { name: 'URLSearchParams', type: 'API', supported: true },
          { name: 'arrowFunction', type: 'API', supported: true },
          { name: 'grid', type: 'CSS', supported: true },
          { name: 'webp', type: 'IMAGE', supported: true }
        ]
      }
    })

    aegis.destroy()
  })
})
