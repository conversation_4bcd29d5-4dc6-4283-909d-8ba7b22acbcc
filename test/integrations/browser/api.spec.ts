import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import api from '../../../src/integrations/browser/api'
import ConsoleTransport from '../../../src/transports/console'
import { urlToString, isEmptyObject, headersToString, bodyToString, parseFetchArgs, splitUrl } from '../../../src/integrations/browser/api/utils'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Api', () => {
  const hostname = `http://localhost:3333`

  describe('fetch', () => {
    test('call fetch', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post?hello=world&test=1`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 })
      }).then(async (resp) => {
        expect(resp.status).toEqual(200)
        expect(await resp.json()).toEqual({ a: 1 })
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: 'hello=world&test=1',
          status: 200,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post?hello=world&test=1`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('call fetch with options', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 })
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      await fetch(`${hostname}/post?test=1&abc=def`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 })
      }).catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: 'test=1&abc=def',
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post?test=1&abc=def`,
          duration: expect.any(Number),
          requestData: JSON.stringify({ a: 1 }),
          responseData: `Failed to execute 'fetch' on 'Window': Request with GET/HEAD method cannot have body.`
        }
      })

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(3)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[2][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      await fetch(`${hostname}/status/404`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: new FormData()
      }).catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(4)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          requestData: '[FormData]',
          responseData: 'Not Found'
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in network error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')
      await fetch(`${hostname}/post`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 })
      }).catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number),
          requestData: JSON.stringify({ a: 1 }),
          responseData: `Failed to execute 'fetch' on 'Window': Request with GET/HEAD method cannot have body.`
        }
      })
      await fetch('invalid://localhost/post').catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'GET',
          requestType: 'fetch',
          base: 'invalid://localhost/post',
          url: 'invalid://localhost/post',
          duration: expect.any(Number),
          responseData: 'Failed to fetch'
        }
      })

      const abortController = new AbortController()

      setTimeout(() => abortController.abort())
      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 }),
        signal: abortController.signal
      }).catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(3)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number),
          requestData: JSON.stringify({ a: 1 }),
          responseData: expect.any(String)
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in server error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      await fetch(`${hostname}/status/404`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: new Blob(['post test'], { type: 'text/plain' })
      }).catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          requestData: '[Blob type: text/plain, size: 9]',
          responseData: 'Not Found'
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in success', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST'
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('aegis destroy should restore fetch', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })
      const countSpy = vi.fn()
      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(countSpy)
        .catch(() => {})

      await wait(100)

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis.destroy()

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(countSpy)
        .catch(() => {})

      expect(countSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledTimes(1)
    })

    test('call reportHttpRequest api', () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      aegis.reportHttpRequest({
        ok: true,
        query: '',
        base: `${hostname}/post`,
        url: `${hostname}/post`,
        status: 200,
        method: 'POST',
        duration: 100,
        headers: JSON.stringify({ 'Content-Type': 'application/json' }),
        requestData: JSON.stringify({ a: 1 }),
        responseData: 'response data',
        requestType: 'fetch'
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          status: 200,
          method: 'POST',
          duration: 100,
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          requestData: JSON.stringify({ a: 1 }),
          responseData: 'response data',
          requestType: 'fetch'
        }
      })

      aegis.destroy()
    })

    test('fetch callback error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST'
      })
        .then(() => {
          throw new Error('error')
        })
        .catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('only report error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST'
      })

      expect(reportSpy).toHaveBeenCalledTimes(0)

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(async (resp) => {
          expect(resp.status).toEqual(404)
          expect(await resp.text()).toEqual(expect.any(String))
        })
        .catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis.destroy()
    })

    test('report url match ignore', async () => {
      const countSpy = vi.fn()
      vi.spyOn(console, 'log').mockImplementation(vi.fn())

      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ ignoreUrls: [`${hostname}`] })],
        transports: [new ConsoleTransport('http://www.abc.com/report.gif')]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}`).then(countSpy)

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await fetch(`${hostname}/path`).then(countSpy)

      expect(countSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await fetch(`${hostname}/path2`).then(countSpy)

      expect(countSpy).toHaveBeenCalledTimes(3)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await fetch('invalid://localhost').catch(countSpy)

      expect(countSpy).toHaveBeenCalledTimes(4)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'GET',
          requestType: 'fetch',
          base: 'invalid://localhost',
          url: 'invalid://localhost',
          duration: expect.any(Number),
          responseData: 'Failed to fetch'
        }
      })

      await fetch('http://www.abc.com/report.gif').catch(countSpy)

      expect(countSpy).toHaveBeenCalledTimes(5)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'GET',
          requestType: 'fetch',
          base: 'invalid://localhost',
          url: 'invalid://localhost',
          duration: expect.any(Number),
          responseData: 'Failed to fetch'
        }
      })

      aegis.destroy()
      vi.restoreAllMocks()
    })

    test('wrong request, but response.text() exception', async () => {
      const originalFetch = window.fetch

      window.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 404,
        clone: () => ({
          clone: () => ({
            text: () => Promise.reject(new Error('text error'))
          })
        })
      })

      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        expect(resp.status).toEqual(404)
        expect(resp.ok).toEqual(false)
      })

      await wait(100)
      window.fetch = originalFetch
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: 'text error'
        }
      })

      aegis.destroy()
      window.fetch = originalFetch
    })

    test('correct request, but response.text() exception', async () => {
      const originalFetch = window.fetch

      window.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        clone: () => ({
          clone: () => ({
            text: () => Promise.reject(new Error('text error'))
          })
        })
      })

      const aegis = new Aegis({
        id: 'test',
        integrations: [
          api({
            reportPolicy: () => true
          })
        ]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        expect(resp.status).toEqual(200)
        expect(resp.ok).toEqual(true)
      })

      await wait(100)
      window.fetch = originalFetch
      expect(reportSpy).toHaveBeenCalledTimes(0)

      aegis.destroy()
      window.fetch = originalFetch
    })

    test('custom error report policy', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [
          api({
            reportPolicy: (responseText) => {
              try {
                const data = JSON.parse(responseText)
                return data.code !== 10001
              } catch (error) {
                return false
              }
            }
          })
        ]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: 10001, data: { a: 1 }, message: 'success' })
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(0)

      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: 10002, data: null, message: 'fail' })
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 500,
          method: 'POST',
          requestType: 'fetch',
          base: `${hostname}/post`,
          url: `${hostname}/post`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          requestData: JSON.stringify({ code: 10002, data: null, message: 'fail' }),
          responseData: JSON.stringify({ code: 10002, data: null, message: 'fail' })
        }
      })

      aegis.destroy()
    })

    test('multiple aegis instance', async () => {
      vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      const aegis1 = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const aegis2 = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy1 = vi.spyOn(aegis1, 'report')
      const reportSpy2 = vi.spyOn(aegis2, 'report')

      await fetch(`${hostname}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ a: 1 })
      }).then(async (resp) => {
        expect(resp.status).toEqual(200)
        expect(await resp.json()).toEqual({ a: 1 })
      })

      expect(reportSpy1).toHaveBeenCalledTimes(1)
      expect(reportSpy1).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'fetch',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })
      expect(reportSpy2).toHaveBeenCalledTimes(0)

      await fetch(`${hostname}/status/404`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).catch(() => {})

      await wait(100)

      expect(reportSpy1).toHaveBeenCalledTimes(2)
      expect(reportSpy1).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy1.mock.calls[1][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)
      expect(reportSpy2).toHaveBeenCalledTimes(1)
      expect(reportSpy2).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy2.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis2.destroy()
      aegis1.destroy()

      vi.restoreAllMocks()
    })

    test('fetch not support', async () => {
      const originalFetch = window.fetch

      // @ts-ignore
      delete window.fetch

      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      try {
        await fetch(`${hostname}/post`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ a: 1 })
        }).catch(() => {})
      } catch (error) {}

      expect(reportSpy).toHaveBeenCalledTimes(0)

      window.fetch = originalFetch
      aegis.destroy()
    })

    test('fetch request error, but request not contain body', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/post`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        body: ''
      }).catch(() => {})

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'GET',
          requestType: 'fetch',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number),
          responseData: `Failed to execute 'fetch' on 'Window': Request with GET/HEAD method cannot have body.`
        }
      })

      aegis.destroy()
    })

    test('only report error, but request header is empty', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await fetch(`${hostname}/status/404`, {
        method: 'GET'
      })
        .then(async (resp) => {
          expect(resp.status).toEqual(404)
          expect(await resp.text()).toEqual(expect.any(String))
        })
        .catch(() => {})

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'fetch',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis.destroy()
    })
  })

  describe('xhr', () => {
    test('call xhr', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post?test=1&abc=def`, true)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: 'test=1&abc=def',
          status: 200,
          method: 'POST',
          requestType: 'xhr',
          base: `${hostname}/post`,
          url: `${hostname}/post?test=1&abc=def`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('call xhr with options', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ a: 1 }))
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/post?hello=world&test=1`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ a: 1 }))
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: 'hello=world&test=1',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/post?hello=world&test=1`,
          base: `${hostname}/post`,
          duration: expect.any(Number),
          requestData: JSON.stringify({ a: 1 }),
          headers: '{"content-type":"application/json"}',
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[1][0].payload.responseData?.includes('Cannot GET /post')).toEqual(true)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(3)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: '{"content-type":"application/json"}',
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[2][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(new FormData())
        xhr.onload = () => resolve(0)
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(4)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: '{"content-type":"application/json"}',
          requestData: '[FormData]',
          responseData: 'Not Found'
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in network error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', 'http://www.example1.com/post', true)
        xhr.send()
        xhr.onload = () => resolve(0)
        xhr.onerror = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          status: 0,
          query: '',
          method: 'GET',
          requestType: 'xhr',
          url: 'http://www.example1.com/post',
          base: 'http://www.example1.com/post',
          duration: expect.any(Number)
        }
      })

      await wait(100)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.timeout = 1
        xhr.send(JSON.stringify({ a: 1 }))
        xhr.ontimeout = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          status: 0,
          ok: false,
          query: '',
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number),
          requestData: JSON.stringify({ a: 1 }),
          headers: '{"content-type":"application/json"}'
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in server error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(new Blob(['post test'], { type: 'text/plain' }))
        xhr.onload = () => resolve(0)
      })

      await wait(100)

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          status: 404,
          query: '',
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          requestData: '[Blob type: text/plain, size: 9]',
          responseData: 'Not Found'
        }
      })

      aegis.destroy()
    })

    test('validation of reported data in success', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          status: 200,
          query: '',
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            resolve(0)
          }
        }

        xhr.open('POST', `${hostname}/post`, true)

        xhr.send()
      })

      expect(reportSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('aegis destroy should restore xhr', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })
      const countSpy = vi.fn()
      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send()
      })

      await wait(100)

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis.destroy()

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledTimes(1)
    })

    test('call reportHttpRequest api', () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      aegis.reportHttpRequest({
        ok: true,
        query: '',
        url: `${hostname}/post`,
        base: `${hostname}/post`,
        status: 200,
        method: 'POST',
        duration: 100,
        headers: JSON.stringify({ 'Content-Type': 'application/json' }),
        requestData: JSON.stringify({ a: 1 }),
        responseData: 'response data',
        requestType: 'xhr'
      })

      expect(reportSpy).toHaveBeenCalled()
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          status: 200,
          method: 'POST',
          duration: 100,
          headers: JSON.stringify({ 'Content-Type': 'application/json' }),
          requestData: JSON.stringify({ a: 1 }),
          responseData: 'response data',
          requestType: 'xhr'
        }
      })

      aegis.destroy()
    })

    test('xhr callback error', async () => {
      vi.spyOn(console, 'error').mockImplementation(vi.fn())

      const handleError = (event: Event) => {
        event.preventDefault()
      }

      window.addEventListener('error', handleError)

      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            throw new Error('error')
          }
        }
        xhr.open('POST', `${hostname}/post`, true)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          status: 200,
          query: '',
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
      window.removeEventListener('error', handleError)
      vi.restoreAllMocks()
    })

    test('only report error', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()

        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            expect(xhr.status).toEqual(404)
            expect(xhr.responseText).toEqual(expect.any(String))
            resolve(0)
          }
        }

        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ a: 1 }))
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          base: `${hostname}/status/404`,
          url: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          requestData: JSON.stringify({ a: 1 }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis.destroy()
    })

    test('report url match ignore', async () => {
      const countSpy = vi.fn()
      vi.spyOn(console, 'log').mockImplementation(vi.fn())

      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ ignoreUrls: [`${hostname}`] })],
        transports: [new ConsoleTransport('http://www.abc.com/report.gif')]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}`, true)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/path`, true)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/path2`, true)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(3)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', 'invalid://localhost', true)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(4)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 0,
          method: 'GET',
          requestType: 'xhr',
          url: 'invalid://localhost',
          base: 'invalid://localhost',
          duration: expect.any(Number)
        }
      })

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', 'http://www.abc.com/report.gif', true)
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            countSpy()
            resolve(0)
          }
        }
        xhr.send()
      })

      expect(countSpy).toHaveBeenCalledTimes(5)
      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          status: 0,
          query: '',
          method: 'GET',
          requestType: 'xhr',
          url: 'invalid://localhost',
          base: 'invalid://localhost',
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
      vi.restoreAllMocks()
    })

    test('method or url is empty', async () => {
      const countSpy = vi.fn()

      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', '', true)
        xhr.send()
        xhr.onload = () => {
          countSpy()
          resolve(0)
        }
      })

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        try {
          const xhr = new XMLHttpRequest()
          xhr.open('', `${hostname}/post`, true)
          xhr.send()
          xhr.onload = () => resolve(0)
        } catch (e) {
          countSpy()
          resolve(0)
        }
      })

      expect(countSpy).toHaveBeenCalledTimes(2)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      aegis.destroy()
    })

    test('method is not valid', async () => {
      const countSpy = vi.fn()

      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        try {
          const xhr = new XMLHttpRequest()
          xhr.open([] as any, `${hostname}/post`, true)
          xhr.send()
          xhr.onload = () => resolve(0)
        } catch (e) {
          countSpy()
          resolve(0)
        }
      })

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      aegis.destroy()
    })

    test('__xhr_data__ is undefined', async () => {
      const countSpy = vi.fn()
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/post`, true)
        xhr['__xhr_data__'] = undefined
        xhr.send()
        xhr.onload = () => {
          countSpy()
          resolve(0)
        }
      })

      expect(countSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledTimes(0)

      aegis.destroy()
    })

    test('custom error report policy', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [
          api({
            reportPolicy: (responseText) => {
              try {
                const data = JSON.parse(responseText)
                return data.code !== 10001
              } catch (error) {
                return false
              }
            }
          })
        ]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ code: 10001, data: { a: 1 }, message: 'success' }))
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ code: 10002, data: null, message: 'fail' }))
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          status: 500,
          method: 'POST',
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          requestData: JSON.stringify({ code: 10002, data: null, message: 'fail' }),
          responseData: JSON.stringify({ code: 10002, data: null, message: 'fail' }),
          requestType: 'xhr'
        }
      })

      aegis.destroy()
    })

    test('custom error report policy, but request header is empty', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [
          api({
            reportPolicy: () => true
          })
        ]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)

      aegis.destroy()
    })

    test('xhr setRequestHeader value is not string', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('abc', 1 as any)
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy).toHaveBeenCalledTimes(0)

      aegis.destroy()
    })

    test('multiple aegis instance', async () => {
      vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      const aegis1 = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const aegis2 = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy1 = vi.spyOn(aegis1, 'report')
      const reportSpy2 = vi.spyOn(aegis2, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            expect(xhr.status).toEqual(200)
            expect(JSON.parse(xhr.responseText)).toEqual({ a: 1 })
            resolve(0)
          }
        }
        xhr.open('POST', `${hostname}/post`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send(JSON.stringify({ a: 1 }))
      })

      expect(reportSpy1).toHaveBeenCalledTimes(1)
      expect(reportSpy1).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: '',
          status: 200,
          method: 'POST',
          requestType: 'xhr',
          url: `${hostname}/post`,
          base: `${hostname}/post`,
          duration: expect.any(Number)
        }
      })
      expect(reportSpy2).toHaveBeenCalledTimes(0)

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', `${hostname}/status/404`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.send()
        xhr.onload = () => resolve(0)
      })

      expect(reportSpy1).toHaveBeenCalledTimes(2)
      expect(reportSpy1).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy1.mock.calls[1][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)
      expect(reportSpy2).toHaveBeenCalledTimes(1)
      expect(reportSpy2).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: '',
          status: 404,
          method: 'GET',
          requestType: 'xhr',
          url: `${hostname}/status/404`,
          base: `${hostname}/status/404`,
          duration: expect.any(Number),
          headers: JSON.stringify({ 'content-type': 'application/json' }),
          responseData: expect.any(String)
        }
      })
      expect(reportSpy2.mock.calls[0][0].payload.responseData?.includes('Cannot GET /status/404')).toEqual(true)

      aegis2.destroy()
      aegis1.destroy()

      vi.restoreAllMocks()
    })

    test('xhr not support', async () => {
      const originalXMLHttpRequest = window.XMLHttpRequest

      // @ts-ignore
      delete window.XMLHttpRequest

      const aegis = new Aegis({
        id: 'test',
        integrations: [api()]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      try {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post`, true)
        xhr.send()
      } catch (error) {}

      expect(reportSpy).toHaveBeenCalledTimes(0)

      window.XMLHttpRequest = originalXMLHttpRequest
      aegis.destroy()
    })

    test('xhr responseType is json', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [
          api({
            reportAll: true,
            reportPolicy: () => true
          })
        ]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.responseType = 'json'
        xhr.open('GET', `${hostname}/json?test=1&abc=def`, true)
        xhr.send()
        xhr.onload = () => {
          expect(xhr.response).toEqual({ a: 1 })
          resolve(0)
        }
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: 'test=1&abc=def',
          status: 500,
          method: 'GET',
          requestType: 'xhr',
          base: `${hostname}/json`,
          responseData: JSON.stringify({ a: 1 }),
          url: `${hostname}/json?test=1&abc=def`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('xhr responseType is arraybuffer', async () => {
      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true, reportPolicy: () => true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.responseType = 'arraybuffer'
        xhr.open('GET', `${hostname}/binary?test=1&abc=def`, true)
        xhr.send()
        xhr.onload = () => {
          expect(xhr.response).toBeInstanceOf(ArrayBuffer)
          resolve(0)
        }
      })

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: false,
          query: 'test=1&abc=def',
          status: 500,
          method: 'GET',
          requestType: 'xhr',
          base: `${hostname}/binary`,
          responseData: `[arraybuffer data]`,
          url: `${hostname}/binary?test=1&abc=def`,
          duration: expect.any(Number)
        }
      })

      aegis.destroy()
    })

    test('xhr methods already proxied by other functions', async () => {
      const originalOpen = XMLHttpRequest.prototype.open
      const originalSend = XMLHttpRequest.prototype.send

      const otherSdkOpenSpy = vi.fn()
      const otherSdkSendSpy = vi.fn()

      XMLHttpRequest.prototype.open = function (...args) {
        otherSdkOpenSpy(...args)
        return originalOpen.apply(this, args)
      }

      XMLHttpRequest.prototype.send = function (...args) {
        otherSdkSendSpy(...args)
        return originalSend.apply(this, args)
      }

      expect(XMLHttpRequest.prototype.open).not.toBe(originalOpen)
      expect(XMLHttpRequest.prototype.send).not.toBe(originalSend)
      expect(XMLHttpRequest.prototype.open.toString()).toEqual(expect.stringContaining('otherSdkOpenSpy'))
      expect(XMLHttpRequest.prototype.send.toString()).toEqual(expect.stringContaining('otherSdkSendSpy'))

      const aegis = new Aegis({
        id: 'test',
        integrations: [api({ reportAll: true })]
      })

      const reportSpy = vi.spyOn(aegis, 'report')

      await new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${hostname}/post?test=1&abc=def`, true)
        xhr.send(JSON.stringify({ a: 1 }))
        xhr.onload = () => resolve(0)
      })

      expect(otherSdkOpenSpy).toHaveBeenCalledTimes(1)
      expect(otherSdkOpenSpy).toHaveBeenCalledWith('POST', `${hostname}/post?test=1&abc=def`, true)

      expect(otherSdkSendSpy).toHaveBeenCalledTimes(1)
      expect(otherSdkSendSpy).toHaveBeenCalledWith(JSON.stringify({ a: 1 }))

      expect(reportSpy).toHaveBeenCalledTimes(1)
      expect(reportSpy).toHaveBeenCalledWith({
        type: 'api',
        payload: {
          ok: true,
          query: 'test=1&abc=def',
          status: 200,
          method: 'POST',
          requestType: 'xhr',
          base: `${hostname}/post`,
          url: `${hostname}/post?test=1&abc=def`,
          duration: expect.any(Number)
        }
      })

      expect(XMLHttpRequest.prototype.open.toString()).toEqual(expect.stringContaining('nextHandler'))
      expect(XMLHttpRequest.prototype.send.toString()).toEqual(expect.stringContaining('nextHandler'))

      aegis.destroy()

      expect(XMLHttpRequest.prototype.open.toString()).toEqual(expect.stringContaining('otherSdkOpenSpy'))
      expect(XMLHttpRequest.prototype.send.toString()).toEqual(expect.stringContaining('otherSdkSendSpy'))

      XMLHttpRequest.prototype.open = originalOpen
      XMLHttpRequest.prototype.send = originalSend

      expect(XMLHttpRequest.prototype.open).toBe(originalOpen)
      expect(XMLHttpRequest.prototype.send).toBe(originalSend)
    })
  })

  describe('utils', () => {
    test('split url', () => {
      expect(splitUrl('http://www.example.com')).toEqual(['http://www.example.com', ''])
      expect(splitUrl('http://www.example.com/path')).toEqual(['http://www.example.com/path', ''])
      expect(splitUrl('http://www.example.com/path?a=1&b=2')).toEqual(['http://www.example.com/path', 'a=1&b=2'])
      expect(splitUrl('http://www.example.com/path?a=1&b=2#/hello/world')).toEqual(['http://www.example.com/path', 'a=1&b=2#/hello/world'])
      expect(splitUrl(null as any)).toEqual(['', ''])
      expect(splitUrl(undefined as any)).toEqual(['', ''])
      expect(splitUrl('')).toEqual(['', ''])
      expect(splitUrl(' ')).toEqual(['', ''])
    })

    test('is empty object', () => {
      expect(isEmptyObject({})).toEqual(true)
      expect(isEmptyObject({ a: 1 })).toEqual(false)
      expect(isEmptyObject(Object.create({ inheritedProp: 'some value' }))).toBe(true)
    })

    test('url to string', () => {
      expect(urlToString('http://www.example.com')).toEqual('http://www.example.com')
      expect(urlToString('http://www.example.com/path')).toEqual('http://www.example.com/path')
      expect(urlToString(new URL('http://www.example.com'))).toEqual('http://www.example.com/')
      expect(urlToString(new URL('http://www.example.com/path'))).toEqual('http://www.example.com/path')
      expect(urlToString(null as any)).toEqual(undefined)
    })

    test('headers to string', () => {
      expect(headersToString({})).toEqual('{}')
      expect(headersToString({ a: '1' })).toEqual(JSON.stringify({ a: '1' }))
      expect(headersToString({ a: '1', b: '2' })).toEqual(JSON.stringify({ a: '1', b: '2' }))

      expect(headersToString(new Headers())).toEqual('{}')
      expect(headersToString(new Headers({ a: '1' }))).toEqual(JSON.stringify({ a: '1' }))
      expect(headersToString(new Headers({ a: '1', b: '2' }))).toEqual(JSON.stringify({ a: '1', b: '2' }))

      expect(headersToString([])).toEqual('{}')
      expect(headersToString([['a', '1']])).toEqual(JSON.stringify({ a: '1' }))
      expect(
        headersToString([
          ['a', '1'],
          ['b', '2']
        ])
      ).toEqual(JSON.stringify({ a: '1', b: '2' }))
    })

    test('body to string', () => {
      expect(bodyToString('')).toEqual(undefined)
      expect(bodyToString(null)).toEqual(undefined)
      expect(bodyToString(undefined)).toEqual(undefined)
      expect(bodyToString('a')).toEqual('a')
      expect(bodyToString(new Blob(['text'], { type: 'text/plain' }))).toEqual('[Blob type: text/plain, size: 4]')
      document.createElement('canvas').toBlob((blob) => expect(bodyToString(blob)).toEqual('[Blob type: image/png, size: 1192]'))
      expect(bodyToString(new FormData())).toEqual('[FormData]')
      expect(bodyToString(new URLSearchParams())).toEqual('')
      expect(bodyToString(new URLSearchParams('a=1&b=2'))).toEqual('a=1&b=2')
      expect(bodyToString(new ReadableStream())).toEqual('[ReadableStream]')
      expect(bodyToString(document)).toEqual(new XMLSerializer().serializeToString(document))
      expect(bodyToString(1 as any)).toEqual('[Unknown Body Type]')
      expect(bodyToString({} as any)).toEqual('[Unknown Body Type]')
      expect(bodyToString([] as any)).toEqual('[Unknown Body Type]')
    })

    test('parse fetch args', () => {
      // @ts-ignore
      expect(parseFetchArgs()).toEqual({ method: 'GET', url: '' })
      expect(parseFetchArgs('http://www.example.com')).toEqual({ method: 'GET', url: 'http://www.example.com' })
      expect(parseFetchArgs('http://www.example.com', { method: 'POST' })).toEqual({ method: 'POST', url: 'http://www.example.com' })
      expect(parseFetchArgs('http://www.example.com', { headers: new Headers({ a: '1' }) })).toEqual({ method: 'GET', url: 'http://www.example.com' })
      expect(parseFetchArgs(null as any, { headers: new Headers({ a: '1' }) })).toEqual({ method: 'GET', url: '' })
      expect(parseFetchArgs({} as any)).toEqual({ method: 'GET', url: '[object Object]' })
      expect(parseFetchArgs({ url: 'http://www.example.com', method: 'POST' } as any)).toEqual({ method: 'POST', url: 'http://www.example.com' })

      const a = {}
      a.toString = undefined as any
      expect(parseFetchArgs(a as any)).toEqual({ method: 'GET', url: '' })
    })
  })
})
