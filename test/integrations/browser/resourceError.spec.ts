import { now } from '../../../src/utils'
import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import resourceError from '../../../src/integrations/browser/resourceError'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('ResourceError', async () => {
  test('report image resource load error', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const errorResource = createImgElement('/empty.jpg')

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'img',
        xpath: 'body > img',
        timing: expect.any(Object),
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.jpg`
      }
    })

    document.body.removeChild(errorResource)
    aegis.destroy()
  })

  test('report js resource load error with `integrity` field', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    const errorResource = createScriptElement('/empty.js', 'sha384-oqVuAfXRKap7fdgcCY5uykM6s5gYjJjlv5YOyBG5z7hoXG5KRGvZ')

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'script',
        xpath: 'body > script',
        timing: expect.any(Object),
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.js`,
        integrity: 'sha384-oqVuAfXRKap7fdgcCY5uykM6s5gYjJjlv5YOyBG5z7hoXG5KRGvZ'
      }
    })
    document.body.removeChild(errorResource)
    aegis.destroy()
  })

  test('trigger js error event, but dont report resource error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    let errorEventTriggered = false

    const globalErrorListener = (event: ErrorEvent) => {
      errorEventTriggered = true
      event.preventDefault()
    }
    window.addEventListener('error', globalErrorListener)

    const script = createContentScriptElement('throw new Error("Simulated JS error")')

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()
    expect(errorEventTriggered).toBe(true)

    window.removeEventListener('error', globalErrorListener)
    document.body.removeChild(script)

    aegis.destroy()
    vi.restoreAllMocks()
  })

  test('performance API is not available, report resource error not contain `timing` field', async () => {
    vi.stubGlobal('performance', undefined)

    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const errorResource = createImgElement('/empty.jpg')

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'img',
        xpath: 'body > img',
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.jpg`
      }
    })

    vi.unstubAllGlobals()
    document.body.removeChild(errorResource)

    aegis.destroy()
  })

  test('call reportResourceError', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })
    const timestamp = now()
    const reportSpy = vi.spyOn(aegis, 'report')
    const img = createImgElement('/empty.jpg')

    aegis.reportResourceError(img, timestamp)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'img',
        xpath: 'body > img',
        timestamp: timestamp,
        timing: expect.any(Object),
        url: `${location.origin}/empty.jpg`
      }
    })

    aegis.reportResourceError(null, timestamp)
    expect(reportSpy).toHaveBeenCalledTimes(1)

    document.body.removeChild(img)
    aegis.destroy()
  })

  test('image resource load success', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    const successResource = createImgElement('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')

    await wait(500)

    expect(reportSpy).not.toHaveBeenCalled()

    document.body.removeChild(successResource)
    aegis.destroy()
  })

  test('report link resource load error', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    const link = createLinkElement('http://localhost/style.css')

    await wait(1000)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'link',
        xpath: 'body > link',
        timing: expect.any(Object),
        timestamp: expect.any(Number),
        url: 'http://localhost/style.css'
      }
    })

    document.body.removeChild(link)
    aegis.destroy()
  })

  test('report embed resource load error', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const obj = document.createElement('object')
    obj.data = '/empty.swf'
    document.body.appendChild(obj)

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'object',
        xpath: 'body > object',
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.swf`
      }
    })

    document.body.removeChild(obj)
    aegis.destroy()
  })

  test('target url is empty', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    const img = createImgElement('')

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()

    document.body.removeChild(img)
    aegis.destroy()
  })

  test('error event not contain target element', () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    let isCallError = false
    let reportSpy = vi.spyOn(aegis, 'report')

    const eventWithNonElementTarget = new ErrorEvent('error', {
      error: new Error('Some error'),
      message: 'Some error message'
    })

    const listener = () => (isCallError = true)

    window.addEventListener('error', listener)

    window.dispatchEvent(eventWithNonElementTarget)

    expect(isCallError).toEqual(true)

    expect(reportSpy).not.toHaveBeenCalled()

    window.removeEventListener('error', listener)

    aegis.destroy()
    vi.restoreAllMocks()
  })

  test('new Image() is url error, resource errors not reported', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const img = new Image()
    img.src = '/empty.jpg'

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(0)

    aegis.destroy()
  })

  test('multiple aegis instance', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis1 = new Aegis({
      id: 'test1',
      integrations: [resourceError()]
    })
    const aegis2 = new Aegis({
      id: 'test2',
      integrations: [resourceError()]
    })

    const reportSpy1 = vi.spyOn(aegis1, 'report')
    const reportSpy2 = vi.spyOn(aegis2, 'report')
    const errorResource = createImgElement('/empty.jpg')

    await wait(100)

    expect(reportSpy1).toHaveBeenCalledTimes(1)
    expect(reportSpy1).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'img',
        xpath: 'body > img',
        timing: expect.any(Object),
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.jpg`
      }
    })

    expect(reportSpy2).toHaveBeenCalledTimes(1)
    expect(reportSpy2).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'img',
        xpath: 'body > img',
        timing: expect.any(Object),
        timestamp: expect.any(Number),
        url: `${location.origin}/empty.jpg`
      }
    })

    document.body.removeChild(errorResource)
    aegis2.destroy()
    aegis1.destroy()

    vi.restoreAllMocks()
  })

  test('boundary case handing, event does not exist', async () => {
    const eventListener = vi.fn()
    const originalWindowEvent = window.event
    const originalAddEventListener = window.addEventListener
    const originalRemoveEventListener = window.removeEventListener

    window.event = new ErrorEvent('error', {
      message: 'Uncaught ReferenceError: _a is not defined',
      filename: 'http://localhost:3333/error.js',
      lineno: 1,
      colno: 1,
      error: new Error('Uncaught ReferenceError: _a is not defined')
    })
    window.addEventListener = eventListener
    window.removeEventListener = eventListener

    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    eventListener.mock.calls.find((call: any) => call[0] === 'error')[1]()

    aegis.destroy()
    window.event = originalWindowEvent
    window.addEventListener = originalAddEventListener
    window.removeEventListener = originalRemoveEventListener
  })

  test('report svg link resource load error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const aegis = new Aegis({
      id: 'test',
      integrations: [resourceError()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
    const svgLink = document.createElementNS('http://www.w3.org/2000/svg', 'a')
    svgLink.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/empty.svg')
    svg.appendChild(svgLink)
    document.body.appendChild(svg)

    const errorEvent = new ErrorEvent('error', {
      error: new Error('SVG load failed'),
      message: 'SVG load failed',
      filename: '/empty.svg'
    })
    Object.defineProperty(errorEvent, 'target', { value: svgLink })
    window.dispatchEvent(errorEvent)

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource_error',
      payload: {
        tagName: 'a',
        xpath: 'body > svg > a',
        timestamp: expect.any(Number),
        url: '/empty.svg'
      }
    })

    document.body.removeChild(svg)
    aegis.destroy()
    vi.restoreAllMocks()
  })
})

function createContentScriptElement(content: string): HTMLScriptElement {
  const element = document.createElement('script')
  element.textContent = content
  document.body.appendChild(element)

  return element
}

function createImgElement(src: string): HTMLImageElement {
  const element = document.createElement('img')
  element.src = src
  document.body.appendChild(element)

  return element
}

function createLinkElement(src: string): HTMLLinkElement {
  const element = document.createElement('link')
  element.href = src
  element.rel = 'stylesheet'
  document.body.appendChild(element)
  return element
}

function createScriptElement(src: string, integrity: string): HTMLScriptElement {
  const element = document.createElement('script')
  element.src = src
  element.integrity = integrity
  document.body.appendChild(element)

  return element
}
