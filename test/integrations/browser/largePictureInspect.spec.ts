import Aegis from '../../../src/core/Aegis'
import { events } from '../../../src/utils'
import { describe, expect, test, vi } from 'vitest'
import largePictureInspect from '../../../src/integrations/browser/largePictureInspect'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('LargePictureInspect', () => {
  test('should handle stylesheets without cssRules', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('http://localhost:3333/background.png');
      }
    `
    document.head.appendChild(styleElement)

    Object.defineProperty(styleElement.sheet, 'cssRules', {
      get: () => null
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/background.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body',
            fileSize: 2000000,
            memorySize: 0,
            size: '300x0',
            url: 'http://localhost:3333/background.png'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should handle stylesheets without cssRules & rules', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('http://localhost:3333/background.png');
      }
    `
    document.head.appendChild(styleElement)

    Object.defineProperty(styleElement.sheet, 'cssRules', {
      get: () => null
    })

    Object.defineProperty(styleElement.sheet, 'rules', {
      get: () => null
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/background.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(0)

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should report background images', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('http://localhost:3333/background.png');
      }
    `
    document.head.appendChild(styleElement)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/background.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body',
            fileSize: 2000000,
            memorySize: 0,
            size: '300x0',
            url: 'http://localhost:3333/background.png'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should report background images without document.baseURI', async () => {
    const originalBaseURI = document.baseURI

    Object.defineProperty(document, 'baseURI', {
      value: null,
      writable: true
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('http://localhost:3333/background.png');
      }
    `
    document.head.appendChild(styleElement)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/background.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body',
            fileSize: 2000000,
            memorySize: 0,
            size: '300x0',
            url: 'http://localhost:3333/background.png'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
    Object.defineProperty(document, 'baseURI', {
      value: originalBaseURI,
      writable: true
    })
  })

  test('should handle background-image without url', async () => {
    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: linear-gradient(to right, red, blue);
      }
    `
    document.head.appendChild(styleElement)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/background.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should convert relative URLs to absolute URLs', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('../images/bg.jpg');
      }
    `
    document.head.appendChild(styleElement)

    const url = `${window.location.origin}/${window.location.pathname.split('/')[1]}/images/bg.jpg`

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          name: url,
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            url: url,
            bg: true,
            path: 'body',
            fileSize: 2000000,
            memorySize: 0,
            size: '300x0'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should convert relative URLs to absolute URLs without URL API', async () => {
    const originalURL = window.URL
    window.URL = null as any

    const styleElement = document.createElement('style')
    styleElement.textContent = `
      body {
        background-image: url('../images/bg2.jpg');
      }
      .logo {
        background-image: url('/logo.png');
      }
      .hello {
        background-image: url('./hello.png');
      }
    `
    document.head.appendChild(styleElement)

    const logoUrl = `${window.location.origin}/logo.png`
    const url = `${window.location.origin}/${window.location.pathname.split('/')[1]}/images/bg2.jpg`
    const helloUrl = `${window.location.origin}/${window.location.pathname.split('/')[1]}/${window.location.pathname.split('/')[2]}/hello.png`

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          name: url,
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 2000000
        },
        {
          name: logoUrl,
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 2000000
        },
        {
          name: helloUrl,
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 2000000
        }
      ]
    })

    const element = document.createElement('div')
    element.className = 'logo'
    element.style.width = '100px'
    element.style.height = '100px'
    document.body.appendChild(element)

    const helloElement = document.createElement('div')
    helloElement.className = 'hello'
    helloElement.style.width = '100px'
    helloElement.style.height = '100px'
    document.body.appendChild(helloElement)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            url: url,
            bg: true,
            path: 'body',
            fileSize: 2000000,
            memorySize: 540000,
            size: '300x200'
          },
          {
            url: logoUrl,
            bg: true,
            path: 'body > div.logo',
            fileSize: 2000000,
            memorySize: 120000,
            size: '100x100'
          },
          {
            url: helloUrl,
            bg: true,
            path: 'body > div.hello',
            fileSize: 2000000,
            memorySize: 120000,
            size: '100x100'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    window.URL = originalURL
    document.head.removeChild(styleElement)
    document.body.removeChild(element)
    document.body.removeChild(helloElement)
  })

  test('should handle cross-origin stylesheets', async () => {
    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    events.on('error', errorListener)

    const styleElement = document.createElement('link')
    styleElement.rel = 'stylesheet'
    styleElement.href = 'http://localhost:3333/style.css'
    document.head.appendChild(styleElement)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpg'
        }
      ]
    })

    await wait(100)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()
    expect(spy).toHaveBeenLastCalledWith('[Style cross-domain error]: http://localhost:3333/style.css')

    events.off('error', errorListener)
    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('should report large picture inspect', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpg'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: false,
            size: '1x1',
            memorySize: 9,
            fileSize: 1000000,
            path: 'body > img',
            url: 'http://localhost:3333/resource.jpg'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should use PerformanceObserver to monitor resources', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(1000)

    const element = document.createElement('img')
    element.src = `http://localhost:3333/2xlogo.png?a=1`
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should ignore specified URLs', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect({ ignoreUrls: [/localhost:3333/] })]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpg'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should report SDK error when performance API is not supported', async () => {
    vi.stubGlobal('performance', undefined)

    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    events.on('error', errorListener)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()
    expect(spy).toHaveBeenLastCalledWith('performance or performance.getEntriesByType is not supported')

    expect(aegis.largePictureInspect()).toEqual([])

    events.off('error', errorListener)
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('should report SDK error when PerformanceObserver is not supported', async () => {
    vi.stubGlobal('PerformanceObserver', undefined)

    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    events.on('error', errorListener)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()
    expect(spy).toHaveBeenLastCalledWith('PerformanceObserver is not supported')

    events.off('error', errorListener)
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('should provide largePictureInspect method', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpeg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpeg'
        }
      ]
    })

    const result = aegis.largePictureInspect()

    expect(result).toEqual([
      {
        bg: false,
        el: element,
        memorySize: 9,
        fileSize: 1000000,
        url: 'http://localhost:3333/resource.jpeg'
      }
    ])

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should return null when findImageElement cannot find an element', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          name: 'http://localhost:3333/non-existent.jpg',
          initiatorType: 'img',
          decodedBodySize: 1000000
        }
      ]
    })

    const result = aegis.largePictureInspect()

    expect(result).toEqual([])

    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('should return empty array when querySelector is not supported', () => {
    const originalQuerySelectorAll = document.querySelectorAll

    document.querySelectorAll = null as any

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const result = aegis.largePictureInspect()
    expect(result).toEqual([])

    aegis.destroy()
    vi.unstubAllGlobals()
    document.querySelectorAll = originalQuerySelectorAll
  })

  test('should return null when no element is found', async () => {
    const styleElement = document.createElement('style')
    styleElement.textContent = `
      .nonexistent {
        background-image: url('http://localhost:3333/nonexistent.png');
      }
    `
    document.head.appendChild(styleElement)

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/nonexistent.png'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()

    aegis.destroy()
    vi.unstubAllGlobals()
    document.head.removeChild(styleElement)
  })

  test('window.getComputedStyle not available', async () => {
    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/resource.jpeg'
        }
      ]
    })

    const originalGetComputedStyle = window.getComputedStyle
    window.getComputedStyle = null as any

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpeg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    await wait(100)

    const result = aegis.largePictureInspect()
    expect(result).toEqual([])

    aegis.destroy()
    vi.unstubAllGlobals()
    window.getComputedStyle = originalGetComputedStyle
    document.body.removeChild(element)
  })

  test('should find inline background image rule', async () => {
    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/inline-bg.jpg'
        }
      ]
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('div')
    element.style.backgroundImage = 'url("http://localhost:3333/inline-bg.jpg")'
    element.style.width = '100px'
    element.style.height = '100px'
    document.body.appendChild(element)

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body > div',
            fileSize: 2000000,
            memorySize: 90000,
            size: '100x100',
            url: 'http://localhost:3333/inline-bg.jpg'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should find inline background rule', async () => {
    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 2000000,
          name: 'http://localhost:3333/inline-bg2.jpg'
        }
      ]
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('div')
    element.style.background = 'url("http://localhost:3333/inline-bg2.jpg") no-repeat center'
    element.style.width = '100px'
    element.style.height = '100px'
    document.body.appendChild(element)

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body > div',
            fileSize: 2000000,
            memorySize: 90000,
            size: '100x100',
            url: 'http://localhost:3333/inline-bg2.jpg'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should find inline background image with relative path', async () => {
    const expectedUrl = `${window.location.origin}/${window.location.pathname.split('/')[1]}/images/relative-bg.jpg`
    const expectedUrl2 = `${window.location.origin}/${window.location.pathname.split('/')[1]}/images/bg2.jpg`

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 1500000,
          name: expectedUrl
        },
        {
          duration: 4500,
          initiatorType: 'css',
          entryType: 'resource',
          decodedBodySize: 1500000,
          name: expectedUrl2
        }
      ]
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [largePictureInspect()]
    })

    const element = document.createElement('div')
    element.style.backgroundImage = 'url("../images/relative-bg.jpg")'
    element.style.width = '100px'
    element.style.height = '100px'
    document.body.appendChild(element)

    const element2 = document.createElement('div')
    element2.style.backgroundImage = 'url("../images/bg2.jpg")'
    element2.style.width = '200px'
    element2.style.height = '200px'
    document.body.appendChild(element2)

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: true,
            path: 'body > div',
            fileSize: 1500000,
            memorySize: 90000,
            size: '100x100',
            url: expectedUrl
          },
          {
            bg: true,
            path: 'body > div',
            fileSize: 1500000,
            memorySize: 360000,
            size: '200x200',
            url: expectedUrl2
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
    document.body.removeChild(element2)
  })

  test('should consider dpr parameter in calculations', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        largePictureInspect({
          dpr: 2
        })
      ]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpg'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: false,
            size: '1x1',
            memorySize: 6,
            fileSize: 1000000,
            path: 'body > img',
            url: 'http://localhost:3333/resource.jpg'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })

  test('should not auto report when reportMode is manual', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        largePictureInspect({
          reportMode: 'manual'
        })
      ]
    })

    const element = document.createElement('img')
    element.src = 'http://localhost:3333/resource.jpg'
    element.width = 1
    element.height = 1
    document.body.appendChild(element)

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          duration: 4500,
          initiatorType: 'img',
          entryType: 'resource',
          decodedBodySize: 1000000,
          name: 'http://localhost:3333/resource.jpg'
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()

    expect(aegis.largePictureInspect()).toEqual([
      {
        bg: false,
        el: element,
        memorySize: 9,
        fileSize: 1000000,
        url: 'http://localhost:3333/resource.jpg'
      }
    ])

    expect(reportSpy).not.toHaveBeenCalled()

    aegis.largePictureInspect(true)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'LPI',
      payload: {
        data: [
          {
            bg: false,
            size: '1x1',
            memorySize: 9,
            fileSize: 1000000,
            path: 'body > img',
            url: 'http://localhost:3333/resource.jpg'
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(element)
  })
})
