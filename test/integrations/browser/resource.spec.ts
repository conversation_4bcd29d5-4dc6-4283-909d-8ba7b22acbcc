import Aegis from '../../../src/core/Aegis'
import { events } from '../../../src/utils'
import { describe, expect, test, vi } from 'vitest'
import Resource from '../../../src/integrations/browser/resource'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('Resource', async () => {
  const hostname = `http://localhost:3333`

  test('not report ignored resource ignoreUrls', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource({ ignoreUrls: [/http:\/\/localhost:444/, hostname], batchInterval: 1000 })]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          name: 'http://localhost:3333/resource.js',
          responseStart: 0,
          requestStart: 0,
          initiatorType: 'script',
          duration: 4500,
          entryType: 'resource',
          redirectEnd: 0,
          redirectStart: 0,
          startTime: 0,
          tcpTime: 0,
          domainLookupEnd: 0,
          domainLookupStart: 0,
          redirectTime: 0,
          toJSON: () => {}
        },
        {
          name: 'http://localhost:444/resource.js',
          responseStart: 0,
          requestStart: 0,
          initiatorType: 'script',
          duration: 4500,
          entryType: 'resource',
          redirectEnd: 0,
          redirectStart: 0,
          startTime: 0,
          tcpTime: 0,
          domainLookupEnd: 0,
          domainLookupStart: 0,
          redirectTime: 0,
          toJSON: () => {}
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    const errorResource = document.createElement('img')
    errorResource.src = `${hostname}/igonreLogo.png`
    document.body.appendChild(errorResource)

    await wait(1100)

    expect(reportSpy.mock.calls).toHaveLength(0)

    aegis.destroy()
    vi.unstubAllGlobals()
    document.body.removeChild(errorResource)
  })

  test('not report ignored resource types', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => [
        {
          name: 'https://example.com/resource.js',
          responseStart: 0,
          requestStart: 0,
          initiatorType: 'fetch',
          duration: 4500,
          entryType: 'resource',
          redirectEnd: 0,
          redirectStart: 0,
          startTime: 0,
          tcpTime: 0,
          domainLookupEnd: 0,
          domainLookupStart: 0,
          redirectTime: 0,
          toJSON: () => {}
        }
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    expect(reportSpy).not.toHaveBeenCalled()
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('non-reporting of empty data', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })

    vi.stubGlobal('performance', {
      getEntriesByType: () => []
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    expect(reportSpy).not.toHaveBeenCalled()
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('delay reporting of resource data monitored by `PerformanceObserver` API', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource({ batchInterval: 1000 })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    dispatchEvent(new Event('load'))

    const element = document.createElement('script')
    element.src = '/a.js'
    document.body.appendChild(element)

    await wait(1100)
    expect(reportSpy).toHaveBeenCalledTimes(2)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: element.src,
            tagName: 'script',
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })
    aegis.destroy()
    document.body.removeChild(element)
  })

  test('report resource performance data', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    const performanceEntries = [
      {
        name: 'https://example.com/resource.js',
        responseStart: 0,
        requestStart: 0,
        connectStart: 0,
        connectEnd: 100,
        secureConnectionStart: 10,
        initiatorType: 'script',
        duration: 4500,
        entryType: 'resource',
        redirectEnd: 0,
        redirectStart: 0,
        startTime: 0,
        tcpTime: 0,
        fetchStart: 0,
        responseEnd: 0,
        domainLookupEnd: 0,
        domainLookupStart: 0,
        redirectTime: 0,
        transferSize: 100,
        toJSON: () => {}
      }
    ]

    vi.stubGlobal('performance', {
      getEntriesByType: () => performanceEntries
    })

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: performanceEntries[0].name,
            tagName: 'script',
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('report load event & `PerformanceObserver` API resource performance data', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource({ batchInterval: 2000 })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    const errorResource = document.createElement('img')
    errorResource.src = `${hostname}/2xlogo.png`
    document.body.appendChild(errorResource)

    await wait(2100)

    expect(reportSpy).toHaveBeenCalledTimes(2)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: errorResource.src,
            tagName: 'img',
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    aegis.destroy()
    document.body.removeChild(errorResource)
  })

  test('multiple `PerformanceObserver` data merges are reported at once', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource({ batchInterval: 2000 })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)

    const errorResource1 = document.createElement('img')
    errorResource1.src = `${hostname}/1xlogo_dark.png`

    await wait(100)

    const errorResource2 = document.createElement('img')
    errorResource2.src = `${hostname}/1xlogo.png`

    await wait(2100)

    expect(reportSpy).toHaveBeenCalledTimes(2)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: `${hostname}/1xlogo_dark.png`,
            tagName: errorResource1.tagName.toLowerCase(),
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          },
          {
            url: `${hostname}/1xlogo.png`,
            tagName: errorResource2.tagName.toLowerCase(),
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    aegis.destroy()
    document.body.appendChild(errorResource1)
    document.body.appendChild(errorResource2)
  })

  test('`PerformanceObserver` API is not available', async () => {
    vi.stubGlobal('PerformanceObserver', undefined)

    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    events.on('error', errorListener)

    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })
    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith('PerformanceObserver is not supported')

    events.off('error', errorListener)
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('performance is not available', async () => {
    vi.stubGlobal('performance', undefined)

    const spy = vi.fn()

    function errorListener(error: Error) {
      spy(error.message)
    }

    events.on('error', errorListener)

    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })
    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).not.toHaveBeenCalled()
    expect(spy).toHaveBeenLastCalledWith('performance or performance.getEntriesByType is not supported')

    events.off('error', errorListener)
    aegis.destroy()
    vi.unstubAllGlobals()
  })

  test('load the same resource multiple times', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource({ batchInterval: 1000 })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)

    const errorResource1 = document.createElement('img')
    errorResource1.src = `${hostname}/2xlogo_dark.png`
    document.body.appendChild(errorResource1)

    await wait(1100)

    expect(reportSpy).toHaveBeenCalledTimes(2)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: errorResource1.src,
            tagName: errorResource1.tagName.toLowerCase(),
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    const errorResource2 = document.createElement('img')
    errorResource1.src = `${hostname}/2xlogo_dark.png`
    document.body.appendChild(errorResource2)

    await wait(1100)

    expect(reportSpy).toHaveBeenCalledTimes(2)

    aegis.destroy()
    document.body.removeChild(errorResource1)
    document.body.removeChild(errorResource2)
  })

  test('multiple aegis instances', async () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const aegis1 = new Aegis({
      id: 'test1',
      integrations: [Resource({ batchInterval: 1000 })]
    })
    const aegis2 = new Aegis({
      id: 'test2',
      integrations: [Resource({ batchInterval: 500 })]
    })

    const reportSpy1 = vi.spyOn(aegis1, 'report')
    const reportSpy2 = vi.spyOn(aegis2, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy1).toHaveBeenCalledTimes(1)
    expect(reportSpy2).toHaveBeenCalledTimes(1)

    const errorResource = document.createElement('img')
    errorResource.src = `${hostname}/desktop_searchbox_sprites318_hr.webp`
    document.body.appendChild(errorResource)

    await wait(510)

    expect(reportSpy1).toHaveBeenCalledTimes(1)
    expect(reportSpy2).toHaveBeenCalledTimes(2)

    await wait(1100)

    expect(reportSpy1).toHaveBeenCalledTimes(2)
    expect(reportSpy2).toHaveBeenCalledTimes(2)
    expect(reportSpy1).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: errorResource.src,
            tagName: errorResource.tagName.toLowerCase(),
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })
    expect(reportSpy2).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: errorResource.src,
            tagName: errorResource.tagName.toLowerCase(),
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: expect.any(Number),
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    document.body.removeChild(errorResource)
    aegis2.destroy()
    aegis1.destroy()

    vi.restoreAllMocks()
  })

  test('performance timeOrigin is not available', async () => {
    vi.stubGlobal('performance', {
      timeOrigin: undefined,
      timing: {
        navigationStart: 10
      },
      getEntriesByType: () => [
        {
          name: 'https://example.com/resource.js',
          responseStart: 0,
          requestStart: 0,
          connectStart: 0,
          connectEnd: 100,
          secureConnectionStart: 10,
          initiatorType: 'script',
          duration: 4500,
          entryType: 'resource',
          redirectEnd: 0,
          redirectStart: 0,
          startTime: 0,
          tcpTime: 0,
          fetchStart: 0,
          responseEnd: 0,
          domainLookupEnd: 0,
          domainLookupStart: 0,
          redirectTime: 0,
          transferSize: 100,
          toJSON: () => {}
        }
      ]
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: 'https://example.com/resource.js',
            tagName: 'script',
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: 10,
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: expect.any(Number)
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })
  test('performance transferSize is not available', async () => {
    vi.stubGlobal('performance', {
      timeOrigin: undefined,
      timing: {
        navigationStart: 10
      },
      getEntriesByType: () => [
        {
          name: 'https://example.com/resource.js',
          responseStart: 0,
          requestStart: 0,
          connectStart: 0,
          connectEnd: 100,
          secureConnectionStart: 10,
          initiatorType: 'script',
          duration: 4500,
          entryType: 'resource',
          redirectEnd: 0,
          redirectStart: 0,
          startTime: 0,
          tcpTime: 0,
          fetchStart: 0,
          responseEnd: 0,
          domainLookupEnd: 0,
          domainLookupStart: 0,
          redirectTime: 0,
          toJSON: () => {}
        }
      ]
    })

    const aegis = new Aegis({
      id: 'test',
      integrations: [Resource()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window.dispatchEvent(new Event('load'))

    await wait(100)

    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenLastCalledWith({
      type: 'resource',
      payload: {
        data: [
          {
            url: 'https://example.com/resource.js',
            tagName: 'script',
            duration: expect.any(Number),
            startTime: expect.any(Number),
            redirectStart: expect.any(Number),
            redirectEnd: expect.any(Number),
            connectStart: expect.any(Number),
            connectEnd: expect.any(Number),
            domainLookupStart: expect.any(Number),
            domainLookupEnd: expect.any(Number),
            secureConnectionStart: 10,
            fetchStart: expect.any(Number),
            requestStart: expect.any(Number),
            responseStart: expect.any(Number),
            responseEnd: expect.any(Number),
            timeOrigin: expect.any(Number),
            encodedBodySize: expect.any(Number),
            transferSize: -1
          }
        ]
      }
    })

    aegis.destroy()
    vi.unstubAllGlobals()
  })
})
