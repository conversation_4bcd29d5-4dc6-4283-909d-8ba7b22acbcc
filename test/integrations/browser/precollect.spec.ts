import { now } from '../../../src/utils'
import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import precollect from '../../../src/integrations/browser/precollect'

describe('PreCollect', () => {
  test('resource error collect', () => {
    const spy = vi.fn()
    const timestamp = now()
    const aegis = new Aegis({
      id: 'test',
      integrations: [precollect()]
    })
    const img = document.createElement('img')

    img.src = 'http://test.com'

    aegis.provide('reportResourceError', spy)

    aegis.precollect('sr', img, timestamp)
    aegis.precollect('sr', img, null as any)

    expect(spy).toHaveBeenCalledWith(img, timestamp)
    expect(spy).toHaveBeenCalledWith(img, expect.any(Number))

    aegis.destroy()
  })

  test('js error collect', () => {
    const spy = vi.fn()
    const timestamp = now()
    const aegis = new Aegis({
      id: 'test',
      integrations: [precollect()]
    })

    aegis.provide('captureException', spy)

    aegis.precollect('js', 'test error' as any, timestamp)
    aegis.precollect('js', { message: 'test error' } as any, timestamp)
    aegis.precollect('js', { error: 'test error' } as any, null as any)

    expect(spy).toHaveBeenCalledWith('test error', timestamp)
    expect(spy).toHaveBeenCalledWith({ message: 'test error' }, timestamp)
    expect(spy).toHaveBeenCalledWith({ error: 'test error' }, expect.any(Number))

    aegis.destroy()
  })

  test('captureException not exist', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [precollect()]
    })

    aegis.precollect('js', 'test error' as any, now())

    aegis.destroy()
  })

  test('plugin install timing test', () => {
    const spy = vi.fn()
    const timestamp = now()
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        {
          name: 'test',
          setup: function (aegis: Aegis) {
            aegis.provide('captureException', spy)
          }
        },
        precollect()
      ]
    })

    aegis.precollect('js', 'test error' as any, timestamp)
    aegis.precollect('js', { message: 'test error' } as any, timestamp)
    aegis.precollect('js', { error: 'test error' } as any, null as any)

    expect(spy).toHaveBeenCalledWith('test error', timestamp)
    expect(spy).toHaveBeenCalledWith({ message: 'test error' }, timestamp)
    expect(spy).toHaveBeenCalledWith({ error: 'test error' }, expect.any(Number))

    aegis.destroy()
  })
})
