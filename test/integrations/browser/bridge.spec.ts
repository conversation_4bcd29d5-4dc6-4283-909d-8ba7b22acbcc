import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import bridge from '../../../src/integrations/browser/bridge'

describe('Bridge', () => {
  test('call request bridge', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api?test=1&abc=def',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api?test=1&abc=def', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        base: 'https://example.com/api',
        query: 'test=1&abc=def',
        url: 'https://example.com/api?test=1&abc=def',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('call other bridge', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_other',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_other', { url: 'https://example.com/api', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).not.toHaveBeenCalled()

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('validation of reported data in network error', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: null, msg: options.params.data ? 'fail' : '', code: -1 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api?hello=world&test=1',
        method: 'POST',
        data: { a: 1 }
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api?hello=world&test=1', method: 'POST', data: { a: 1 } })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: null, msg: 'fail', code: -1 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: 'hello=world&test=1',
        base: 'https://example.com/api',
        url: 'https://example.com/api?hello=world&test=1',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: JSON.stringify({ a: 1 })
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST',
        data: 'a=1'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST', data: 'a=1' })

    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        requestData: 'a=1',
        responseData: 'fail'
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST' })

    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        requestData: undefined,
        responseData: 'Unknown network error'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('validation of reported data in server error', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10002, data: null, message: options.params.data ? 'fail' : '', success: false }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST',
        data: { a: 1 }
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST', data: { a: 1 } })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10002, data: null, message: 'fail', success: false }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: JSON.stringify({ a: 1 })
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST',
        data: 'a=1'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST', data: 'a=1' })

    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        requestData: 'a=1',
        responseData: 'fail'
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST' })

    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'Unknown server error',
        requestData: undefined
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('validation of reported data in success', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        if (options.params.data) {
          options.callback && options.callback({ data: "{ code: 10001, data: { a: 2 }, message: 'ok', success: true, }", msg: 'ok', code: 0 })
        } else {
          options.callback && options.callback({ data: JSON.stringify({ code: 10001, data: { a: 2 }, message: 'ok', success: true }), msg: 'ok', code: 0 })
        }
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: JSON.stringify({ code: 10001, data: { a: 2 }, message: 'ok', success: true }), msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET',
        data: 'test'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: "{ code: 10001, data: { a: 2 }, message: 'ok', success: true, }", msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('aegis destroy should restore bridge', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledTimes(2)
    expect(callbackSpy).toHaveBeenCalledTimes(2)
    expect(reportSpy).toHaveBeenCalledTimes(1)

    delete window['AEJSBridge']
  })

  test('bridge not ready, wait for AEJSBridgeReady event', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    document.dispatchEvent(new Event('AEJSBridgeReady'))

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('call reportBridgeRequest api', () => {
    const dispatchSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    aegis.reportBridgeRequest({
      ok: true,
      query: '',
      base: 'https://example.com/api',
      url: 'https://example.com/api',
      status: 200,
      method: 'GET',
      duration: 100
    })

    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: 100,
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('request bridge callback error', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: null, msg: 'fail', code: -1 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    try {
      window['AEJSBridge'].dispatch({
        handlerName: 'action_network_request',
        params: {
          url: 'https://example.com/api',
          method: 'POST',
          data: { a: 1 }
        },
        callback: (result) => {
          callbackSpy(result)
          throw new Error('error')
        }
      })
    } catch (e) {}

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST', data: { a: 1 } })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: null, msg: 'fail', code: -1 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: JSON.stringify({ a: 1 })
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('ios bridge ready', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()
    const UA = navigator.userAgent

    Object.defineProperty(navigator, 'userAgent', {
      value: 'iPhone',
      configurable: true
    })

    window['WebViewJavascriptBridge'] = {}

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
    delete window['WebViewJavascriptBridge']

    Object.defineProperty(navigator, 'userAgent', {
      value: UA,
      configurable: true
    })
  })

  test('only report error', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: null, msg: options.params.data ? 'success' : 'fail', code: options.params.data ? 0 : -1 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST',
        data: { a: 1 }
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'POST', data: { a: 1 } })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: null, msg: 'success', code: 0 })
    expect(reportSpy).not.toHaveBeenCalled()

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'POST'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: null, msg: 'fail', code: -1 })

    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: undefined
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('multiple aegis instance', () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)

        if (options.params.url === 'https://example.com/api') {
          options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
        } else {
          options.callback && options.callback({ data: { code: 10002, data: null, message: options.params.data ? 'fail' : '', success: false }, msg: 'ok', code: 0 })
        }
      }
    }

    const aegis1 = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const aegis2 = new Aegis({
      id: 'test',
      integrations: [bridge()]
    })

    const reportSpy1 = vi.spyOn(aegis1, 'report')
    const reportSpy2 = vi.spyOn(aegis2, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledTimes(1)
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api', method: 'GET' })

    expect(callbackSpy).toHaveBeenCalledTimes(1)
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })

    expect(reportSpy1).toHaveBeenCalledTimes(1)
    expect(reportSpy1).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        query: '',
        base: 'https://example.com/api',
        url: 'https://example.com/api',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    expect(reportSpy2).not.toHaveBeenCalled()

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://abc.com/api',
        method: 'POST',
        data: { a: 1 }
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledTimes(2)
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://abc.com/api', method: 'POST', data: { a: 1 } })

    expect(callbackSpy).toHaveBeenCalledTimes(2)
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10002, data: null, message: 'fail', success: false }, msg: 'ok', code: 0 })

    expect(reportSpy1).toHaveBeenCalledTimes(2)
    expect(reportSpy1).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://abc.com/api',
        url: 'https://abc.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: JSON.stringify({ a: 1 })
      }
    })

    expect(reportSpy2).toHaveBeenCalledTimes(1)
    expect(reportSpy2).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: false,
        query: '',
        base: 'https://abc.com/api',
        url: 'https://abc.com/api',
        status: 500,
        method: 'POST',
        duration: expect.any(Number),
        requestType: 'bridge',
        responseData: 'fail',
        requestData: JSON.stringify({ a: 1 })
      }
    })

    aegis2.destroy()
    aegis1.destroy()

    delete window['AEJSBridge']

    vi.restoreAllMocks()
  })

  test('call request bridge, but url is empty', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [bridge({ reportAll: true })]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: ' ',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: ' ', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        base: '',
        query: '',
        url: ' ',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })

  test('report url match ignore', () => {
    const dispatchSpy = vi.fn()
    const callbackSpy = vi.fn()

    window['AEJSBridge'] = {
      dispatch: (options) => {
        dispatchSpy(options.handlerName, options.params)
        options.callback && options.callback({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
      }
    }

    const aegis = new Aegis({
      id: 'test',
      integrations: [
        bridge({
          reportAll: true,
          ignoreUrls: ['http://www.sayhello.com']
        })
      ]
    })

    const reportSpy = vi.spyOn(aegis, 'report')

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'https://example.com/api?test=1&abc=def',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalled()
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'https://example.com/api?test=1&abc=def', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalled()
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalled()
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        base: 'https://example.com/api',
        query: 'test=1&abc=def',
        url: 'https://example.com/api?test=1&abc=def',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    window['AEJSBridge'].dispatch({
      handlerName: 'action_network_request',
      params: {
        url: 'http://www.sayhello.com/api?test=1&abc=def',
        method: 'GET'
      },
      callback: (result) => callbackSpy(result)
    })

    expect(dispatchSpy).toHaveBeenCalledTimes(2)
    expect(dispatchSpy).toHaveBeenCalledWith('action_network_request', { url: 'http://www.sayhello.com/api?test=1&abc=def', method: 'GET' })
    expect(callbackSpy).toHaveBeenCalledTimes(2)
    expect(callbackSpy).toHaveBeenCalledWith({ data: { code: 10001, data: { a: 1 }, message: 'ok', success: true }, msg: 'ok', code: 0 })
    expect(reportSpy).toHaveBeenCalledTimes(1)
    expect(reportSpy).toHaveBeenCalledWith({
      type: 'api',
      payload: {
        ok: true,
        base: 'https://example.com/api',
        query: 'test=1&abc=def',
        url: 'https://example.com/api?test=1&abc=def',
        status: 200,
        method: 'GET',
        duration: expect.any(Number),
        requestType: 'bridge'
      }
    })

    aegis.destroy()

    delete window['AEJSBridge']
  })
})
