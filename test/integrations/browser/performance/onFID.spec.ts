import { afterEach, describe, test, vi } from 'vitest'
import { onFID } from '../../../../src/integrations/browser/performance/onFID'
import { FID_ENTRY, NAVIGATION_ENTRY, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('onFID test', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the FID event is triggered', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [FID_ENTRY as any]
      }
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['first-input'], [FID_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onFID(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['first-input'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('not report when PerformanceObserver is not supported', async ({ expect }) => {
    vi.spyOn(globalThis.PerformanceObserver, 'supportedEntryTypes', 'get').mockReturnValue([])
    const callback = vi.fn(() => {})
    onFID(callback)
    await wait(10)
    expect(callback).not.toBeCalled()
  })
})
