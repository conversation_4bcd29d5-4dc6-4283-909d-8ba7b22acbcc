import { afterEach, describe, test, vi } from 'vitest'
import { onLCP } from '../../../../src/integrations/browser/performance/onLCP'
import { LCP_ENTRY, NAVIGATION_ENTRY, NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START, RESOURCE_ENTRY, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('onLCP test', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the LCP event is triggered', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [RESOURCE_ENTRY as any]
      }
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [LCP_ENTRY])

    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should report when navigation entry without activationStart', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START as any]
      } else {
        return [RESOURCE_ENTRY as any]
      }
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [LCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should report which when keydown emit', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [RESOURCE_ENTRY as any]
      }
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [LCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    image.click()
    await wait(100)
    expect(callback).toBeCalledTimes(1)
  })

  test('large text content should be reported', async ({ expect }) => {
    const p = document.createElement('p')
    p.innerHTML = 'Hello World'.repeat(10)
    document.body.append(p)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [RESOURCE_ENTRY as any]
      }
    })
    const entry = { ...LCP_ENTRY, element: p }
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [entry])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('lcp should  be reported when the resource have no requestStart', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [{ ...RESOURCE_ENTRY, requestStart: undefined } as any]
      }
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [LCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('lcpEntry.url be undefined', async ({ expect }) => {
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [RESOURCE_ENTRY as any]
      }
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [{ ...LCP_ENTRY, url: '' }])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should not report when navigationEntry not found', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => {
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [LCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(100)
    expect(callback).not.toBeCalled()
  })

  test('should not report when PerformanceObserver not found', async ({ expect }) => {
    vi.stubGlobal('PerformanceObserver', undefined)
    const callback = vi.fn(() => {})
    onLCP(callback)
    await wait(100)
    expect(callback).not.toBeCalled()
  })

  test('should not report when no entry found', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => {
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['largest-contentful-paint'], [])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onLCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['largest-contentful-paint'])
    })
    await wait(100)
    expect(callback).not.toBeCalled()
  })
})
