import { afterEach, describe, expect, test, vi } from 'vitest'
import Aegis from '../../../../src/core/Aegis'
import { FCP_ENTRY, FID_ENTRY, LAYOUT_SHIFT_ENTRY, LCP_ENTRY, LONG_TASK_ENTRY, NAVIGATION_ENTRY, PerformancePlugin, RESOURCE_ENTRY, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('Performance Plugin', async () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('abnormal window size', async () => {
    vi.stubGlobal('innerWidth', 1)
    vi.stubGlobal('innerHeight', 1)

    const aegis = new Aegis({
      id: 'test',
      integrations: [PerformancePlugin()]
    })

    const reportSpy = vi.spyOn(aegis, 'report')
    expect(aegis).toBeDefined()
    await wait(500)

    expect(reportSpy).not.toHaveBeenCalled()

    aegis.destroy()
  })

  test('show not report anything', async () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [PerformancePlugin({ reportType: [] })]
    })

    expect(aegis).toBeDefined()

    const reportSpy = vi.spyOn(aegis, 'report')
    await wait(1000)
    expect(reportSpy).not.toHaveBeenCalled()
    aegis.destroy()
  })

  test('should report all', async () => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      return [NAVIGATION_ENTRY, FCP_ENTRY, LONG_TASK_ENTRY, RESOURCE_ENTRY, LCP_ENTRY, FID_ENTRY, LAYOUT_SHIFT_ENTRY] as any
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(
      ['paint', 'longtask', 'resource', 'largest-contentful-paint', 'first-input', 'layout-shift'],
      [FCP_ENTRY, LONG_TASK_ENTRY, RESOURCE_ENTRY, FID_ENTRY, LCP_ENTRY, LAYOUT_SHIFT_ENTRY],
      100
    )
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const aegis = new Aegis({
      id: 'test',
      integrations: [
        PerformancePlugin({
          spaLoad: {
            ignoreUrls: [/http:\/\/localhost/],
            loadTimeout: 3000,
            stableTimeout: 100
          }
        })
      ]
    })
    const reportSpy = vi.spyOn(aegis, 'report')
    document.dispatchEvent(new Event('DOMContentLoaded'))
    await wait(300)
    expect(reportSpy).toBeCalled()
  })
})
