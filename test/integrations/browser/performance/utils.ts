export { default as PerformancePlugin } from '../../../../src/integrations/browser/performance'
export const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export const getMockedPerformanceObserver = (type: string, entries: any[], delay?: number) => {
  function Po(callback: Function) {
    this.callback = callback
  }
  Po.supportedEntryTypes = [type]
  Po.prototype.observe = function () {
    setTimeout(() => {
      if (this.disconnected) return
      this.callback({ getEntries: () => entries })
    }, delay ?? 100)
  }
  Po.prototype.disconnect = function () {
    this.disconnected = true
  }
  Po.prototype.takeRecords = function () {
    return entries
  }

  return Po
}

export const getMultipleMockedPerformanceObserver = (types: string[], entries: any[], delay?: number) => {
  return class MockedPerformanceObserver {
    private callback: Function
    private disconnected: boolean
    private types: string[]

    static supportedEntryTypes = [...types]
    static instances: MockedPerformanceObserver[] = []

    constructor(callback: Function) {
      this.callback = callback
      MockedPerformanceObserver.instances.push(this)
    }

    observe(options: { entryTypes?: string[]; type?: string }) {
      if (this.disconnected) return
      this.types = options.entryTypes ?? (options.type ? [options.type] : [])
      if (delay !== void 0) {
        setTimeout(() => {
          this.emits(options.entryTypes ?? (options.type ? [options.type] : []))
        }, delay)
      }
    }

    disconnect() {
      this.disconnected = true
    }

    takeRecords() {
      return entries
    }

    emits(types: string[]) {
      this.types.forEach((type) => {
        if (types.includes(type)) {
          this.callback({ getEntries: () => entries.filter((entry) => types.includes(entry.name) || types.includes(entry.entryType)) })
        }
      })
    }
  }
}

export const NAVIGATION_ENTRY = {
  responseStart: 1,
  activationStart: 1
}
export const NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START = {
  responseStart: 1
}

export const FCP_ENTRY = {
  entryType: 'paint',
  name: 'first-contentful-paint',
  startTime: 2
}

export const LAYOUT_SHIFT_ENTRY = {
  name: '',
  entryType: 'layout-shift',
  startTime: 365153.5,
  duration: 0,
  value: 0.0022486251610349933,
  hadRecentInput: false,
  lastInputTime: 31479.20000000298,
  sources: [
    {
      node: document.createElement('div'),
      previousRect: {
        x: 0,
        y: 219,
        width: 136,
        height: 1193,
        top: 219,
        right: 136,
        bottom: 1412,
        left: 0
      },
      currentRect: {
        x: 0,
        y: 239,
        width: 136,
        height: 1193,
        top: 239,
        right: 136,
        bottom: 1432,
        left: 0
      }
    }
  ]
}

export const FID_ENTRY = {
  name: 'first-input',
  startTime: 10,
  processingStart: 10,
  target: document.createElement('img')
}

export const LCP_ENTRY = {
  entryType: 'paint',
  name: 'largest-contentful-paint',
  startTime: 5,
  duration: 1000,
  element: document.createElement('img'),
  url: 'https://source.unsplash.com/random'
}

export const RESOURCE_ENTRY = {
  entryType: 'resource',
  startTime: 6,
  requestStart: 6,
  responseEnd: 1000,
  url: 'https://source.unsplash.com/random',
  name: 'https://source.unsplash.com/random'
}

export const TTFB_ENTRY = {
  name: 'time-to-first-byte',
  startTime: 9,
  duration: 1000,
  element: document.createElement('img'),
  url: 'https://source.unsplash.com/random'
}

export const LONG_TASK_ENTRY = {
  entryType: 'longtask',
  startTime: 7,
  duration: 1000,
  attribution: []
}
