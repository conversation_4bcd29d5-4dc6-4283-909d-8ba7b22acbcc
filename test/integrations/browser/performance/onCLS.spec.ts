import { afterEach, describe, test, vi } from 'vitest'
import { onCLS } from '../../../../src/integrations/browser/performance/onCLS'
import { LAYOUT_SHIFT_ENTRY, NAVIGATION_ENTRY, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('onCLS test', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the CLS event is triggered', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [
          {
            name: 'https://app.soulapp.cn/chatroom-v3/#/mall?userIdEcpt=RDVnMlNSQXluVkJYRnBhV05La1orUT09&version=6.22.0&_webviewId=h44xXqsl_9Uy4zd2YVqwT',
            entryType: 'navigation',
            startTime: 0,
            duration: 417.6000000014901,
            initiatorType: 'navigation',
            deliveryType: '',
            nextHopProtocol: 'h2',
            renderBlockingStatus: 'non-blocking',
            workerStart: 0,
            redirectStart: 0,
            redirectEnd: 0,
            fetchStart: 1.1000000014901161,
            domainLookupStart: 4.799999997019768,
            domainLookupEnd: 4.799999997019768,
            connectStart: 4.799999997019768,
            secureConnectionStart: 5.100000001490116,
            connectEnd: 32.79999999701977,
            requestStart: 32.899999998509884,
            responseStart: 54.899999998509884,
            firstInterimResponseStart: 0,
            responseEnd: 55.100000001490116,
            transferSize: 1458,
            encodedBodySize: 1158,
            decodedBodySize: 2797,
            responseStatus: 200,
            serverTiming: [],
            unloadEventStart: 56.600000001490116,
            unloadEventEnd: 56.600000001490116,
            domInteractive: 150.20000000298023,
            domContentLoadedEventStart: 198.70000000298023,
            domContentLoadedEventEnd: 198.70000000298023,
            domComplete: 417.5,
            loadEventStart: 417.5,
            loadEventEnd: 417.6000000014901,
            type: 'reload',
            redirectCount: 0,
            activationStart: 0,
            criticalCHRestart: 0
          } as any
        ]
      }
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['layout-shift'], [{ ...LAYOUT_SHIFT_ENTRY, sources: [{ node: document.createTextNode('test') }] }])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onCLS(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['layout-shift'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('cls with hadRecentInput', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      }
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(
      ['layout-shift'],
      [
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: true },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false, value: 0.2 },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false, value: -2 }
      ]
    )
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onCLS(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['layout-shift'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('cls with not sources entry', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      }
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(
      ['layout-shift'],
      [
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: true, sources: [] },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false, value: 0.2, sources: [] },
        { ...LAYOUT_SHIFT_ENTRY, hadRecentInput: false, value: -2 }
      ]
    )
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onCLS(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['layout-shift'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })
  test('cls with not sources entry', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      }
      return []
    })

    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['layout-shift'], [])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onCLS(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['layout-shift'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })
})
