// 编写 firstInputPolyfill.ts 的测试用例
import { firstInputPolyfill, resetFirstInputPolyfill } from '../../../../../src/integrations/browser/performance/lib/firstInputPolyfill'
import { describe, expect, test, vi } from 'vitest'
import { wait } from '../utils'

describe('firstInputPolyfill', () => {
  test('firstInputPolyfill by keyboard event', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('keydown', { cancelable: true }))
    await wait(10)
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('firstInputPolyfill by keyboard event with cancelable false', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('keydown', { cancelable: false }))
    await wait(10)
    expect(callback).not.toHaveBeenCalled()
  })

  test('firstInputPolyfill by keyboard event with no timeStamp', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    const ev = new CustomEvent('keydown', { cancelable: true })
    vi.spyOn(ev, 'timeStamp', 'get').mockReturnValue(1e12 + 1)
    window.dispatchEvent(ev)
    await wait(10)
    expect(callback).not.toHaveBeenCalled()
  })

  test('firstInputPolyfill by mouse event', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('mousedown', { cancelable: true }))
    await wait(10)
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('firstInputPolyfill by touch event', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('touchstart', { cancelable: true }))
    await wait(10)
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('firstInputPolyfill by pointer event', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('pointerdown', { cancelable: true }))
    await wait(10)
    window.dispatchEvent(new CustomEvent('pointerdown', { cancelable: true }))
    await wait(200)
    window.dispatchEvent(new CustomEvent('pointerup', { cancelable: true }))
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('firstInputPolyfill by pointer event with pointer cancel', async () => {
    const callback = vi.fn(() => {})

    resetFirstInputPolyfill()
    firstInputPolyfill(callback)
    await wait(10)
    window.dispatchEvent(new CustomEvent('pointerdown', { cancelable: true }))
    await wait(200)
    window.dispatchEvent(new CustomEvent('pointercancel', { cancelable: true }))
    expect(callback).toHaveBeenCalledTimes(0)
  })
})
