import { describe, expect, test } from 'vitest'
import { IRecord, reducePrecision } from '../../../../../src/integrations/browser/performance/lib/reducePrecision'

describe('Performance Plugin reducePrecision', () => {
  test('should directly return if input is not an object', () => {
    const record = 'string'
    reducePrecision(record as unknown as IRecord)
    expect(record).toBe('string')
  })

  test('should break stack if input is a circular object', () => {
    const record: any = {
      a: 1.1111,
      b: {
        c: [1.1111]
      }
    }
    record.b = record
    expect(() => reducePrecision(record))
  })

  test('should reduce high resolution timestamp', () => {
    const record = {
      a: 1.123456,
      b: {
        c: 2.123456,
        d: {
          e: 3.123456,
          f: [4.123456, 5.123456, 3],
          g: [{ h: 6.123456 }, { h: 7 }]
        }
      }
    }
    reducePrecision(record)
    expect(record.a).toBe(1.12)
    expect(record.b.c).toBe(2.12)
    expect(record.b.d.e).toBe(3.12)
    expect(record.b.d.f[0]).toBe(4.12)
    expect(record.b.d.f[1]).toBe(5.12)
    expect(record.b.d.f[2]).toBe(3)
    expect(record.b.d.g[0].h).toBe(6.12)
    expect(record.b.d.g[1].h).toBe(7)
  })
})
