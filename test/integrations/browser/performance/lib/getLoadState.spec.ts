import { getLoadState } from '../../../../../src/integrations/browser/performance/lib/getLoadState'
import { afterEach, describe, test, vi } from 'vitest'

describe('getLoadState', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should return loading when the document is still loading', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('loading')
    const loadState = getLoadState(0)
    expect(loadState).toBe('loading')
  })

  test('should return complete when readyState is not loading and not found any navigation entry', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [])
    const loadState = getLoadState(0)
    expect(loadState).toBe('complete')
  })

  test('should return loading when timestamp is less than domInteractive', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 200, domComplete: 300 } as any])
    const loadState = getLoadState(50)
    expect(loadState).toBe('loading')
  })

  test('should return dom-interactive when navigationEntry.domContentLoadedEventStart is 0', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 0, domComplete: 300 } as any])
    const loadState = getLoadState(150)
    expect(loadState).toBe('dom-interactive')
  })

  test('should return dom-interactive when timestamp is less than domContentLoadedEventStart', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 200, domComplete: 300 } as any])
    const loadState = getLoadState(150)
    expect(loadState).toBe('dom-interactive')
  })

  test('should return dom-content-loaded when navigationEntry.domComplete is 0', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 200, domComplete: 0 } as any])
    const loadState = getLoadState(250)
    expect(loadState).toBe('dom-content-loaded')
  })

  test('should return dom-content-loaded when timestamp is less than domComplete', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 100, domComplete: 300 } as any])
    const loadState = getLoadState(250)
    expect(loadState).toBe('dom-content-loaded')
  })

  test('should return complete when timestamp is greater than domComplete', ({ expect }) => {
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ responseStart: 1, domInteractive: 100, domContentLoadedEventStart: 100, domComplete: 300 } as any])
    const loadState = getLoadState(350)
    expect(loadState).toBe('complete')
  })
})
