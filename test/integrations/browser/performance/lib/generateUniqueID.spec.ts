import { generateUniqueID } from '../../../../../src/integrations/browser/performance/lib/generateUniqueID'
import { describe, expect, test } from 'vitest'

describe('generateUniqueID', () => {
  test('should return a string', () => {
    const id = generateUniqueID()
    expect(typeof id).toBe('string')
  })

  test('should match the expected format', () => {
    const id = generateUniqueID()
    expect(id).toMatch(/^v4-\d{13}-\d{13}$/)
  })

  test('should generate unique IDs', () => {
    const id1 = generateUniqueID()
    const id2 = generateUniqueID()
    expect(id1).not.toBe(id2)
  })
})
