import { whenIdle } from '../../../../../src/integrations/browser/performance/lib/whenIdle'
import { afterEach, describe, test, vi } from 'vitest'

describe('whenIdle', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the document visibility state is hidden', ({ expect }) => {
    const callback = vi.fn(() => {})
    const requestIdleCallback = vi.spyOn(self, 'requestIdleCallback')
    const setTimeout = vi.spyOn(self, 'setTimeout')
    vi.spyOn(document, 'visibilityState', 'get').mockReturnValue('hidden')
    whenIdle(callback)
    expect(requestIdleCallback).toHaveBeenCalledTimes(0)
    expect(setTimeout).toHaveBeenCalledTimes(0)
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('requestIdleCallback should be called when the document visibility state is not hidden and ', ({ expect }) => {
    const callback = vi.fn(() => {})
    const requestIdleCallback = vi.spyOn(self, 'requestIdleCallback')
    const setTimeout = vi.spyOn(self, 'setTimeout')
    vi.spyOn(document, 'visibilityState', 'get').mockReturnValue('visible')
    whenIdle(callback)
    expect(requestIdleCallback).toHaveBeenCalledTimes(1)
    expect(setTimeout).toHaveBeenCalledTimes(0)
  })

  test('setTimeout should be called when the document visibility state is not hidden and requestIdleCallback is not available', ({ expect }) => {
    const callback = vi.fn(() => {})
    vi.stubGlobal('requestIdleCallback', undefined)
    const setTimeout = vi.spyOn(self, 'setTimeout')
    vi.spyOn(document, 'visibilityState', 'get').mockReturnValue('visible')
    whenIdle(callback)
    expect(setTimeout).toHaveBeenCalledTimes(1)
  })
})
