import { observe } from '../../../../../src/integrations/browser/performance/lib/observe'
import { afterEach, describe, test, vi } from 'vitest'
import { wait } from '../utils'

describe('observe', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should return undefined when the type is not supported', ({ expect }) => {
    vi.spyOn(globalThis.PerformanceObserver, 'supportedEntryTypes', 'get').mockReturnValue([])
    const po = observe('event', () => {})
    expect(po).toBeUndefined()
  })

  test('should return a PerformanceObserver instance when the type is supported', ({ expect }) => {
    vi.spyOn(globalThis.PerformanceObserver, 'supportedEntryTypes', 'get').mockReturnValue(['event'])
    const po = observe('event', () => {})
    expect(po).toBeInstanceOf(PerformanceObserver)
  })

  test('should call the callback with the entries', async ({ expect }) => {
    vi.stubGlobal(
      'PerformanceObserver',
      class PO {
        static supportedEntryTypes = ['event']
        private callbackList: Function[] = []
        constructor(callback: Function) {
          this.callbackList.push(callback)
        }

        observe(options?: PerformanceObserverInit | undefined): void {
          this.callbackList.forEach((callback) => {
            callback({ getEntries: () => [{ name: 'event' }] })
          })
        }
      }
    )

    const callback = vi.fn(() => {})
    observe('event', callback)
    await wait(100)
    expect(callback).toBeCalledTimes(1)
    expect(callback).toBeCalledWith([{ name: 'event' }])
  })
})
