import { afterEach, describe, test, vi } from 'vitest'
import { whenActivated } from '../../../../../src/integrations/browser/performance/lib/whenActivated'
import { wait } from '../utils'

describe('whenActivated', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the prerenderingchange event is triggered', async ({ expect }) => {
    const callback = vi.fn(() => {})
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(true)
    whenActivated(callback)
    await wait(10)
    expect(callback).toHaveBeenCalledTimes(0)
    dispatchEvent(new Event('prerenderingchange'))
    await wait(10)
    expect(callback).toHaveBeenCalledTimes(1)
  })

  test('should call the callback when the prerenderingchange event is not triggered', ({ expect }) => {
    const callback = vi.fn(() => {})
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(false)
    whenActivated(callback)
    expect(callback).toHaveBeenCalledTimes(1)
  })
})
