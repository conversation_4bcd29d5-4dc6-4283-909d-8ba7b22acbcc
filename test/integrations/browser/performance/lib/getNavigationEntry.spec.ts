import { getNavigationEntry } from '../../../../../src/integrations/browser/performance/lib/getNavigationEntry'
import { afterEach, describe, test, vi } from 'vitest'

describe('getNavigationEntry', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should return undefined if performance or getEntriesByType is not available', ({ expect }) => {
    vi.stubGlobal('performance', undefined)
    expect(getNavigationEntry()).toBeUndefined()
    vi.stubGlobal('performance', {})
    expect(getNavigationEntry()).toBeUndefined()
  })

  test('should return undefined if responseStart is not valid', ({ expect }) => {
    const entry = {
      responseStart: 0
    }
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [entry] as any)
    expect(getNavigationEntry()).toBeUndefined()
  })

  test('should return the navigation entry if responseStart is valid', ({ expect }) => {
    const entry = {
      responseStart: 1
    }
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [entry] as any)
    expect(getNavigationEntry()).toBe(entry)
  })
})
