import { getActivationStart } from '../../../../../src/integrations/browser/performance/lib/getActivationStart'
import { afterEach, describe, test, vi } from 'vitest'

describe('getActivationStart', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should return activationStart from navigation entry', ({ expect }) => {
    vi.spyOn(globalThis.performance, 'now').mockReturnValue(1000)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [{ activationStart: 100, responseStart: 1 } as any])
    const activationStart = getActivationStart()
    expect(activationStart).toBe(100)
  })

  test('should return 0 when no navigation entry found', ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation(() => [])
    const activationStart = getActivationStart()
    expect(activationStart).toBe(0)
  })
})
