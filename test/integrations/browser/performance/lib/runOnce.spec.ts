import { describe, test, vi } from 'vitest'
import { runOnce } from '../../../../../src/integrations/browser/performance/lib/runOnce'

describe('runOnce', () => {
  test('should call the callback only once', ({ expect }) => {
    const spy = vi.fn(() => {})
    const cb = vi.fn(
      runOnce(() => {
        spy()
      })
    )
    expect(spy).toHaveBeenCalledTimes(0)
    cb()
    cb()
    expect(spy).toHaveBeenCalledTimes(1)
  })
})
