import { afterEach, describe, test, vi } from 'vitest'
import { onTTI } from '../../../../src/integrations/browser/performance/onTTI'
import { FCP_ENTRY, LONG_TASK_ENTRY, RESOURCE_ENTRY, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('onTTI test', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the TTI event is triggered', async ({ expect }) => {
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint', 'longtask', 'resource'], [FCP_ENTRY, LONG_TASK_ENTRY, RESOURCE_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onTTI(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint', 'longtask', 'resource'])
    })
    document.dispatchEvent(new Event('DOMContentLoaded'))
    await wait(100)
    expect(callback).toBeCalledTimes(1)
    MockedPerformanceObserver.instances = []
  })

  test('should call the callback when the TTI event is triggered with multiple resources', async ({ expect }) => {
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(
      ['paint', 'longtask', 'resource'],
      [
        FCP_ENTRY,
        LONG_TASK_ENTRY,
        { ...RESOURCE_ENTRY, initiatorType: 'xmlhttprequest', startTime: 1 },
        { ...RESOURCE_ENTRY, initiatorType: 'fetch', responseEnd: 0 },
        { ...RESOURCE_ENTRY, initiatorType: 'fetch' }
      ]
    )
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onTTI(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint', 'longtask', 'resource'])
    })
    document.dispatchEvent(new Event('DOMContentLoaded'))
    await wait(100)
    expect(callback).toBeCalledTimes(1)
    MockedPerformanceObserver.instances = []
  })

  test('should call the callback when the TTI event is triggered with multiple resources and long tasks', async ({ expect }) => {
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(
      ['paint', 'longtask', 'resource'],
      [{ ...FCP_ENTRY }, { ...LONG_TASK_ENTRY, startTime: 20000 }, { ...RESOURCE_ENTRY, initiatorType: 'xmlhttprequest' }, { ...RESOURCE_ENTRY, initiatorType: 'fetch' }]
    )
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onTTI(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint', 'longtask', 'resource'])
    })
    document.dispatchEvent(new Event('DOMContentLoaded'))
    await wait(100)
    expect(callback).toBeCalledTimes(1)
    MockedPerformanceObserver.instances = []
  })

  test('not report when PerformanceObserver only support fcp', async ({ expect }) => {
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [FCP_ENTRY])
    MockedPerformanceObserver.supportedEntryTypes = ['paint']
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onTTI(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    document.dispatchEvent(new CustomEvent('DOMContentLoaded'))
    await wait(100)
    expect(callback).not.toBeCalled()
  })

  test('not report when PerformanceObserver without fcp', async ({ expect }) => {
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [{ ...FCP_ENTRY, name: 'first-input' }])
    MockedPerformanceObserver.supportedEntryTypes = ['paint']
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onTTI(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    document.dispatchEvent(new CustomEvent('DOMContentLoaded'))
    await wait(100)
    expect(callback).not.toBeCalled()
  })
})
