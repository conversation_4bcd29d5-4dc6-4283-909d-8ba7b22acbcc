import { afterEach, describe, test, vi } from 'vitest'
import { onFCP } from '../../../../src/integrations/browser/performance/onFCP'
import { FCP_ENTRY, NAVIGATION_ENTRY, NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START, getMultipleMockedPerformanceObserver, wait } from './utils'

describe('onFCP', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('should call the callback when the FCP event is triggered', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [FCP_ENTRY as any]
      }
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [FCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onFCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should call the callback when the FCP event is triggered and navigationEntry without activationStart', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START as any]
      } else {
        return [FCP_ENTRY as any]
      }
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [FCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onFCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('report without navigationEntry', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      return []
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [FCP_ENTRY])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onFCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('not report without any entry', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      return []
    })
    const MockedPerformanceObserver = getMultipleMockedPerformanceObserver(['paint'], [])
    vi.stubGlobal('PerformanceObserver', MockedPerformanceObserver)
    const callback = vi.fn(() => {})
    onFCP(callback)
    MockedPerformanceObserver.instances.forEach((instance) => {
      instance.emits(['paint'])
    })
    await wait(10)
    expect(callback).not.toBeCalled()
  })
})
