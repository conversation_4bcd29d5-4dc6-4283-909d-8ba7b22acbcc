import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import Aegis from '../../../../src/core/Aegis'
import PageView from '../../../../src/integrations/browser/pageview'
import { onSPA_LOAD } from '../../../../src/integrations/browser/performance/onSPA_LOAD'
import { SPALoadMetric } from '../../../../src/integrations/browser/performance/types/spa'
import { wait } from './utils'
import { events } from '../../../../src/utils'
describe('onSPA_LOAD', () => {
  let aegisMock: Aegis
  let callback: (metric: SPALoadMetric) => void
  let options: {
    loadTimeout: number
    stableTimeout: number
    mode?: 'history' | 'hash'
    ignoreUrls?: (string | RegExp)[]
  }

  let urls: string[] = []
  let appendedNodes: Node[] = []
  let shouldReturnResourceEntry = false

  beforeEach(() => {
    aegisMock = new Aegis({ id: 'test', integrations: [PageView({ mode: 'history' })] })

    let moCallback: (type: 'add' | 'remove', nodeList: Node[]) => void

    // 默认配置
    options = {
      loadTimeout: 1000,
      stableTimeout: 100,
      mode: 'history',
      ignoreUrls: [/__vitest_/]
    }

    vi.stubGlobal('__SPA_LOAD_INITIALIZED__', false)

    const originalCreateElement = document.createElement
    // mock createElement - 合并script和img的模拟逻辑
    vi.spyOn(document, 'createElement').mockImplementation((tag) => {
      const element = originalCreateElement.call(document, tag)

      if (tag === 'script' || tag === 'img') {
        // 通用属性和方法模拟
        const originalGetAttribute = element.getAttribute
        const originalSetAttribute = element.setAttribute
        const originalAddEventListener = element.addEventListener
        element.__src = ''

        // 设置src属性模拟
        vi.spyOn(element, 'src', 'set').mockImplementation((value) => {
          element.__src = value
          urls.push(value)
        })
        vi.spyOn(element, 'src', 'get').mockImplementation(() => {
          return element.__src
        })

        // getAttribute模拟
        vi.spyOn(element, 'getAttribute').mockImplementation((name) => {
          if (name === 'src') {
            return element.__src
          } else {
            return originalGetAttribute.call(element, name)
          }
        })

        // setAttribute模拟
        vi.spyOn(element, 'setAttribute').mockImplementation((name, value) => {
          if (name === 'src') {
            element.__src = value as string
            urls.push(value as string)
          } else {
            originalSetAttribute.call(element, name, value)
          }
        })

        // 针对特定元素类型的特殊处理
        if (tag === 'img') {
          // img特有属性模拟
          let complete = false
          vi.spyOn(element, 'complete', 'get').mockImplementation(() => complete)

          // img加载事件模拟
          vi.spyOn<HTMLElement, 'addEventListener'>(element, 'addEventListener').mockImplementation((name, listener) => {
            if (name === 'load' && element.__src.includes('example.com/image')) {
              setTimeout(
                () => {
                  complete = true
                  ;(listener as EventListener).call(element, { target: element })
                },
                element.__src.includes('timeout') ? 11000 : 100
              )
            } else {
              originalAddEventListener.call(element, name, listener)
            }
          })
        } else if (tag === 'script') {
          // script加载事件模拟
          vi.spyOn<HTMLElement, 'addEventListener'>(element, 'addEventListener').mockImplementation((name, listener) => {
            if (name === 'load' && element.__src.includes('example.com/script')) {
              setTimeout(() => {
                ;(listener as EventListener).call(element, { target: element })
              }, 100)
            } else {
              originalAddEventListener.call(element, name, listener)
            }
          })
        }
      }

      return element
    })

    vi.spyOn<Performance, 'getEntriesByType'>(performance, 'getEntriesByType').mockImplementation(() => {
      return shouldReturnResourceEntry
        ? (urls.map((url) => ({
            name: url,
            duration: 100,
            entryType: 'resource',
            startTime: 0
          })) as PerformanceResourceTiming[])
        : []
    })

    vi.spyOn(performance, 'timeOrigin', 'get').mockReturnValue(Date.now())
    vi.spyOn(performance.timing, 'navigationStart', 'get').mockReturnValue(Date.now())

    const originalAppendChild = document.appendChild
    vi.spyOn(document.body, 'appendChild').mockImplementation((node) => {
      appendedNodes.push(node)
      moCallback('add', [node])
      if (node instanceof Element) {
        const originalRemove = node.remove
        // @ts-ignore
        vi.spyOn(node, 'remove', 'get').mockImplementation(() => {
          moCallback('remove', [node])
          originalRemove.call(node)
        })
      }
      return originalAppendChild.call(document.body, node)
    })

    const originalRemoveChild = document.removeChild
    vi.spyOn(document.body, 'removeChild').mockImplementation((node) => {
      moCallback('remove', [node])
      return originalRemoveChild.call(document.body, node)
    })

    vi.stubGlobal(
      'MutationObserver',
      class MockedMutationRecord {
        constructor(callback: MutationCallback) {
          moCallback = (type: 'add' | 'remove', nodeList: Node[]) => {
            callback(
              [
                {
                  type: 'childList',
                  target: document,
                  addedNodes: type === 'add' ? (nodeList as unknown as NodeList) : ([] as unknown as NodeList),
                  removedNodes: type === 'remove' ? (nodeList as unknown as NodeList) : ([] as unknown as NodeList),
                  attributeName: '',
                  attributeNamespace: '',
                  nextSibling: null,
                  oldValue: '',
                  previousSibling: null
                }
              ],
              null as unknown as MutationObserver
            )
          }
        }

        observe(target: Node, options?: MutationObserverInit) {}

        disconnect() {}
      }
    )

    // 设置timer
    vi.useFakeTimers()
  })

  afterEach(() => {
    aegisMock.destroy()
    shouldReturnResourceEntry = false
    vi.restoreAllMocks()
    vi.useRealTimers()
    vi.unstubAllGlobals()
    vi.stubGlobal('__SPA_LOAD_INITIALIZED__', false)
    urls = []
  })

  it('should report with initialization', () => {
    callback = vi.fn()
    const cleanupFn = onSPA_LOAD(
      aegisMock,
      {
        loadTimeout: 1000,
        stableTimeout: 100,
        ignoreUrls: [/__vitest_/]
      },
      callback
    )

    vi.advanceTimersByTime(100)

    expect(cleanupFn).toBeInstanceOf(Function)
    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: 0,
      source: 'stable',
      records: [],
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    cleanupFn()
  })

  it('should report with multiple initializations', () => {
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    vi.advanceTimersByTime(100)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: 0,
      source: 'stable',
      records: [],
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    cleanupFn()

    const callback2 = vi.fn()
    const cleanupFn2 = onSPA_LOAD(aegisMock, options, callback2)

    vi.advanceTimersByTime(100)

    expect(callback2).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: 0,
      source: 'stable',
      records: [],
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })
    cleanupFn2()
  })

  it('should handle timeout', () => {
    vi.spyOn(performance, 'timeOrigin', 'get').mockReturnValue(undefined as unknown as number)
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    const img = document.createElement('img')
    img.setAttribute('src', `https://timeout.example.com/image.jpg`)
    img.width = 100
    img.height = 100
    document.body.appendChild(img)

    vi.advanceTimersByTime(10000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: 1000,
      source: 'timeout',
      records: expect.any(Array),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    cleanupFn()
  })

  it('should handle browser without performance.getEntriesByType', () => {
    // 模拟不支持performance.getEntriesByType的浏览器

    vi.stubGlobal('performance', {
      getEntriesByType: undefined,
      timeOrigin: Date.now(),
      timing: {
        navigationStart: Date.now()
      }
    })

    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: 0,
      source: 'stable',
      records: [],
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    cleanupFn()
  })

  it('should handle script that is already loaded', () => {
    const script = document.createElement('script')
    script.setAttribute('src', 'https://example.com/script.js')
    document.head.appendChild(script)
    shouldReturnResourceEntry = true

    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: expect.any(Number),
      source: 'stable',
      records: [],
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })
    expect(callback.mock.calls[0][0].value).toBeLessThan(10)

    cleanupFn()
  })

  it('should handle script that is not loaded', () => {
    vi.stubGlobal('__SPA_LOAD_ES_MODULE_SCRIPT__', true)

    const script = document.createElement('script')
    script.src = 'https://example.com/script.js'
    document.head.appendChild(script)

    const script2 = document.createElement('script')
    script2.src = 'https://2.example.com/script.js'
    document.head.appendChild(script2)

    const script3 = document.createElement('script')
    script3.src = 'https://3.example.com/script.js'
    script3.noModule = true
    document.head.appendChild(script3)

    setTimeout(() => {
      shouldReturnResourceEntry = true
    }, 10)

    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: expect.any(Number),
      source: 'stable',
      records: expect.any(Array),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].value).toBeGreaterThanOrEqual(100)
    expect(callback.mock.calls[0][0].value).toBeLessThanOrEqual(110)
    expect(callback.mock.calls[0][0].records[0].url).toBe('https://example.com/script.js')

    cleanupFn()
  })

  it('should handle dynamic added img tags', async () => {
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    // 动态创建并添加img标签
    const img = document.createElement('img')
    img.width = 100
    img.height = 100
    img.src = 'https://example.com/image.jpg'
    document.body.appendChild(img)

    const img2 = document.createElement('img')
    img2.width = 100
    img2.height = 100
    img2.src = 'https://2.example.com/image.jpg'
    document.body.appendChild(img2)

    const img3 = document.createElement('img')
    img3.width = 0
    img3.height = 0
    img3.src = 'https://3.example.com/image.jpg'
    document.body.appendChild(img3)

    setTimeout(() => {
      document.body.removeChild(img2)
    }, 50)
    vi.advanceTimersByTime(1000)

    // 检查回调是否被调用，以及参数是否正确
    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: expect.any(Number),
      source: 'stable',
      records: expect.any(Array),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].records.length).toBe(2)
    expect(callback.mock.calls[0][0].records[0].url).toBe('https://example.com/image.jpg')
    expect(callback.mock.calls[0][0].records[1].url).toBe('https://2.example.com/image.jpg')
    expect(callback.mock.calls[0][0].value).toBeGreaterThanOrEqual(100)

    // 清理函数
    cleanupFn()
  })

  it('should handle dynamic added script tags', () => {
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    const script = document.createElement('script')
    script.setAttribute('src', 'https://example.com/script.js')
    document.body.appendChild(script)

    const script2 = document.createElement('script')
    script2.setAttribute('src', 'https://2.example.com/script.js')
    document.body.appendChild(script2)
    setTimeout(() => {
      document.body.removeChild(script2)
    }, 50)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: expect.any(Number),
      source: 'stable',
      records: expect.any(Array),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].value).toBeGreaterThanOrEqual(100)
    expect(callback.mock.calls[0][0].records.length).toBe(2)
    expect(callback.mock.calls[0][0].records[0].url).toBe('https://example.com/script.js')
    expect(callback.mock.calls[0][0].records[1].url).toBe('https://2.example.com/script.js')

    cleanupFn()
  })

  it('should handle dynamic added background images, such as inline styles and background-image in css', () => {
    shouldReturnResourceEntry = true
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    const div = document.createElement('div')
    div.style.width = '100px'
    div.style.height = '100px'
    div.style.backgroundImage = 'url(https://example.com/image.jpg)'
    document.body.appendChild(div)
    const div2 = document.createElement('div')
    div2.style.width = '0px'
    div2.style.height = '0px'
    div2.style.backgroundImage = 'url(https://2.example.com/image.jpg)'
    document.body.appendChild(div2)
    const div3 = document.createElement('div')
    div3.style.width = '1px'
    div3.style.height = '1px'
    div3.style.backgroundImage = 'url(https://3.example.com/image.jpg)'
    document.body.appendChild(div3)
    const div4 = document.createElement('div')
    div4.style.width = '1px'
    div4.style.height = '1px'
    div4.style.backgroundImage = 'url(https://4.example.com/image.jpg)'
    document.body.appendChild(div4)
    const div5 = document.createElement('div')
    div5.style.width = '1px'
    div5.style.height = '1px'
    div5.style.backgroundImage = 'url(https://5.example.com/image.jpg)'
    document.body.appendChild(div5)
    setTimeout(() => {
      document.body.removeChild(div4)
    }, 50)
    setTimeout(() => {
      urls.push('https://example.com/image.jpg', 'https://3.example.com/image.jpg')
    }, 100)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      startTime: expect.any(Number),
      value: expect.any(Number),
      source: 'stable',
      records: expect.any(Array),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].records.length).toBe(3)
    expect(callback.mock.calls[0][0].records[0].url).toBe('https://example.com/image.jpg')
    expect(callback.mock.calls[0][0].records[1].url).toBe('https://3.example.com/image.jpg')
    expect(callback.mock.calls[0][0].records[2].url).toBe('https://4.example.com/image.jpg')
    expect(callback.mock.calls[0][0].value).toBeGreaterThanOrEqual(100)

    cleanupFn()
  })

  it('should handle fetch requests', async () => {
    vi.useRealTimers()
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, { loadTimeout: 1000, stableTimeout: 100, ignoreUrls: [/__vitest_/, /google/] }, callback)

    const xhr = new XMLHttpRequest()
    xhr.open('GET', window.location.origin)
    xhr.send()

    const xhr2 = new XMLHttpRequest()
    xhr2.open('GET', 'https://www.google.com')
    xhr2.send()

    try {
      await Promise.all([fetch(window.location.origin), fetch('https://www.baidu.com'), fetch('https://www.google.com')])
    } catch (err) {}

    await wait(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      source: 'stable',
      records: expect.any(Array),
      startTime: expect.any(Number),
      value: expect.any(Number),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].records.length).toBe(3)

    cleanupFn()
  })

  it('should handle bridge requests', () => {
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    events.emit('bridgeRequest', '1', 'GET', window.location.origin)
    setTimeout(() => {
      events.emit('bridgeResponse', '1', {
        data: 'hello'
      })
    }, 100)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      source: 'stable',
      records: expect.any(Array),
      startTime: expect.any(Number),
      value: expect.any(Number),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    expect(callback.mock.calls[0][0].records.length).toBe(1)
    expect(callback.mock.calls[0][0].records[0].url).toBe(`${window.location.origin}`)

    cleanupFn()
  })

  it('should handle route changes', () => {
    const callback = vi.fn()
    const cleanupFn = onSPA_LOAD(aegisMock, options, callback)

    const img = document.createElement('img')
    img.src = 'https://example.com/image.jpg'
    img.width = 100
    img.height = 100

    document.body.appendChild(img)

    setTimeout(() => {
      window.history.pushState({}, '', `${window.location.origin}/test`)
    }, 50)

    vi.advanceTimersByTime(1000)

    expect(callback).toHaveBeenCalledWith({
      name: 'SPA_LOAD',
      source: 'stable',
      records: expect.any(Array),
      startTime: expect.any(Number),
      value: expect.any(Number),
      isLowPowerMode: false,
      initTime: expect.any(Number),
      startLoadTime: expect.any(Number),
      onStartedTime: expect.any(Number)
    })

    cleanupFn()
  })
})
