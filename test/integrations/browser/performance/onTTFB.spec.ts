import { afterEach, describe, test, vi } from 'vitest'
import { onTTFB } from '../../../../src/integrations/browser/performance/onTTFB'
import { NAVIGATION_ENTRY, NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START, TTFB_ENTRY, wait } from './utils'

describe('onTTFB test', () => {
  afterEach(() => {
    vi.unstubAllGlobals()
    vi.restoreAllMocks()
  })

  test('report without navigation entry', async ({ expect }) => {
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return []
      } else {
        return [TTFB_ENTRY as any]
      }
    })

    const callback = vi.fn(() => {})
    onTTFB(callback)
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should call the callback when the TTFB event is triggered', async ({ expect }) => {
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(true)
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY as any]
      } else {
        return [TTFB_ENTRY as any]
      }
    })
    const callback = vi.fn(() => {})
    onTTFB(callback)
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(false)
    dispatchEvent(new Event('prerenderingchange'))

    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('should report when navigation entry without activationStart', async ({ expect }) => {
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(false)
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('loading')
    const image = document.createElement('img')
    document.body.append(image)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START as any]
      } else {
        return [TTFB_ENTRY as any]
      }
    })

    const callback = vi.fn(() => {})
    onTTFB(callback)
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    dispatchEvent(new Event('load'))
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('document was discarded', async ({ expect }) => {
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(false)
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(document, 'wasDiscarded', 'get').mockReturnValue(true)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START as any]
      } else {
        return [TTFB_ENTRY as any]
      }
    })

    const callback = vi.fn(() => {})
    onTTFB(callback)
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })

  test('document was discarded', async ({ expect }) => {
    vi.spyOn(document, 'prerendering', 'get').mockReturnValue(false)
    vi.spyOn(document, 'readyState', 'get').mockReturnValue('complete')
    vi.spyOn(document, 'wasDiscarded', 'get').mockReturnValue(false)
    vi.spyOn(globalThis.performance, 'getEntriesByType').mockImplementation((type) => {
      if (type === 'navigation') {
        return [{ ...NAVIGATION_ENTRY_WITHOUT_ACTIVATION_START, type: 'load' } as any]
      } else {
        return [TTFB_ENTRY as any]
      }
    })

    const callback = vi.fn(() => {})
    onTTFB(callback)
    await wait(10)
    expect(callback).toBeCalledTimes(1)
  })
})
