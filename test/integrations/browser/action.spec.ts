import Aegis from '../../../src/core/Aegis'
import { describe, expect, test, vi } from 'vitest'
import action from '../../../src/integrations/browser/action'

describe('Action', () => {
  test('click action report', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const div = document.createElement('div')
    div.innerText = 'hello'
    document.body.appendChild(div)

    div.dispatchEvent(new PointerEvent('pointerdown'))

    expect(spy).toHaveBeenLastCalledWith({
      type: 'action',
      immediate: true,
      payload: {
        type: 'pointerdown',
        target: {
          text: 'hello',
          path: 'body > div'
        }
      }
    })

    document.body.removeChild(div)

    aegis.destroy()
  })

  test('click action report with nested element', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const div = document.createElement('div')
    div.innerHTML = '<span></span>'
    document.body.appendChild(div)

    div.querySelector('span')?.dispatchEvent(new PointerEvent('pointerdown'))

    expect(spy).toHaveBeenLastCalledWith({
      type: 'action',
      immediate: true,
      payload: {
        type: 'pointerdown',
        target: {
          text: '',
          path: 'body > div > span'
        }
      }
    })

    document.body.removeChild(div)

    aegis.destroy()
  })

  test('PointerEvent not supported', () => {
    vi.stubGlobal('PointerEvent', undefined)

    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const div = document.createElement('div')
    div.innerText = 'hello'
    document.body.appendChild(div)

    div.click()

    expect(spy).toHaveBeenLastCalledWith({
      type: 'action',
      immediate: true,
      payload: {
        type: 'click',
        target: {
          text: 'hello',
          path: 'body > div'
        }
      }
    })

    document.body.removeChild(div)

    aegis.destroy()

    vi.unstubAllGlobals()
  })

  test('event.target is SVGElement', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
    svg.innerHTML = '<text>hello</text>'
    document.body.appendChild(svg)

    svg.querySelector('text')?.dispatchEvent(new PointerEvent('pointerdown'))

    expect(spy).toHaveBeenLastCalledWith({
      type: 'action',
      immediate: true,
      payload: {
        type: 'pointerdown',
        target: {
          text: 'hello',
          path: 'body > svg > text'
        }
      }
    })

    document.body.removeChild(svg)

    aegis.destroy()
  })

  test('event.target is not an Element', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    document.dispatchEvent(new PointerEvent('pointerdown'))

    expect(spy).not.toHaveBeenCalled()

    aegis.destroy()
  })

  test('element without innerText and textContent property', () => {
    const aegis = new Aegis({
      id: 'test',
      integrations: [action()]
    })

    const spy = vi.spyOn(aegis, 'report')

    const div = document.createElement('div')

    Object.defineProperty(div, 'innerText', {
      value: null,
      writable: true
    })
    Object.defineProperty(div, 'textContent', {
      value: null,
      writable: true
    })

    document.body.appendChild(div)

    div.dispatchEvent(new PointerEvent('pointerdown'))

    expect(spy).toHaveBeenLastCalledWith({
      type: 'action',
      immediate: true,
      payload: {
        type: 'pointerdown',
        target: {
          text: '',
          path: 'body > div'
        }
      }
    })

    document.body.removeChild(div)

    aegis.destroy()
  })
})
