import { events } from '../../src/utils'
import store from '../../src/utils/store'
import { isBrowser } from '../../src/utils/is'
import { describe, expect, test, vi } from 'vitest'
import browserStorage from '../../src/utils/browser/storage'

describe('storage', () => {
  test.runIf(isBrowser)('getItem', () => {
    localStorage.setItem('test', '1')
    expect(store.getItemSync<number>('test')).toEqual(1)

    localStorage.removeItem('test')
    expect(store.getItemSync<number>('test')).toEqual(undefined)
  })

  test.runIf(isBrowser)('setItem', () => {
    store.setItemSync('test', 1)
    expect(store.getItemSync<number>('test')).toEqual(1)

    store.setItemSync('test', 'test')
    expect(store.getItemSync<string>('test')).toEqual('test')

    store.setItemSync('test', { test: 1 })
    expect(store.getItemSync<{ test: number }>('test')).toEqual({ test: 1 })

    localStorage.clear()
  })

  test.runIf(isBrowser)('getItem SyntaxError Exception', () => {
    const handleError = (error: unknown) => expect((error as Error).message).toEqual(`storage SyntaxError: Unexpected token 'e', "test" is not valid JSON`)

    events.on('error', handleError)

    localStorage.setItem('test', 'test')

    expect(store.getItemSync<string>('test')).toEqual({})

    localStorage.clear()
    events.off('error', handleError)
  })

  test('getItem other Exception', () => {
    const handleError = (error: unknown) => expect((error as Error).message).toEqual(`storage an unexpected error occurred: Mocked Error`)

    events.on('error', handleError)

    vi.stubGlobal('localStorage', {
      getItem() {
        throw new Error('Mocked Error')
      }
    })

    store.getItemSync<number>('test')

    vi.unstubAllGlobals()
    events.off('error', handleError)
  })

  test('setItem QuotaExceededError Exception', () => {
    const errorMessage = 'Mocked DOMException error message'
    const domException = new DOMException(errorMessage, 'QuotaExceededError')
    const handleError = (error: unknown) => expect((error as Error).message).toEqual(`storage DOMException: ${errorMessage}`)

    events.on('error', handleError)

    vi.stubGlobal('localStorage', {
      setItem() {
        throw domException
      }
    })

    store.setItemSync('test', 1)

    vi.unstubAllGlobals()
    events.off('error', handleError)
  })

  test('setItem SecurityError Exception', () => {
    const errorMessage = 'Mocked DOMException error message'
    const domException = new DOMException(errorMessage, 'SecurityError')
    const handleError = (error: unknown) => expect((error as Error).message).toEqual(`storage DOMException: ${errorMessage}`)

    events.on('error', handleError)

    vi.stubGlobal('localStorage', {
      setItem() {
        throw domException
      }
    })

    store.setItemSync('test', 1)

    vi.unstubAllGlobals()
    events.off('error', handleError)
  })

  test.runIf(isBrowser)('browserStorage not available', () => {
    const originalBrowserStorageAvailable = browserStorage.available

    browserStorage.available = false

    document.cookie = 'a=1; path=/;'
    expect(store.getItemSync('a')).toEqual(1)
    document.cookie = 'a=1; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

    store.setItemSync('a', 2)
    expect(document.cookie).toEqual('a=2')
    expect(store.getItemSync('a')).toEqual(2)
    document.cookie = 'a=2; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

    browserStorage.available = originalBrowserStorageAvailable
  })
})
