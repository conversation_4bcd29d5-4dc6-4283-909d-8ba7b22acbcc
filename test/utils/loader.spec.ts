import scriptLoader from '../../src/loader'
import { describe, expect, test, vi } from 'vitest'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('loader', () => {
  test('load script url and trigger the callback correctly', async () => {
    const spy = vi.fn()
    const code = btoa('const _a = 1')

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].on = spy
      window['__loadertest__'].off = spy
      window['__loadertest__'].emit = spy
    })

    window['__loadertest__'].on('on', 'test')
    window['__loadertest__'].off('off', 'test')
    window['__loadertest__'].emit('emit', 'test')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenNthCalledWith(1, 'on', 'test')
    expect(spy).toHaveBeenNthCalledWith(2, 'off', 'test')
    expect(spy).toHaveBeenNthCalledWith(3, 'emit', 'test')

    delete window['__loadertest__']
  })

  test('simulate js error', async () => {
    vi.spyOn(console, 'error').mockImplementation(vi.fn())

    const spy = vi.fn()
    const code = btoa('const _b = 1')

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = spy
    })

    const event = new ErrorEvent('error', {
      message: 'Uncaught ReferenceError: _a is not defined',
      filename: 'http://localhost:3333/error.js',
      lineno: 1,
      colno: 1,
      error: new Error('Uncaught ReferenceError: _a is not defined')
    })
    window.dispatchEvent(event)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith('js', event, expect.any(Number))

    delete window['__loadertest__']
    vi.restoreAllMocks()
  })

  test('simulate promise error', async () => {
    const originNow = Date.now

    Date.now = undefined as any

    const spy = vi.fn()
    const code = btoa('const _bb = 1')

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = spy
    })

    const error = new Error('Uncaught ReferenceError: _a is not defined') as any
    error.code = 'VITEST_PENDING'

    const event = new PromiseRejectionEvent('unhandledrejection', {
      reason: error,
      promise: new Promise(() => {})
    })
    window.dispatchEvent(event)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith('js', event, expect.any(Number))
    Date.now = originNow

    delete window['__loadertest__']
  })

  test('simulate promise error, but error event is custom event', async () => {
    const spy = vi.fn()
    const code = btoa('const _bbb = 1')

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = spy
    })

    const error = new Error('Uncaught ReferenceError: _a is not defined')
    const event = new CustomEvent('unhandledrejection', {
      detail: {
        reason: error,
        promise: new Promise(() => {})
      }
    }) as any

    event.reason = {
      code: 'VITEST_PENDING'
    }

    window.dispatchEvent(event)

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenLastCalledWith('js', event, expect.any(Number))

    delete window['__loadertest__']
  })

  test('simulate resource error', async () => {
    const spy = vi.fn()
    const code = btoa(`
      const _sr = document.createElement('script');
      _sr.src = '/error.js';
      _sr.crossOrigin = 'anonymous';      
      document.body.appendChild(_sr);`)

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = spy
    })

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy.mock.calls[0][0]).toEqual('sr')
    expect(spy.mock.calls[0][1] instanceof HTMLScriptElement).toEqual(true)
    expect(spy.mock.calls[0][1].tagName.toLowerCase()).toEqual('script')
    expect(spy.mock.calls[0][2]).toEqual(expect.any(Number))

    delete window['__loadertest__']
  })

  test('simulate sri error', async () => {
    const spy = vi.fn()
    const code = btoa(`
      const _sri = document.createElement('script');
      _sri.src = '/error.js';
      _sri.crossOrigin = 'anonymous';
      _sri.integrity = 'sha384-1';      
      document.body.appendChild(_sri);`)

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = spy
    })

    await wait(100)
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy.mock.calls[0][0]).toEqual('sr')
    expect(spy.mock.calls[0][1] instanceof HTMLScriptElement).toEqual(true)
    expect(spy.mock.calls[0][1].tagName.toLowerCase()).toEqual('script')
    expect(spy.mock.calls[0][1].getAttribute('integrity')).toEqual('sha384-1')
    expect(spy.mock.calls[0][2]).toEqual(expect.any(Number))

    delete window['__loadertest__']
  })

  test('plugin not found', async () => {
    const spyConsole = vi.fn()
    vi.spyOn(console, 'error').mockImplementation(spyConsole)

    const spy = vi.fn()
    const code = btoa('const _c = 1')

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'] = {
        on: spy,
        off: spy,
        emit: spy
      }
    })

    window['__loadertest__'].on('on', 'test')
    window['__loadertest__'].off('off', 'test')
    window['__loadertest__'].emit('emit', 'test')
    window['__loadertest__'].sendPV('sendPV', 'test')

    await wait(100)

    expect(spy).toHaveBeenCalledTimes(3)
    expect(spy).toHaveBeenNthCalledWith(1, 'on', 'test')
    expect(spy).toHaveBeenNthCalledWith(2, 'off', 'test')
    expect(spy).toHaveBeenNthCalledWith(3, 'emit', 'test')
    expect(spyConsole).toHaveBeenCalledTimes(1)
    expect(spyConsole).toHaveBeenLastCalledWith('Plugin not found:', 'sendPV')

    delete window['__loadertest__']
    vi.restoreAllMocks()
  })

  test('boundary case handing, event does not exist', async () => {
    const loadertest = vi.fn()
    const eventListener = vi.fn()
    const code = btoa('const _ccc = 1')
    const originalWindowEvent = window.event
    const originalAddEventListener = window.addEventListener
    const originalRemoveEventListener = window.removeEventListener

    const event = new ErrorEvent('error', {
      message: 'Uncaught ReferenceError: _a is not defined',
      filename: 'http://localhost:3333/error.js',
      lineno: 1,
      colno: 1,
      error: new Error('Uncaught ReferenceError: _a is not defined')
    })

    window.event = event
    window.addEventListener = eventListener
    window.removeEventListener = eventListener

    scriptLoader(`data:application/javascript;base64,${code}`, '__loadertest__', () => {
      window['__loadertest__'].precollect = loadertest
    })

    const handleError = eventListener.mock.calls.find((call) => call[0] === 'error')[1]
    const handleRejectError = eventListener.mock.calls.find((call) => call[0] === 'unhandledrejection')[1]

    handleError()
    handleRejectError()

    window.event = originalWindowEvent
    window.addEventListener = originalAddEventListener
    window.removeEventListener = originalRemoveEventListener
  })
})
