import { isBrowser } from '../../src/utils/is'
import { describe, expect, test, vi } from 'vitest'
import { assign, includes, requestIdleCallback, cancelIdleCallback, findIndex, toArray, cssSupports } from '../../src/utils/polyfills'

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

describe('utils', () => {
  test('Object.assign test cases', () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    expect(assign({ a: 1 }, { b: 2 })).toEqual({ a: 1, b: 2 })
    expect(assign({ a: 1 }, { b: 2 }, { c: 3 })).toEqual({ a: 1, b: 2, c: 3 })
    expect(assign({ a: 1 }, null)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, undefined)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, 1)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, true)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, false)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, 'a')).toEqual({ 0: 'a', a: 1 })
    expect(assign({ a: 1 }, () => {})).toEqual({ a: 1 })
    expect(() => assign(null as any)).toThrowError('Cannot convert undefined or null to object')
    expect(assign({ a: () => 'a' }, { a: () => 'b' })['a']()).toEqual('b')

    process.env.NODE_ENV = originalEnv
  })

  test('Object.assign polyfill test cases', () => {
    vi.stubGlobal('polyfill', true)

    expect(assign({ a: 1 }, { b: 2 })).toEqual({ a: 1, b: 2 })
    expect(assign({ a: 1 }, { b: 2 }, { c: 3 })).toEqual({ a: 1, b: 2, c: 3 })
    expect(assign({ a: 1 }, null)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, undefined)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, 1)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, true)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, false)).toEqual({ a: 1 })
    expect(assign({ a: 1 }, 'a')).toEqual({ 0: 'a', a: 1 })
    expect(assign({ a: 1 }, () => {})).toEqual({ a: 1 })
    expect(() => assign(null as any)).toThrowError('Cannot convert undefined or null to object')
    expect(assign({ a: () => 'a' }, { a: () => 'b' })['a']()).toEqual('b')

    vi.unstubAllGlobals()
  })

  test('use Object.assign polyfill copy prototype chain', () => {
    vi.stubGlobal('polyfill', true)

    const parent = {
      inheritedProp: 'inherited'
    }
    const child = Object.create(parent)
    child.ownProp = 'own'

    const target = {}

    assign(target, child)

    expect(target).toHaveProperty('ownProp', 'own')
    expect(target).not.toHaveProperty('inheritedProp')

    vi.unstubAllGlobals()
  })

  test('includes test cases', () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    expect(includes([1, 2, 3], 1)).toEqual(true)
    expect(() => includes(<any>null, 4)).toThrowError('The array cannot be null or undefined')

    process.env.NODE_ENV = originalEnv
  })

  test('includes polyfill test cases', () => {
    vi.stubGlobal('polyfill', true)

    expect(includes([1, 2, 3], 1)).toEqual(true)
    expect(includes([1, 2, 3], 4)).toEqual(false)
    expect(() => includes(<any>null, 4)).toThrowError('The array cannot be null or undefined')
    expect(includes([], 1)).toEqual(false)
    expect(includes([1, 2, 3], 1, -1)).toEqual(false)

    vi.unstubAllGlobals()
  })

  test('use includes polyfill not found element', () => {
    vi.stubGlobal('polyfill', true)

    expect(includes([1, 2, 3], NaN)).toEqual(false)

    vi.unstubAllGlobals()
  })

  test('requestIdleCallback test cases', async () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    const spy = vi.fn()
    const idleCallbackId = <number>requestIdleCallback(spy)

    await wait(100)
    expect(spy.mock.calls.length).toEqual(1)
    expect(spy.mock.calls[0][0].didTimeout).toEqual(false)
    expect(typeof spy.mock.calls[0][0].timeRemaining).toEqual('function')
    expect(spy.mock.calls[0][0].timeRemaining()).toEqual(expect.any(Number))

    process.env.NODE_ENV = originalEnv
  })

  test('requestIdleCallback polyfill test cases', async () => {
    vi.stubGlobal('polyfill', true)

    const spy = vi.fn()
    const idleCallbackId = requestIdleCallback(spy)

    await wait(20)
    expect(spy.mock.calls.length).toEqual(1)
    expect(spy.mock.calls[0][0].didTimeout).toEqual(false)
    expect(typeof spy.mock.calls[0][0].timeRemaining).toEqual('function')
    expect(spy.mock.calls[0][0].timeRemaining()).toEqual(expect.any(Number))

    vi.unstubAllGlobals()
  })

  test('cancelIdleCallback test cases', async () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    const spy = vi.fn()
    const idleCallbackId = requestIdleCallback(spy)

    cancelIdleCallback(idleCallbackId)

    await wait(20)
    expect(spy.mock.calls.length).toEqual(0)

    process.env.NODE_ENV = originalEnv
  })

  test('cancelIdleCallback polyfill test cases', async () => {
    vi.stubGlobal('polyfill', true)

    const spy = vi.fn()
    const idleCallbackId = requestIdleCallback(spy)

    cancelIdleCallback(idleCallbackId)

    await wait(20)
    expect(spy.mock.calls.length).toEqual(0)

    vi.unstubAllGlobals()
  })

  test('findIndex test cases', () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 3)).toEqual(2)
    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 6)).toEqual(-1)
    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 3, 3)).toEqual(2)
    expect(
      findIndex(
        [
          { a: 1, b: 2 },
          { a: 2, b: 3 },
          { a: 3, b: 4 }
        ],
        (el) => el.a === 2
      )
    ).toEqual(1)
    expect(
      findIndex(
        [
          { a: 1, b: 2 },
          { a: 2, b: 3 },
          { a: 3, b: 4 }
        ],
        (el) => el.a === 6
      )
    ).toEqual(-1)

    try {
      expect(findIndex(null as any, (el) => el === 3)).toEqual(-1)
    } catch (error) {}

    process.env.NODE_ENV = originalEnv
  })

  test('findIndex polyfill test cases', () => {
    vi.stubGlobal('polyfill', true)

    expect(findIndex([], (el) => el === 3)).toEqual(-1)
    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 3)).toEqual(2)
    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 6)).toEqual(-1)
    expect(findIndex([1, 2, 3, 4, 5], (el) => el === 3, 3)).toEqual(2)
    expect(
      findIndex(
        [
          { a: 1, b: 2 },
          { a: 2, b: 3 },
          { a: 3, b: 4 }
        ],
        (el) => el.a === 2
      )
    ).toEqual(1)
    expect(
      findIndex(
        [
          { a: 1, b: 2 },
          { a: 2, b: 3 },
          { a: 3, b: 4 }
        ],
        (el) => el.a === 6
      )
    ).toEqual(-1)

    vi.unstubAllGlobals()
  })

  test('toArray test cases', () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    expect(toArray('foo')).toEqual(['f', 'o', 'o'])
    expect(toArray([1, 2, 3])).toEqual([1, 2, 3])
    expect(toArray({ length: 3 })).toEqual([undefined, undefined, undefined])

    if (isBrowser) {
      expect(toArray(document.querySelectorAll('*')).length > 2).toEqual(true)
    }

    process.env.NODE_ENV = originalEnv
  })

  test('toArray polyfill test cases', () => {
    vi.stubGlobal('polyfill', true)

    expect(toArray('foo')).toEqual(['f', 'o', 'o'])
    expect(toArray([1, 2, 3])).toEqual([1, 2, 3])
    expect(toArray({ length: 3 })).toEqual([undefined, undefined, undefined])

    if (isBrowser) {
      expect(toArray(document.querySelectorAll('*')).length > 2).toEqual(true)
    }

    vi.unstubAllGlobals()
  })

  test('cssSupports test cases', () => {
    const originalEnv = process.env.NODE_ENV

    vi.stubGlobal('process', {
      env: {
        NODE_ENV: 'production'
      }
    })

    expect(cssSupports('display', 'grid')).toEqual(isBrowser ? true : false)

    process.env.NODE_ENV = originalEnv
  })

  test.runIf(isBrowser)('cssSupports polyfill test cases', () => {
    vi.stubGlobal('polyfill', true)

    expect(cssSupports('display', 'grid')).toEqual(true)

    vi.unstubAllGlobals()
  })
})
