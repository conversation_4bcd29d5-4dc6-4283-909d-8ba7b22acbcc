import json from '../../src/utils/json'
import { describe, expect, test, vi } from 'vitest'
import { isObject, isFunction, isString, isArray, isWeb, isNodejs, isMiniapp, isReactNative, isBrowser } from '../../src/utils/is'
import { noop, mixin, aop, resolveURL, URIEncode, now, createCleanupTask, getRegexp, checkIsIgnored, events, AOP_STACK } from '../../src/utils'

describe('utils', () => {
  test('noop test cases', () => {
    expect(noop()).toEqual(undefined)
    expect(typeof noop).toEqual('function')
    expect(
      noop
        .toString()
        .replace(/\{[^{}]*\}/g, '{}')
        .replace(/function\s+\(\)/g, 'function()')
    ).toEqual('function() {}')
  })

  test('isObject test cases', () => {
    expect(isObject({})).toEqual(true)
    expect(isObject([])).toEqual(false)
    expect(isObject(null)).toEqual(false)
    expect(isObject(undefined)).toEqual(false)
    expect(isObject('')).toEqual(false)
    expect(isObject(1)).toEqual(false)
    expect(isObject(true)).toEqual(false)
    expect(isObject(false)).toEqual(false)
    expect(isObject(() => {})).toEqual(false)
  })

  test('mixins test cases', () => {
    const target = { a: 1 }
    const source = { b: 2 }
    mixin(target, source)
    expect(target).toEqual({ a: 1, b: 2 })

    const recevicer = {}
    const sender = Object.create({ inheritedProp: 'some value' })
    sender.ownProp = 'own value'

    mixin(recevicer, sender)

    expect(recevicer).toHaveProperty('ownProp', 'own value')
    expect(recevicer).not.toHaveProperty('inheritedProp')
  })

  test('aop test cases', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()

    const target = {
      value: 10,
      add(value: number) {
        this.value += value

        return this.value
      },
      sub(value: number) {
        this.value -= value

        return this.value
      },
      mul(value: number) {
        this.value *= value

        return this.value
      }
    }

    expect(target.add(1)).toEqual(11)
    expect(target.sub(1)).toEqual(10)

    aop(target, 'add', function (original, args) {
      spy1(args[0])
      return original.apply(this, args)
    })

    const restore = aop(target, 'sub', function (original, value) {
      const result = original.apply(this, value)

      spy2(result)

      return result
    })

    expect(Object.keys(AOP_STACK).length).toEqual(2)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(Object.keys(AOP_STACK)[1]).toEqual(expect.stringContaining('sub'))

    expect(target.add(2)).toEqual(12)
    expect(target.sub(2)).toEqual(10)
    expect(spy1).toBeCalledWith(2)
    expect(spy2).toBeCalledWith(10)
    expect(target.mul(2)).toEqual(20)
    expect(spy1.mock.calls.length).toEqual(1)
    expect(spy2.mock.calls.length).toEqual(1)
    expect(target.sub(5)).toEqual(15)
    expect(spy2.mock.calls.length).toEqual(2)

    restore()
    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))

    expect(target.sub(2)).toEqual(13)
    expect(spy2.mock.calls.length).toEqual(2)
    expect(target.sub(2)).toEqual(11)
    expect(spy2.mock.calls.length).toEqual(2)
    expect(target.add(20)).toEqual(31)
    expect(spy1.mock.calls.length).toEqual(2)
    expect(target.add(4)).toEqual(35)
    expect(spy1.mock.calls.length).toEqual(3)

    Object.keys(AOP_STACK).forEach((key) => delete AOP_STACK[key])
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop handles invalid targets and non-function properties', () => {
    const spy = vi.fn()

    const restoreNull = aop(null as any, 'method', (original, args) => {
      spy(args)
      return original.apply(this, args)
    })

    expect(restoreNull).toBe(noop)
    expect(spy).not.toHaveBeenCalled()

    const restoreUndefined = aop(undefined as any, 'method', (original, args) => {
      spy(args)
      return original.apply(this, args)
    })

    expect(restoreUndefined).toBe(noop)
    expect(spy).not.toHaveBeenCalled()

    const targetWithNonFunction = {
      notAFunction: 'This is a string, not a function'
    } as any

    const restoreNonFunction = aop(targetWithNonFunction, 'notAFunction', (original, args) => {
      spy(args)
      return original.apply(this, args)
    })

    expect(restoreNonFunction).toBe(noop)
    expect(spy).not.toHaveBeenCalled()

    const targetWithoutProperty = {
      existingMethod: () => {}
    } as any

    const restoreNonExistent = aop(targetWithoutProperty, 'nonExistentMethod' as any, (original, args) => {
      spy(args)
      return original.apply(this, args)
    })

    expect(restoreNonExistent).toBe(noop)
    expect(spy).not.toHaveBeenCalled()

    expect(targetWithNonFunction.notAFunction).toBe('This is a string, not a function')
    expect(targetWithoutProperty.nonExistentMethod).toBeUndefined()
  })

  test('aop restore edge case', () => {
    const spy = vi.fn()

    const target = {
      value: 10,
      add(value: number) {
        this.value += value
        return this.value
      }
    }

    const restore1 = aop(target, 'add', function (original, args) {
      spy(args[0])
      return original.apply(this, args)
    })

    const restore2 = aop(target, 'add', function (original, args) {
      spy(args[0])
      return original.apply(this, args)
    })

    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(AOP_STACK[Object.keys(AOP_STACK)[0]].length).toEqual(2)

    expect(target.add(5)).toBe(15)
    expect(spy).toHaveBeenCalledTimes(2)

    restore2()
    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(AOP_STACK[Object.keys(AOP_STACK)[0]].length).toEqual(1)

    expect(target.add(5)).toBe(20)
    expect(spy).toHaveBeenCalledTimes(3)

    restore1()
    expect(Object.keys(AOP_STACK).length).toEqual(0)

    expect(target.add(5)).toBe(25)
    expect(spy).toHaveBeenCalledTimes(3)

    restore1()
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop multiple proxies and restore scenarios', () => {
    const spy = vi.fn()

    const target = {
      value: 10,
      add(value: number) {
        this.value += value
        return this.value
      }
    }

    const restoreA1 = aop(target, 'add', function (original, args) {
      spy('A1')
      return original.apply(this, args)
    })

    const restoreA2 = aop(target, 'add', function (original, args) {
      spy('A2')
      return original.apply(this, args)
    })

    const restoreA3 = aop(target, 'add', function (original, args) {
      spy('A3')
      return original.apply(this, args)
    })

    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(AOP_STACK[Object.keys(AOP_STACK)[0]].length).toEqual(3)
    expect(target.add(5)).toBe(15)
    expect(spy.mock.calls).toEqual([['A3'], ['A2'], ['A1']])
    spy.mockClear()

    restoreA2()
    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(AOP_STACK[Object.keys(AOP_STACK)[0]].length).toEqual(2)

    expect(target.add(5)).toBe(20)
    expect(spy.mock.calls).toEqual([['A3'], ['A1']])
    spy.mockClear()

    restoreA3()
    expect(Object.keys(AOP_STACK).length).toEqual(1)
    expect(Object.keys(AOP_STACK)[0]).toEqual(expect.stringContaining('add'))
    expect(AOP_STACK[Object.keys(AOP_STACK)[0]].length).toEqual(1)

    expect(target.add(5)).toBe(25)
    expect(spy.mock.calls).toEqual([['A1']])
    spy.mockClear()
    restoreA1()
    expect(Object.keys(AOP_STACK).length).toEqual(0)

    expect(target.add(5)).toBe(30)
    expect(spy).not.toHaveBeenCalled()

    const restoreA2Again = aop(target, 'add', function (original, args) {
      spy('A2')
      return original.apply(this, args)
    })

    expect(target.add(5)).toBe(35)
    expect(spy.mock.calls).toEqual([['A2']])
    spy.mockClear()

    restoreA2Again()
    expect(target.add(5)).toBe(40)
    expect(spy).not.toHaveBeenCalled()

    restoreA1()
    restoreA2()
    restoreA3()
    expect(target.add(5)).toBe(45)
    expect(spy).not.toHaveBeenCalled()

    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop with A1 and A2 proxies and different restore orders', () => {
    const spy = vi.fn()

    const target = {
      value: 10,
      add(value: number) {
        this.value += value
        return this.value
      }
    }

    let restoreA1 = aop(target, 'add', function (original, args) {
      spy('A1')
      return original.apply(this, args)
    })

    let restoreA2 = aop(target, 'add', function (original, args) {
      spy('A2')
      return original.apply(this, args)
    })

    expect(target.add(5)).toBe(15)
    expect(spy.mock.calls).toEqual([['A2'], ['A1']])
    spy.mockClear()

    restoreA2()
    expect(target.add(5)).toBe(20)
    expect(spy.mock.calls).toEqual([['A1']])
    spy.mockClear()

    restoreA1()
    expect(target.add(5)).toBe(25)
    expect(spy).not.toHaveBeenCalled()
    spy.mockClear()

    restoreA1 = aop(target, 'add', function (original, args) {
      spy('A1')
      return original.apply(this, args)
    })

    restoreA2 = aop(target, 'add', function (original, args) {
      spy('A2')
      return original.apply(this, args)
    })

    expect(target.add(5)).toBe(30)
    expect(spy.mock.calls).toEqual([['A2'], ['A1']])
    spy.mockClear()

    restoreA1()
    expect(target.add(5)).toBe(35)
    expect(spy.mock.calls).toEqual([['A2']])
    spy.mockClear()

    restoreA2()
    expect(target.add(5)).toBe(40)
    expect(spy).not.toHaveBeenCalled()
    spy.mockClear()

    expect(target.add(5)).toBe(45)
    expect(spy).not.toHaveBeenCalled()
    spy.mockClear()

    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop proxy custom object with native detection', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()

    function errorListener(error: Error) {
      spy1(error.message)
    }

    events.on('error', errorListener)

    const target = {
      value: 10,
      add(value: number) {
        this.value += value

        return this.value
      }
    }

    const restore = aop(
      target,
      'add',
      function (original, args) {
        spy2(args[0])
        return original.apply(this, args)
      },
      true
    )

    expect(target.add(2)).toEqual(12)
    expect(spy2).toBeCalledWith(2)
    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy1).toHaveBeenLastCalledWith('AOP Warning: Object.add is not a [native code]')

    events.off('error', errorListener)

    restore()
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test.runIf(isBrowser)('aop proxy native object with native detection', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()

    function errorListener(error: Error) {
      spy1(error.message)
    }

    events.on('error', errorListener)

    expect(history.pushState.toString()).toContain('native code')

    const restore = aop(
      history,
      'pushState',
      function (original, args) {
        spy2(args)
        return original.apply(this, args)
      },
      true
    )

    history.pushState({}, '', '/hello')

    expect(history.pushState.toString()).not.toContain('native code')

    expect(spy1).toHaveBeenCalledTimes(0)
    expect(spy2).toHaveBeenLastCalledWith([{}, '', '/hello'])

    history.back()

    restore()
    events.off('error', errorListener)
    expect(history.pushState.toString()).toContain('native code')
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test.runIf(isBrowser)('aop proxy native object with native detection, but native object has been proxied', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()

    function errorListener(error: Error) {
      spy1(error.message)
    }

    events.on('error', errorListener)

    expect(history.pushState.toString()).toContain('native code')

    vi.stubGlobal('history', {
      pushState: (...args: any[]) => {
        spy2(args)
      }
    })

    const restore = aop(
      history,
      'pushState',
      function (original, args) {
        spy2(args)
        return original.apply(this, args)
      },
      true
    )

    history.pushState({}, '', '/hello')

    expect(history.pushState.toString()).not.toContain('native code')

    expect(spy1).toHaveBeenCalledTimes(1)
    expect(spy1).toHaveBeenCalledWith('AOP Warning: Object.pushState is not a [native code]')
    expect(spy2).toHaveBeenCalledTimes(2)
    expect(spy2).toHaveBeenLastCalledWith([{}, '', '/hello'])
    expect(history.pushState.toString()).toEqual(expect.stringContaining('nextHandler'))

    restore()

    expect(history.pushState.toString()).toEqual(expect.stringContaining('spy2'))

    vi.unstubAllGlobals()
    events.off('error', errorListener)

    expect(history.pushState.toString()).toContain('native code')
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('multiple aop test cases', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()

    const target = {
      value: 10,
      add(value: number) {
        this.value += value

        return this.value
      },
      sub(value: number) {
        this.value -= value

        return this.value
      },
      mul(value: number) {
        this.value *= value

        return this.value
      }
    }

    expect(target.add(1)).toEqual(11)
    expect(target.sub(1)).toEqual(10)

    aop(target, 'add', function (original, args) {
      spy1(args[0])
      return original.apply(this, args)
    })

    expect(target.add(2)).toEqual(12)
    expect(target.sub(2)).toEqual(10)
    expect(spy1).toBeCalledWith(2)
    expect(spy1).toHaveBeenCalledTimes(1)

    aop(target, 'add', function (original, args) {
      spy1(args[0])
      spy2(args[0])
      return original.apply(this, args)
    })

    expect(target.add(3)).toEqual(13)
    expect(target.sub(1)).toEqual(12)
    expect(spy1).toBeCalledWith(3)
    expect(spy1).toHaveBeenCalledTimes(3)
    expect(spy2).toBeCalledWith(3)
    expect(spy2).toHaveBeenCalledTimes(1)

    Object.keys(AOP_STACK).forEach((key) => delete AOP_STACK[key])
    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop with manual proxy interaction test cases', () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()
    const spy3 = vi.fn()
    const spyCallOrder: string[] = []

    const target = {
      value: 10,
      add(value: number) {
        this.value += value

        return this.value
      }
    }

    const restore1 = aop(target, 'add', function (original, args) {
      spy1(args[0])
      spyCallOrder.push('spy1')
      return original.apply(this, args)
    })

    expect(target.add.toString()).toEqual(expect.stringContaining('nextHandler'))

    const originalAdd = target.add
    target.add = function customAdd(value: number) {
      spy2(value)
      spyCallOrder.push('spy2')
      return originalAdd.apply(this, [value])
    }

    expect(target.add.toString()).toEqual(expect.stringContaining('spy2(value)'))

    const restore2 = aop(target, 'add', function (original, args) {
      spy3(args[0])
      spyCallOrder.push('spy3')
      return original.apply(this, args)
    })

    expect(target.add.toString()).toEqual(expect.stringContaining('nextHandler'))

    expect(target.add(5)).toEqual(15)
    expect(spy3).toHaveBeenCalledWith(5)
    expect(spy2).toHaveBeenCalledWith(5)
    expect(spy1).toHaveBeenCalledWith(5)
    expect(spy3.mock.calls.length).toBe(1)
    expect(spy2.mock.calls.length).toBe(1)
    expect(spy1.mock.calls.length).toBe(1)
    expect(spyCallOrder).toEqual(['spy3', 'spy2', 'spy1'])

    restore1()
    spyCallOrder.length = 0
    expect(target.add.toString()).toEqual(expect.stringContaining('nextHandler'))

    expect(target.add(5)).toEqual(20)
    expect(spy3.mock.calls.length).toBe(2)
    expect(spy2.mock.calls.length).toBe(2)
    expect(spy1.mock.calls.length).toBe(1)
    expect(spyCallOrder).toEqual(['spy3', 'spy2'])

    restore2()
    spyCallOrder.length = 0
    expect(target.add.toString()).toEqual(expect.stringContaining('customAdd'))

    expect(target.add(5)).toEqual(25)
    expect(spy3.mock.calls.length).toBe(2)
    expect(spy2.mock.calls.length).toBe(3)
    expect(spy1.mock.calls.length).toBe(1)
    expect(spyCallOrder).toEqual(['spy2'])

    target.add = originalAdd
    spyCallOrder.length = 0

    expect(target.add(5)).toEqual(30)
    expect(spy3.mock.calls.length).toBe(2)
    expect(spy2.mock.calls.length).toBe(3)
    expect(spy1.mock.calls.length).toBe(1)
    expect(spyCallOrder).toEqual([])

    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('aop handles async cleanup and manual method override', async () => {
    const spy1 = vi.fn()
    const spy2 = vi.fn()
    const spy3 = vi.fn()
    const spyCallOrder: string[] = []

    const target = {
      value: 10,
      add(value: number) {
        this.value += value

        return this.value
      }
    }

    const restore1 = aop(target, 'add', function (original, args) {
      spy1(args[0])
      spyCallOrder.push('spy1')
      return original.apply(this, args)
    })

    const restore2 = aop(target, 'add', function (original, args) {
      spy3(args[0])
      spyCallOrder.push('spy3')
      return original.apply(this, args)
    })

    const originalAdd = target.add
    target.add = function customAdd(value: number) {
      spy2(value)
      spyCallOrder.push('spy2')
      return originalAdd.apply(this, [value])
    }

    await new Promise<void>((resolve) => {
      setTimeout(() => {
        restore2()
        resolve()
      }, 300)
    })

    expect(Object.keys(AOP_STACK).length).toEqual(1)

    expect(target.add(5)).toEqual(15)
    expect(spy2).toHaveBeenCalledWith(5)
    expect(spy1).toHaveBeenCalledWith(5)
    expect(spyCallOrder).toEqual(['spy2', 'spy1'])
    expect(target.add.toString()).toEqual(expect.stringContaining('customAdd'))

    restore1()
    spyCallOrder.length = 0

    expect(target.add(5)).toEqual(20)
    expect(spyCallOrder).toEqual(['spy2'])
    expect(target.add.toString()).toEqual(expect.stringContaining('customAdd'))

    expect(Object.keys(AOP_STACK).length).toEqual(0)
  })

  test('resolveURL test cases', () => {
    expect(resolveURL('')).toEqual({})

    expect(resolveURL('https://user:<EMAIL>:8080/s?wd=123#section/hello?uid=8899')).toEqual({
      path: '/s',
      search: '?wd=123',
      protocol: 'https',
      hash: '#section/hello?uid=8899',
      host: 'user:<EMAIL>:8080',
      relative: '/s?wd=123#section/hello?uid=8899'
    })

    expect(resolveURL('https://example.com')).toEqual({
      search: '',
      host: 'example.com',
      path: '',
      hash: '',
      protocol: 'https',
      relative: ''
    })

    expect(resolveURL('https://example.com?wd=123')).toEqual({
      search: '?wd=123',
      host: 'example.com',
      path: '',
      hash: '',
      protocol: 'https',
      relative: '?wd=123'
    })

    expect(resolveURL({ match: () => null } as any)).toEqual({})
  })

  test('encode URI test cases', () => {
    expect(URIEncode('https://example.com')).toEqual('https%3A%2F%2Fexample.com')
    expect(URIEncode('https://example.com?wd=123')).toEqual('https%3A%2F%2Fexample.com%3Fwd%3D123')
  })

  test('now test cases', () => {
    const original = Date.now

    expect(Date.now.toString()).toContain('native code')

    Date.now = vi.fn(() => 1234567890)
    expect(now()).toEqual(1234567890)
    expect(Date.now.toString()).not.toContain('native code')

    Date.now = null as any
    expect(now()).toEqual(expect.any(Number))
    expect(now().toString().length).toEqual(13)

    Date.now = original
    expect(Date.now.toString()).toContain('native code')
  })

  test('isFunction test cases', () => {
    expect(isFunction(() => {})).toEqual(true)
    expect(isFunction(function () {})).toEqual(true)
    expect(isFunction(function* () {})).toEqual(true)
    expect(isFunction(async function () {})).toEqual(true)
    expect(isFunction(async function* () {})).toEqual(true)
  })

  test('isString test cases', () => {
    expect(isString('')).toEqual(true)
    expect(isString('test')).toEqual(true)
    expect(isString('123')).toEqual(true)
    expect(isString('true')).toEqual(true)
    expect(isString('false')).toEqual(true)
    expect(isString('null')).toEqual(true)
    expect(isString('undefined')).toEqual(true)
    expect(isString('[]')).toEqual(true)
    expect(isString('{}')).toEqual(true)
    expect(isString(1)).toEqual(false)
    expect(isString(true)).toEqual(false)
    expect(isString(false)).toEqual(false)
    expect(isString(null)).toEqual(false)
    expect(isString(undefined)).toEqual(false)
    expect(isString([])).toEqual(false)
    expect(isString({})).toEqual(false)
    expect(isString(() => {})).toEqual(false)
    expect(isString(function () {})).toEqual(false)
    expect(isString(function* () {})).toEqual(false)
    expect(isString(async function () {})).toEqual(false)
    expect(isString(async function* () {})).toEqual(false)
  })

  test('isArray test cases', () => {
    expect(isArray([])).toEqual(true)
    expect(isArray([1, 2, 3])).toEqual(true)
    expect(isArray(new Array(10))).toEqual(true)
    expect(isArray(new Array(1, 2, 3))).toEqual(true)
    expect(isArray(new Array('1', '2', '3'))).toEqual(true)
    expect(isArray('')).toEqual(false)
    expect(isArray('test')).toEqual(false)
    expect(isArray('123')).toEqual(false)
    expect(isArray('true')).toEqual(false)
    expect(isArray('false')).toEqual(false)
    expect(isArray('null')).toEqual(false)
    expect(isArray('undefined')).toEqual(false)
    expect(isArray(1)).toEqual(false)
    expect(isArray(true)).toEqual(false)
    expect(isArray(false)).toEqual(false)
    expect(isArray(null)).toEqual(false)
    expect(isArray(undefined)).toEqual(false)
    expect(isArray({})).toEqual(false)
    expect(isArray(() => {})).toEqual(false)
  })

  test('createCleanTask test cases', () => {
    const spy = vi.fn()
    const cleanupTasks = createCleanupTask()

    cleanupTasks.add(spy)

    expect(spy.mock.calls.length).toEqual(0)
    cleanupTasks.dispose()
    expect(spy.mock.calls.length).toEqual(1)
  })

  test('json test cases', () => {
    expect(json.parse('')).toEqual({})
    expect(json.parse('null')).toEqual(null)
    expect(json.parse('undefined')).toEqual({})
    expect(json.parse('true')).toEqual(true)
    expect(json.parse('false')).toEqual(false)
    expect(json.parse('123')).toEqual(123)
    expect(json.parse('[]')).toEqual([])
    expect(json.parse('{}')).toEqual({})
    expect(json.parse('{"a":1}')).toEqual({ a: 1 })
    expect(json.parse('{"a":1')).toEqual({})

    expect(json.stringify({})).toEqual('{}')
    expect(json.stringify(null)).toEqual('null')
    expect(json.stringify(undefined)).toEqual(undefined)
    expect(json.stringify(true)).toEqual('true')
    expect(json.stringify(false)).toEqual('false')
    expect(json.stringify(123)).toEqual('123')
    expect(json.stringify([])).toEqual('[]')
    expect(json.stringify({})).toEqual('{}')
    expect(json.stringify({ a: 1 })).toEqual('{"a":1}')

    const obj = {} as any
    obj.self = obj

    expect(json.stringify(obj)).toEqual('')
  })

  test('getRegexp test cases', () => {
    expect(getRegexp([])).toEqual(null)
    expect(getRegexp(['test'])).toEqual(/test/i)
    expect(getRegexp([/test/])).toEqual(/test/i)
    expect(getRegexp(['test', /test/])).toEqual(/test|test/i)
    expect(getRegexp([/test/, 'test'])).toEqual(/test|test/i)
    expect(getRegexp(['test', 'test'])).toEqual(/test|test/i)
    expect(getRegexp([/test/, /test/])).toEqual(/test|test/i)
    expect(getRegexp([123] as any)).toEqual(/(?:)/i)
  })

  test('check is ignored test cases', () => {
    expect(checkIsIgnored(['http://www.example.com'], 'http://www.example.com')).toEqual(true)
    expect(checkIsIgnored(['http://www.example.com'], 'http://www.example.com/path')).toEqual(true)
    expect(checkIsIgnored(['http://www.example.com'], 'http://www.example.com/path2')).toEqual(true)
    expect(checkIsIgnored(['http://www.example.com'], 'http://www.example2.com')).toEqual(false)
    expect(checkIsIgnored(null as any, 'http://www.example2.com/path')).toEqual(false)
    expect(checkIsIgnored([new RegExp('http://www.example.com')], 'http://www.example.com')).toEqual(true)
  })

  test('isWeb test cases', () => {
    expect(isWeb('web')).toEqual(true)
    expect(isWeb('nodejs')).toEqual(false)
    expect(isWeb('miniapp')).toEqual(false)
    expect(isWeb('rn')).toEqual(false)
    expect(isWeb('')).toEqual(false)
    expect(isWeb('undefined')).toEqual(false)
  })

  test('isNodejs test cases', () => {
    expect(isNodejs('web')).toEqual(false)
    expect(isNodejs('nodejs')).toEqual(true)
    expect(isNodejs('miniapp')).toEqual(false)
    expect(isNodejs('rn')).toEqual(false)
    expect(isNodejs('')).toEqual(false)
    expect(isNodejs('undefined')).toEqual(false)
  })

  test('isMiniapp test cases', () => {
    expect(isMiniapp('web')).toEqual(false)
    expect(isMiniapp('nodejs')).toEqual(false)
    expect(isMiniapp('miniapp')).toEqual(true)
    expect(isMiniapp('rn')).toEqual(false)
    expect(isMiniapp('')).toEqual(false)
    expect(isMiniapp('undefined')).toEqual(false)
  })

  test('isReactNative test cases', () => {
    expect(isReactNative('web')).toEqual(false)
    expect(isReactNative('nodejs')).toEqual(false)
    expect(isReactNative('miniapp')).toEqual(false)
    expect(isReactNative('rn')).toEqual(true)
    expect(isReactNative('')).toEqual(false)
    expect(isReactNative('undefined')).toEqual(false)
  })
})
