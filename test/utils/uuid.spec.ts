import uuid from '../../src/utils/uuid'
import { describe, expect, test, vi } from 'vitest'

describe('uuid', () => {
  test('use native randomUUID', () => {
    const id = uuid()
    expect(id.length).toEqual(32)
  })

  test('should generate a valid uuid', () => {
    vi.stubGlobal('crypto', {
      randomUUID: undefined
    })

    const id = uuid()
    expect(id.length).toEqual(32)

    vi.unstubAllGlobals()
  })
})
