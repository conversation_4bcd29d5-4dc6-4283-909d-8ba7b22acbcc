import { describe, expect, test } from 'vitest'
import cookie from '../../../src/utils/browser/cookie'

describe('cookie', () => {
  test('cookie.get', () => {
    document.cookie = 'a=1; path=/;'

    expect(cookie.get('a')).toEqual('1')
    expect(cookie.get('b')).toEqual(undefined)

    document.cookie = 'a=1; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  })

  test('cookie string is empty', () => {
    expect(cookie.get('a')).toEqual(undefined)
  })

  test('cookie.set', () => {
    const expires = new Date()
    expires.setFullYear(expires.getFullYear() + 1)

    cookie.set('a', '1', expires)

    expect(document.cookie).toEqual('a=1')
    expect(cookie.get('a')).toEqual('1')
    document.cookie = 'a=1; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  })

  test('not in browser environment, cookie not available', () => {
    const originalCookieAvailable = cookie.available

    cookie.available = false

    const expires = new Date()
    expires.setFullYear(expires.getFullYear() + 1)

    cookie.set('a', '1', expires)

    expect(cookie.get('a')).toEqual(undefined)

    cookie.available = originalCookieAvailable
  })
})
