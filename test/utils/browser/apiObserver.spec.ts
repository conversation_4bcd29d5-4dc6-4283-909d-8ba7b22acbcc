import { describe, expect, test, vi } from 'vitest'
import { createApiObserver, supportsNativeFetch } from '../../../src/utils/browser/apiObserver'

describe('api observer', () => {
  test('create api observer', async () => {
    const observer = createApiObserver(undefined as any)

    const fetchSpy = vi.spyOn(window, 'fetch')
    await window.fetch('/test-url')
    expect(fetchSpy).toHaveBeenCalledWith('/test-url')

    const xhr = new XMLHttpRequest()
    const xhrOpenSpy = vi.spyOn(xhr, 'open')
    const xhrSendSpy = vi.spyOn(xhr, 'send')

    xhr.open('GET', '/test-url')
    expect(xhrOpenSpy).toHaveBeenCalledWith('GET', '/test-url')

    xhr.send()
    expect(xhrSendSpy).toHaveBeenCalled()

    observer.dispose()
    fetchSpy.mockRestore()
    xhrOpenSpy.mockRestore()
    xhrSendSpy.mockRestore()
  })

  test('supports native fetch', () => {
    const originalFetch = window.fetch
    const originalFetchToString = window.fetch.toString

    expect(supportsNativeFetch()).toEqual(true)

    // @ts-ignore
    delete window.fetch
    expect(supportsNativeFetch()).toEqual(false)
    window.fetch = originalFetch

    vi.stubGlobal('Headers', function () {
      throw new Error('error')
    })
    expect(supportsNativeFetch()).toEqual(false)
    vi.unstubAllGlobals()

    window.fetch.toString = (() => {}) as any
    expect(supportsNativeFetch()).toEqual(true)
    window.fetch.toString = originalFetchToString
  })

  test('supportsNativeFetch return false, because document.createElement is not available', () => {
    const originalFetchToString = window.fetch.toString
    const originalCreateElement = document.createElement

    // @ts-ignore
    document.createElement = undefined
    window.fetch.toString = (() => {}) as any

    expect(supportsNativeFetch()).toEqual(false)

    window.fetch.toString = originalFetchToString
    document.createElement = originalCreateElement
  })

  test('supportsNativeFetch return false, because sandbox.contentWindow is not available', () => {
    const originalFetchToString = window.fetch.toString
    const originalCreateElement = document.createElement

    document.createElement = (tagName: string) => {
      return originalCreateElement.call(document, tagName === 'iframe' ? 'div' : tagName)
    }
    window.fetch.toString = (() => {}) as any

    expect(supportsNativeFetch()).toEqual(false)

    window.fetch.toString = originalFetchToString
    document.createElement = originalCreateElement
  })
})
