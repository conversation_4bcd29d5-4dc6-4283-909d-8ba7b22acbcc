import { describe, expect, test } from 'vitest'
import htmlTreeStringify from '../../../src/utils/browser/htmlTreeStringify'

describe('html tree stringify', () => {
  test('stringify a simple tree', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <button>
        <span>hello</span>
      </button>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > button > span')
  })

  test('stringify a tree with id', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div id="id1">
        <div>
          <span>hello</span>
        </div>
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div#id1 > div > span')
  })

  test('stringify a long tree, max tree length is 5 floor', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div id="id1">
        <div>
          <div>
            <div>
              <div>
                <div>
                  <div>
                    <div>
                      <div>
                        <div>
                          <span>hello</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>  
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div > div > div > span')
  })

  test('stringify a tree with className', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div class="class1">
        <div>
          <span>hello</span>
        </div>
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div.class1 > div > span')
  })

  test('stringify a tree with multiple className', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div class="class1 class2">
        <div>
          <span>hello</span>
        </div>
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div.class1.class2 > div > span')
  })

  test('stringify a tree with multiple className and id', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div class="class1 class2" id="id1">
        <div>
          <span>hello</span>
        </div>
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div#id1.class1.class2 > div > span')
  })

  test('stringify a tree with attribute', () => {
    const div = document.createElement('div')
    div.innerHTML = `
      <div class="class1 class2" id="id1" title="test">
        <div>
          <span>hello</span>
        </div>
      </div>
    `
    expect(htmlTreeStringify(div.querySelector('span'))).toEqual('div > div#id1.class1.class2[title="test"] > div > span')
  })

  test('stringify unknown element', () => {
    expect(htmlTreeStringify(1)).toEqual('')
    expect(htmlTreeStringify({})).toEqual('')
    expect(htmlTreeStringify([])).toEqual('')
    expect(htmlTreeStringify('')).toEqual('<unknown>')
    expect(htmlTreeStringify(null)).toEqual('<unknown>')
    expect(
      htmlTreeStringify({
        tagName: 'div',
        parentNode: null
      })
    ).toBe('<unknown>')
  })

  test('stringify mock html element ', () => {
    const mockElement = {
      tagName: 'html',
      getAttribute: () => ''
    }

    expect(htmlTreeStringify(mockElement)).toEqual('')
  })
})
