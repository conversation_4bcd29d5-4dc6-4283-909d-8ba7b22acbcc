import { describe, expect, test, vi } from 'vitest'
import { attachPageUnloadEvent, setObjectProperty, parseBackgroundImageUrls } from '../../../src/utils/browser/utils'

describe('browser utils', () => {
  test('attach page unload event test cases', () => {
    const spy = vi.fn()
    let restore = attachPageUnloadEvent(spy)

    expect(spy.mock.calls.length).toEqual(0)
    window.dispatchEvent(new Event('unload'))
    expect(spy.mock.calls.length).toEqual(1)
    restore()

    restore = attachPageUnloadEvent(spy)
    window.dispatchEvent(new Event('pagehide'))
    expect(spy.mock.calls.length).toEqual(2)
    restore()

    restore = attachPageUnloadEvent(spy)
    window.dispatchEvent(new Event('beforeunload'))
    expect(spy.mock.calls.length).toEqual(3)
    restore()

    window.dispatchEvent(new Event('unload'))
    expect(spy.mock.calls.length).toEqual(3)
    window.dispatchEvent(new Event('pagehide'))
    expect(spy.mock.calls.length).toEqual(3)
    window.dispatchEvent(new Event('beforeunload'))
    expect(spy.mock.calls.length).toEqual(3)
  })

  test('setObjectProperty test cases', () => {
    vi.spyOn(console, 'warn').mockImplementation(vi.fn())

    const obj = {
      a: 1,
      b: 2,
      c: [1, 2]
    }
    Object.defineProperty(obj, 'a', {
      writable: false
    })
    Object.defineProperty(obj.c, 1, {
      writable: false
    })

    setObjectProperty(obj, 'a', 2)
    expect(obj.a).toEqual(1)
    setObjectProperty(obj, 'b', 3)
    expect(obj.b).toEqual(3)
    setObjectProperty(obj.c, 1, 3)
    expect(obj.c[1]).toEqual(2)
    setObjectProperty(obj.c, 0, 2)
    expect(obj.c[0]).toEqual(2)

    vi.restoreAllMocks()
  })

  test('should handle element without background', () => {
    const div = document.createElement('div')
    expect(parseBackgroundImageUrls(div.style.backgroundImage, '')).toEqual([])
  })

  test('should extract single background url', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("https://example.com/image.jpg")'
    expect(parseBackgroundImageUrls(div.style.backgroundImage, '')).toEqual(['https://example.com/image.jpg'])
  })

  test('should handle relative urls', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("/image.jpg")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, location.href)
    expect(urls[0]).toEqual(`${location.origin}/image.jpg`)
  })

  test('should handle multiple background images', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("image1.jpg"), url("image2.jpg")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, location.href)
    expect(urls.length).toBe(2)
    expect(urls[0]).toEqual(new URL('image1.jpg', location.href).href)
    expect(urls[1]).toEqual(new URL('image2.jpg', location.href).href)
  })

  test('should handle background image with data url and gradient', () => {
    const div = document.createElement('div')
    div.style.backgroundImage =
      'url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="), url("https://example.com/image.jpg"),linear-gradient(to right, #000, #fff)'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, '')
    expect(urls.length).toBe(1)
    expect(urls[0]).toEqual('https://example.com/image.jpg')
  })

  test('should handle empty url in url() function', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, '')
    expect(urls).toEqual([])
  })

  test('should handle url() with only whitespace', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("   ")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, '')
    expect(urls).toEqual([])
  })

  test('should handle duplicate urls', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url("image.jpg"), url("image.jpg")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, location.href)
    expect(urls.length).toBe(1)
    expect(urls[0]).toEqual(new URL('image.jpg', location.href).href)
  })

  test('should handle malformed url() functions', () => {
    const div = document.createElement('div')
    div.style.backgroundImage = 'url(), url("image.jpg")'
    const urls = parseBackgroundImageUrls(div.style.backgroundImage, location.href)
    expect(urls.length).toBe(1)
    expect(urls[0]).toEqual(new URL('image.jpg', location.href).href)
  })
})
