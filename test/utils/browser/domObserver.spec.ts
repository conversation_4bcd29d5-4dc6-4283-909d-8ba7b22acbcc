import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest'
import { createDomObserver } from '../../../src/utils/browser/domObserver'

describe('dom observer', () => {
  let container: HTMLDivElement

  beforeEach(() => {
    container = document.createElement('div')
    document.body.appendChild(container)
  })

  afterEach(() => {
    document.body.removeChild(container)
  })

  test('target is null, throw error', () => {
    expect(() => createDomObserver(null as any, () => {})).toThrow('Target node is required')
  })

  test('observe DOM changes using MutationObserver', () => {
    const callback = vi.fn()
    const observer = createDomObserver(container, callback)

    const child = document.createElement('div')
    container.appendChild(child)

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        expect(callback).toHaveBeenCalled()
        const mutations = callback.mock.calls[0][0]
        expect(mutations[0].type).toBe('childList')
        expect(mutations[0].target).toBe(container)

        observer.dispose()
        resolve()
      }, 50)
    })
  })

  test('dispose observer should stop observing DOM changes', () => {
    const callback = vi.fn()
    const observer = createDomObserver(container, callback)

    observer.dispose()

    const child = document.createElement('div')
    container.appendChild(child)

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        expect(callback).not.toHaveBeenCalled()
        resolve()
      }, 50)
    })
  })

  test('fallback to DOMSubtreeModified event', () => {
    const originalMutationObserver = window.MutationObserver

    window.MutationObserver = null as any

    const callback = vi.fn()
    const observer = createDomObserver(container, callback)

    const child = document.createElement('span')
    container.appendChild(child)

    container.removeChild(child)

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        expect(callback).toHaveBeenCalled()
        const mutations = callback.mock.calls[0][0]
        expect(mutations[0].type).toBe('childList')
        expect(mutations[0].target).toBe(child)

        observer.dispose()
        window.MutationObserver = originalMutationObserver
        resolve()
      }, 50)
    })
  })
})
