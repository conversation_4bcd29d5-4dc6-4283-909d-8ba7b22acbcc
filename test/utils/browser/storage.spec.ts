import { describe, expect, test } from 'vitest'
import browserStorage from '../../../src/utils/browser/storage'

describe('browserStorage', () => {
  test('browserStorage.get', () => {
    localStorage.setItem('a', '1')

    expect(browserStorage.get('a')).toEqual('1')
    expect(browserStorage.get('b')).toEqual(undefined)

    localStorage.removeItem('a')
  })

  test('browserStorage is empty', () => {
    expect(browserStorage.get('a')).toEqual(undefined)
  })

  test('browserStorage.set', () => {
    browserStorage.set('a', '1')

    expect(localStorage.getItem('a')).toEqual('1')

    localStorage.removeItem('a')
  })

  test('not in browser environment, browserStorage not available', () => {
    const originalBrowserStorageAvailable = browserStorage.available

    browserStorage.available = false

    browserStorage.set('a', '1')

    expect(browserStorage.get('a')).toEqual(undefined)

    browserStorage.available = originalBrowserStorageAvailable
  })
})
