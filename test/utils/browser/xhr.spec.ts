import { describe, expect, test, vi } from 'vitest'
import xhrRequest from '../../../src/utils/browser/xhr'

describe('request', () => {
  const hostname = `http://localhost:3333`

  test.skip('POST request fail', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      xhrRequest({
        url: `${hostname}/status/404`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        },
        retries: 0
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  })

  test('POST request success', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      xhrRequest({
        url: `${hostname}/post`,
        data: { foo: 'bar' },
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith(null, '{"foo":"bar"}')
  })

  test.skip('POST request retry', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      xhrRequest({
        url: `${hostname}/status/404`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  }, 15000)

  test.skip('POST request retry with query', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      xhrRequest({
        url: `${hostname}/status/404?hello=world`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  }, 15000)

  test('__AEGIS_XHR__ when available', async () => {
    let stateCallback: any
    const spyOpen = vi.fn()
    const spySend = vi.fn()
    const spySetRequestHeader = vi.fn()

    const mockXHR = function (this: any) {
      this.open = spyOpen
      this.send = spySend
      this.setRequestHeader = spySetRequestHeader
      this.readyState = 4
      this.status = 200
      this.responseText = '{"foo":"bar"}'

      Object.defineProperty(this, 'onreadystatechange', {
        set(fn) {
          stateCallback = fn
        }
      })
    }

    window['__AEGIS_XHR__'] = mockXHR

    const spy = vi.fn()

    const promise = new Promise((resolve) => {
      xhrRequest({
        url: `${hostname}/post`,
        data: { foo: 'bar' },
        callback: (err: Error | null, responseText?: string) => {
          spy(err, responseText)
          resolve(undefined)
        }
      })
    })

    stateCallback()
    await promise

    expect(spy).toHaveBeenCalledWith(null, '{"foo":"bar"}')
    expect(spyOpen).toHaveBeenCalledWith('POST', `${hostname}/post`, true)
    expect(spySetRequestHeader).toHaveBeenCalledWith('Content-Type', 'application/json;charset=UTF-8')
    expect(spySend).toHaveBeenCalledWith('{"foo":"bar"}')

    window['__AEGIS_XHR__'] = undefined
  })
})
