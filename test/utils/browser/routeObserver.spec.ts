import { noop } from '../../../src/utils'
import { describe, expect, test, vi } from 'vitest'
import { createRouteObserver } from '../../../src/utils/browser/routeObserver'

describe('route observer', () => {
  test('create route observer', () => {
    vi.stubGlobal('history', {
      pushState: vi.fn(),
      replaceState: vi.fn()
    })

    const addEventListenerSpy = vi.spyOn(window, 'addEventListener')
    const observer = createRouteObserver(undefined as any)

    expect(addEventListenerSpy).toHaveBeenCalledWith('popstate', noop, true)

    expect(history.pushState).toBeDefined()
    expect(history.replaceState).toBeDefined()

    observer.dispose()

    vi.unstubAllGlobals()
    vi.clearAllMocks()
  })

  test('use replaceState change same url', () => {
    vi.stubGlobal('history', {
      pushState: vi.fn(),
      replaceState: vi.fn()
    })

    const observer = createRouteObserver(undefined as any)

    history.replaceState({}, '', location.pathname)

    observer.dispose()

    vi.unstubAllGlobals()
    vi.clearAllMocks()
  })
})
