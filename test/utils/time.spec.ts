import { normalizeTimestamp } from './../../src/utils/time'
import { describe, expect, test, vi } from 'vitest'

describe('normalizeTimestamp', () => {
  test('rounds to nearest integer', () => {
    expect(normalizeTimestamp(123.456)).toBe(123)
    expect(normalizeTimestamp(123.567)).toBe(124)
  })

  test('returns 0 for non-numeric inputs', () => {
    expect(normalizeTimestamp('123')).toBe(0)
    expect(normalizeTimestamp(null)).toBe(0)
    expect(normalizeTimestamp(undefined)).toBe(0)
    expect(normalizeTimestamp(NaN)).toBe(0)
    expect(normalizeTimestamp({})).toBe(0)
    expect(normalizeTimestamp([])).toBe(0)
  })

  test('returns integer input unmodified', () => {
    expect(normalizeTimestamp(123)).toBe(123)
    expect(normalizeTimestamp(-456)).toBe(-456)
  })

  test('handles negative numbers', () => {
    expect(normalizeTimestamp(-123.456)).toBe(-123)
    expect(normalizeTimestamp(-123.567)).toBe(-124)
  })
})
