import { isBrowser } from '../../src/utils/is'
import fetchRequest from '../../src/utils/fetch'
import { describe, expect, test, vi } from 'vitest'

describe('request', () => {
  const hostname = `http://localhost:3333`

  test('POST request fail', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      fetchRequest({
        url: `${hostname}/status/404`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        },
        retries: 0
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  })

  test('POST request success', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      fetchRequest({
        url: `${hostname}/post`,
        data: { foo: 'bar' },
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith(null, '{"foo":"bar"}')
  })

  test('POST request retry', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      fetchRequest({
        url: `${hostname}/status/404`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  }, 15000)

  test('POST request retry with query', async () => {
    const spy = vi.fn()

    await new Promise((resolve) => {
      fetchRequest({
        url: `${hostname}/status/404?hello=world`,
        data: {},
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err?.message, responseText))
        }
      })
    })

    expect(spy).toBeCalledWith('Request failed with status: 404', undefined)
  }, 15000)

  test('__AEGIS_FETCH__ when available', async () => {
    const mockFetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      text: () => Promise.resolve('{"foo":"bar"}')
    })

    let originalWindow: any = null

    if (isBrowser) {
      window['__AEGIS_FETCH__'] = mockFetch
    } else {
      originalWindow = globalThis.window
      globalThis.window = { __AEGIS_FETCH__: mockFetch } as any
    }

    const spy = vi.fn()

    await new Promise((resolve) => {
      fetchRequest({
        url: `${hostname}/post`,
        data: { foo: 'bar' },
        callback: (err: Error | null, responseText?: string) => {
          resolve(spy(err, responseText))
        }
      })
    })

    expect(mockFetch).toHaveBeenCalledWith(
      `${hostname}/post`,
      expect.objectContaining({
        method: 'POST',
        body: '{"foo":"bar"}',
        headers: { 'Content-Type': 'application/json;charset=UTF-8' }
      })
    )
    expect(spy).toHaveBeenCalledWith(null, '{"foo":"bar"}')

    if (isBrowser) {
      window['__AEGIS_FETCH__'] = undefined
    } else {
      globalThis.window['__AEGIS_FETCH__'] = undefined
      globalThis.window = originalWindow
    }
  })
})
