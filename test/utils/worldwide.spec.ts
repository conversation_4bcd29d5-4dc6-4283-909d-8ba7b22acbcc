import { isBrowser, isNode } from '../../src/utils/is'
import { describe, expect, test, vi } from 'vitest'
import { BrowserTiming, getBrowserTiming } from '../../src/utils/worldwide'

describe('BrowserTiming', () => {
  test.runIf(isNode)('browserTiming is a empty object', () => {
    expect(BrowserTiming).toEqual({})
  })

  test.runIf(isBrowser)('browserTiming is default object', () => {
    expect(BrowserTiming).toEqual({
      initTime: 0,
      deviceId: '',
      darkMode: '',
      pageType: '',
      userIdEcpt: '',
      networkType: '',
      deviceModel: '',
      deviceVendor: '',
      startLoadTime: 0,
      onStartedTime: 0,
      isLowPowerMode: false
    })
  })

  test.runIf(isBrowser)('simulate browserTiming', () => {
    window['BrowserTiming'] = {
      initTime: 1713542400000,
      deviceId: '1234567890',
      darkMode: 'dark',
      pageType: 'fullscreen',
      userIdEcpt: '1234567890',
      networkType: 'wifi',
      deviceModel: 'iPhone 12',
      deviceVendor: 'Apple',
      startLoadTime: 1713542400000,
      onStartedTime: 1713542400000,
      isLowPowerMode: true
    }

    expect(getBrowserTiming()).toEqual({
      initTime: 1713542400000,
      deviceId: '1234567890',
      darkMode: 'dark',
      pageType: 'fullscreen',
      userIdEcpt: '1234567890',
      networkType: 'wifi',
      deviceModel: 'iPhone 12',
      deviceVendor: 'Apple',
      startLoadTime: 1713542400000,
      onStartedTime: 1713542400000,
      isLowPowerMode: true
    })

    delete window['BrowserTiming']
  })

  test.runIf(isBrowser)('simulate browserTiming with undefined', () => {
    window['BrowserTiming'] = undefined

    expect(BrowserTiming).toEqual({
      initTime: 0,
      deviceId: '',
      darkMode: '',
      pageType: '',
      userIdEcpt: '',
      networkType: '',
      deviceModel: '',
      deviceVendor: '',
      startLoadTime: 0,
      onStartedTime: 0,
      isLowPowerMode: false
    })

    delete window['BrowserTiming']
  })

  test.runIf(isBrowser)('BrowserTiming field is not a number', () => {
    window['BrowserTiming'] = {
      initTime: '1713542400000'
    }

    expect(getBrowserTiming().initTime).toEqual(1713542400000)

    delete window['BrowserTiming']
  })

  test.runIf(isBrowser)('BrowserTiming field is not a boolean', () => {
    window['BrowserTiming'] = {
      isLowPowerMode: 'false'
    }

    expect(getBrowserTiming().isLowPowerMode).toEqual(false)

    delete window['BrowserTiming']
  })
})
