// eslint-disable-next-line @typescript-eslint/no-explicit-any
type anyType = any

interface Window {
  polyfill: boolean
  __AEGIS_FETCH__?: typeof fetch
  __AEGIS_XHR__?: typeof XMLHttpRequest
  AEJSBridge?: {
    dispatch: (options: { handlerName: string; params: Record<string, unknown>; callback: (reuslt: { data: Record<string, unknown> | string | null; msg: string; code: 0 | -1 }) => void }) => void
  }
  WebViewJavascriptBridge?: Record<string, unknown>
  __loadertest__: anyType
  BrowserTiming: {
    initTime: number
    startLoadTime: number
    onStartedTime: number
    networkType: string
    isLowPowerMode: boolean
    pageType: 'overlay' | 'popup' | 'fullscreen'
    darkMode: 'dark' | 'light'
    deviceId: string
    deviceVendor: string
    deviceModel: string
    userIdEcpt: string
  }
  __SPA_LOAD_INITIALIZED__: boolean
  __SPA_LOAD_READY_CALLBACK__: () => void
}

interface Navigator {
  connection: {
    effectiveType: string
    type: string
  }
  mozConnection: {
    effectiveType: string
    type: string
  }
  webkitConnection: {
    effectiveType: string
    type: string
  }
}

interface XMLHttpRequest {
  __xhr_data__: {
    status?: number
    url: string | undefined
    method: string | undefined
    headers: Record<string, string>
    body?: Document | XMLHttpRequestBodyInit | null | undefined
  }
}

interface Vue2Instance {
  $router?: {
    options?: {
      scrollBehavior?: () => void
    }
  }
  $root?: {
    $options?: {
      _base?: {
        version?: string
      }
    }
    constructor?: {
      config?: {
        errorHandler?: () => void
      }
    }
  }
}

interface Vue3Instance {
  version?: string
  config?: {
    globalProperties?: {
      $router?: {
        options?: {
          scrollBehavior?: () => void
        }
      }
    }
    errorHandler?: () => void
  }
}

interface Element {
  __vue__?: Vue2Instance
  __vue_app__?: Vue3Instance
}
