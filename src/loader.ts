// @ts-nocheck
/* eslint-disable */

export default function scriptLoader(scriptUrl, globalName, callback) {
  const queue = []
  const win = window
  const precollect = 'precollect'
  const addEventListener = 'addEventListener'
  const script = document.createElement('script')
  const removeEventListener = 'removeEventListener'
  const stubMethodNames = [
    'on',
    'off',
    'emit',
    'contains',
    'setConfig',
    'provide',
    'report',
    'build',
    'send',
    'destroy',
    'sendPV',
    precollect,
    'reportBridgeRequest',
    'reportResourceError',
    'reportHttpRequest',
    'captureException',
    'largePictureInspect'
  ]

  win['__AEGIS_FETCH__'] = fetch
  win['__AEGIS_XHR__'] = XMLHttpRequest
  win[globalName] = win[globalName] || {}

  // stubbing
  stubMethodNames.forEach(function (methodName) {
    win[globalName][methodName] = function () {
      queue.push([methodName, arguments])
    }
  })

  script.src = scriptUrl
  script.crossOrigin = 'anonymous'
  script.onload = function () {
    callback()
    // In production, removes the setTimeout and keep the callback
    // It's only for testing purposes
    setTimeout(function () {
      win[removeEventListener]('error', handleError, true)
      win[removeEventListener]('unhandledrejection', handleRejectError, true)
    }, 50)

    queue.forEach(function (item) {
      const method = win[globalName][item[0]]

      if (method) {
        method.apply(win[globalName], item[1])
      } else {
        console.error('Plugin not found:', item[0])
      }
    })

    queue.length = 0
  }
  // pre collect resource error, js error, promise error
  function handleError(event) {
    event = event || win.event
    const target = event.target || event.srcElement

    if (target instanceof Element) {
      win[globalName][precollect]('sr', target, now())
    } else {
      win[globalName][precollect]('js', event, now())
    }
  }

  function handleRejectError(event) {
    event = event || win.event
    win[globalName][precollect]('js', event, now())
  }

  function now() {
    return Date.now ? Date.now() : +new Date()
  }

  win[addEventListener]('error', handleError, true)
  win[addEventListener]('unhandledrejection', handleRejectError, true)

  document.getElementsByTagName('head')[0].appendChild(script)
}
