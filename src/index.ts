import Aegis from './core/Aegis'
import api from './integrations/browser/api'
import HttpTransport from './transports/http'
import bridge from './integrations/browser/bridge'
import action from './integrations/browser/action'
import device from './integrations/browser/device'
import ConsoleTransport from './transports/console'
import feature from './integrations/browser/feature'
import jsError from './integrations/browser/jsError'
import perf from './integrations/browser/performance'
import pageview from './integrations/browser/pageview'
import resource from './integrations/browser/resource'
import PixelTransport from './transports/browser/pixel'
import precollect from './integrations/browser/precollect'
import blankScreen from './integrations/browser/blankScreen'
import resourceError from './integrations/browser/resourceError'
import largePictureInspect from './integrations/browser/largePictureInspect'

export { Aegis, pageview, precollect, action, HttpTransport, PixelTransport, ConsoleTransport, blankScreen, api, bridge, resource, resourceError, jsError, device, perf, largePictureInspect, feature }
