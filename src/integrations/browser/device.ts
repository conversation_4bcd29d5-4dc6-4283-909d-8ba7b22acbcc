import { noop } from '../../utils'
import Aegis from '../../core/Aegis'
import { assign } from '../../utils/polyfills'
import type { Integration } from '../../core/Aegis'
import { BrowserTiming } from '../../utils/worldwide'
import { DEVICE_MATCHERS } from '../../utils/browser/ua-parser'

type DeviceReport = {
  type: 'device'
  immediate?: boolean
  payload: {
    os: string
    sr: string
    dpr: number
    lang: string
    device: string
    engine: string
    browser: string
    engineVer: string
    browserVer: string
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends DeviceReport>(report: P): void
  }
}

interface DeviceOptions extends Record<string, unknown> {
  extra?: (ua: string) => Record<string, string>
}

export default function (options?: DeviceOptions): Integration {
  options = options || {}

  const userAgent = navigator.userAgent || Unknown
  const getExtraDeviceInfo = options.extra || noop

  return {
    name: 'device',
    options: options,
    setup: function (aegis: Aegis) {
      aegis.report({
        type: 'device',
        payload: assign(getDeviceInfo(userAgent), getExtraDeviceInfo(userAgent))
      })
    }
  }
}

const Unknown = 'Unknown'

function getDeviceInfo(userAgent: string) {
  const result = getDeviceVendorAndModel(userAgent)

  return {
    model: BrowserTiming.deviceModel || result.model,
    vendor: BrowserTiming.deviceVendor || result.vendor,
    engine: getEngine(userAgent),
    browser: getBrowser(userAgent),
    device: getDeviceType(userAgent),
    dpr: window.devicePixelRatio || 1,
    lang: navigator.language || Unknown,
    engineVer: getEngineVersion(userAgent),
    browserVer: getBrowserVersion(userAgent),
    os: getOS(userAgent, navigator.platform || ''),
    sr: window.screen.width + 'x' + window.screen.height
  }
}

function getOS(userAgent: string, platform: string): string {
  if (/Android/.test(userAgent)) return 'Android'
  if (/iPhone|iPad|iPod/.test(userAgent)) return 'IOS'
  if (/Mac OS X/.test(userAgent)) return 'MacOS'
  if (/Windows NT|Windows Phone/.test(userAgent)) return 'Windows'
  if (/CrOS/.test(userAgent)) return 'Chrome OS'
  if (/Ubuntu/.test(userAgent)) return 'Ubuntu'
  if (/FreeBSD/.test(userAgent)) return 'FreeBSD'
  if (/Linux/.test(userAgent) || /Linux/.test(platform)) return 'Linux'
  return Unknown
}

function getBrowser(userAgent: string): string {
  if (/MicroMessenger/.test(userAgent)) return 'WeChat'
  if (/Edge\/\d+/.test(userAgent)) return 'Edge'
  if (/Edg\/\d+/.test(userAgent)) return 'Edge (Chromium)'
  if (/OPR\/\d+/.test(userAgent)) return 'Opera'
  if (/Chrome\/\d+/.test(userAgent) && !/Edg/.test(userAgent)) return 'Chrome'
  if (/Safari\/\d+/.test(userAgent) && !/Chrome/.test(userAgent)) return 'Safari'
  if (/Firefox\/\d+/.test(userAgent)) return 'Firefox'
  if (/MSIE|Trident/.test(userAgent)) return 'IE'
  if (/CriOS\/\d+/.test(userAgent)) return 'Chrome (IOS)'
  if (/FxiOS\/\d+/.test(userAgent)) return 'Firefox (IOS)'
  if (/SamsungBrowser\/\d+/.test(userAgent)) return 'Samsung Internet'
  if (/SoulBegin/.test(userAgent)) return 'SoulApp'

  const appMatch = userAgent.match(/(\w+)Begin-iOS-(\d+\.\d+\.\d+)/)

  if (appMatch) {
    return appMatch[1]
  }

  return Unknown
}

function getBrowserVersion(userAgent: string): string {
  const versionRegexps = [
    /MicroMessenger\/([\d.]+)/,
    /SoulBegin-iOS-([\d.]+)/,
    /Edge\/([\d.]+)/,
    /Edg\/([\d.]+)/,
    /OPR\/([\d.]+)/,
    /Chrome\/([\d.]+)/,
    /Safari\/([\d.]+)/,
    /Firefox\/([\d.]+)/,
    /MSIE ([\d.]+)/,
    /Trident\/.*rv:([\d.]+)/,
    /CriOS\/([\d.]+)/,
    /FxiOS\/([\d.]+)/,
    /SamsungBrowser\/([\d.]+)/,
    /Windows Phone(?: OS)? ([\d.]+)/
  ]

  for (let i = 0; i < versionRegexps.length; i++) {
    const match = userAgent.match(versionRegexps[i])

    if (match) {
      return match[1]
    }
  }

  const appMatch = userAgent.match(/(\w+)Begin-iOS-(\d+\.\d+\.\d+)/)

  if (appMatch) {
    return appMatch[2]
  }

  return Unknown
}

function getEngine(userAgent: string): string {
  if (/AppleWebKit\/([\d.]+)/.test(userAgent)) return 'WebKit'
  if (/Gecko\/([\d.]+)/.test(userAgent) && !/like Gecko/.test(userAgent)) return 'Gecko'
  if (/Trident\/([\d.]+)/.test(userAgent)) return 'Trident'
  if (/Presto\/([\d.]+)/.test(userAgent)) return 'Presto'
  return Unknown
}

function getEngineVersion(userAgent: string): string {
  const engineVersionRegexps = [/AppleWebKit\/([\d.]+)/, /Gecko\/([\d.]+)/, /Trident\/([\d.]+)/, /Presto\/([\d.]+)/, /EdgeHTML\/([\d.]+)/]

  for (let i = 0; i < engineVersionRegexps.length; i++) {
    const match = userAgent.match(engineVersionRegexps[i])

    if (match) {
      return match[1]
    }
  }

  return Unknown
}

function getDeviceType(userAgent: string): string {
  if (/Tablet|iPad/.test(userAgent)) {
    return 'tablet'
  }

  if (/Mobile|Android|iP(hone|od|ad)|Windows Phone|webOS|BlackBerry|IEMobile|Opera Mini/.test(userAgent)) {
    return 'mobile'
  }

  if (userAgent === Unknown || !userAgent) {
    return Unknown
  }

  return 'pc'
}

export function getDeviceVendorAndModel(userAgent: string) {
  for (let i = 0; i < DEVICE_MATCHERS.length; i++) {
    const matcher = DEVICE_MATCHERS[i]
    const match = userAgent.match(matcher.regex)

    if (match) {
      return { vendor: matcher.brand, model: match[1] }
    }
  }

  return { vendor: Unknown, model: Unknown }
}
