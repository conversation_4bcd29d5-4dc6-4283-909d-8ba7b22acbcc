import Aegis from '../../core/Aegis'
import { request } from '../../utils'
import { cssSupports } from '../../utils/polyfills'
import type { Integration } from '../../core/Aegis'
import { requestIdleCallback, cancelIdleCallback } from '../../utils/polyfills'

interface IFeature {
  name: string
  skip?: boolean
  async?: boolean
  type: 'API' | 'CSS' | 'IMAGE'
  strategy: 'property' | 'ctor' | 'format'
  params: {
    class?: string
    value?: string
    args?: anyType[]
    context?: string
    property?: string
    base64?: string
  }
}

interface IFeatureResult {
  name: string
  type: 'API' | 'CSS' | 'IMAGE'
  supported: boolean
}

type FeatureReport = {
  type: 'feature'
  immediate?: boolean
  payload: {
    features: IFeatureResult[]
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends FeatureReport>(report: P): void
  }
}

interface FeatureOptions extends Record<string, unknown> {
  remoteUrl?: string
  immediate?: boolean
  features?: IFeature[]
}

export default function (options?: FeatureOptions): Integration {
  options = options || {}

  let idleCallbackId = 0
  const remoteUrl = options.remoteUrl || ''
  const featureList = options.features || []
  const immediate = 'immediate' in options ? options.immediate : false

  return {
    name: 'feature',
    options: options,
    setup: function (aegis: Aegis) {
      const features = builtinFeatures().concat(featureList)

      const reportFeatures = function (results: IFeatureResult[]) {
        aegis.report({
          type: 'feature',
          payload: {
            features: results
          }
        })
      }

      const detectFeatures = function (featureList: IFeature[]) {
        if (immediate) {
          runDetection(featureList, reportFeatures)
        } else {
          idleCallbackId = <number>requestIdleCallback(function () {
            runDetection(featureList, reportFeatures)
          })
        }
      }

      if (remoteUrl) {
        aegis.addReportUrl(remoteUrl)

        getRemoteFeatures(remoteUrl, function (remoteFeatures: IFeature[]) {
          detectFeatures(features.concat(remoteFeatures))
        })
      } else {
        detectFeatures(features)
      }
    },
    tearDown: function () {
      if (idleCallbackId) {
        cancelIdleCallback(idleCallbackId)
      }
    }
  }
}

const detectionStrategies = {
  API: {
    property: function (params: IFeature['params']): boolean {
      if (!params.property) {
        return false
      }

      try {
        let context = window

        if (params.context) {
          let currentContext: unknown = window
          const parts = params.context.split('.')

          for (let i = 0; i < parts.length; i++) {
            const part = parts[i]

            if (currentContext && typeof currentContext === 'object') {
              currentContext = (currentContext as Record<string, unknown>)[part]
            } else {
              currentContext = undefined
            }
          }

          context = currentContext as typeof window
        }

        return params.property in context && context[params.property as keyof typeof context] !== undefined
      } catch (error) {
        return false
      }
    },
    ctor: function (params: IFeature['params']): boolean {
      if (!params.class) {
        return false
      }

      try {
        const args = [null].concat(params.args || []) as [anyType, ...anyType[]]
        const Ctor = window[params.class as keyof Window] as new (...args: anyType[]) => object
        const BoundCtor = Function.prototype.bind.apply(Ctor, args) as unknown as new () => object

        const instance = new BoundCtor()
        return instance !== undefined && (typeof instance === 'object' || typeof instance === 'function')
      } catch (error) {
        return false
      }
    }
  },
  CSS: {
    property: function (params: IFeature['params']): boolean {
      if (!params.property || !params.value) {
        return false
      }

      return cssSupports(params.property, params.value)
    }
  },
  IMAGE: {
    format: function (params: IFeature['params'], callback: (supported: boolean) => void) {
      if (!params.base64) {
        callback(false)
        return
      }

      const img = new Image()

      img.onload = function () {
        callback(true)
      }

      img.onerror = function () {
        callback(false)
      }

      img.src = params.base64
    }
  }
}

function getRemoteFeatures(url: string, callback: (features: IFeature[]) => void) {
  request({
    url: url,
    data: {},
    retries: 0,
    method: 'GET',
    callback: function (err: Error | null, responseText?: string) {
      if (err || !responseText) {
        return callback([])
      }

      try {
        callback(JSON.parse(responseText) as IFeature[])
      } catch (error) {
        return callback([])
      }
    }
  })
}

function runDetection(features: IFeature[], callback: (features: IFeatureResult[]) => void) {
  const syncFeatureResults: IFeatureResult[] = features
    .filter(function (feature) {
      return !feature.skip && !feature.async && feature.name && feature.type && feature.strategy && feature.params
    })
    .map(function (feature) {
      const detectType = detectionStrategies[feature.type] as Record<string, (params: IFeature['params']) => boolean>
      const detectStrategy = detectType[feature.strategy]

      return {
        name: feature.name,
        type: feature.type,
        supported: detectStrategy(feature.params)
      }
    })

  const asyncFeatures = features.filter(function (feature) {
    return !feature.skip && feature.async && feature.name && feature.type && feature.strategy && feature.params
  })

  /* istanbul ignore if -- @preserve */
  if (asyncFeatures.length === 0) {
    callback(syncFeatureResults)
    return
  }

  let asyncFeaturesCompletedCount = 0
  const asyncFeatureResults: IFeatureResult[] = []

  asyncFeatures.forEach(function (feature) {
    const detectType = detectionStrategies[feature.type] as Record<string, (params: IFeature['params'], callback: (supported: boolean) => void) => void>
    const detectStrategy = detectType[feature.strategy]

    detectStrategy(feature.params, function (supported: boolean) {
      asyncFeatureResults.push({
        name: feature.name,
        type: feature.type,
        supported: supported
      })

      asyncFeaturesCompletedCount++

      if (asyncFeaturesCompletedCount === asyncFeatures.length) {
        callback(syncFeatureResults.concat(asyncFeatureResults))
      }
    })
  })
}

function builtinFeatures(): IFeature[] {
  return [
    {
      type: 'API',
      name: 'promise',
      strategy: 'property',
      params: { property: 'Promise' }
    },
    {
      type: 'API',
      name: 'fetch',
      strategy: 'property',
      params: { property: 'fetch' }
    },
    {
      type: 'API',
      name: 'performance',
      strategy: 'property',
      params: { property: 'performance' }
    },
    {
      type: 'API',
      name: 'requestAnimationFrame',
      strategy: 'property',
      params: { property: 'requestAnimationFrame' }
    },
    {
      type: 'API',
      name: 'MutationObserver',
      strategy: 'property',
      params: { property: 'MutationObserver' }
    },
    {
      type: 'API',
      name: 'Blob',
      strategy: 'ctor',
      params: {
        class: 'Blob',
        args: [[''], { type: 'text/plain' }]
      }
    },
    {
      type: 'API',
      name: 'IntersectionObserver',
      strategy: 'property',
      params: { property: 'IntersectionObserver' }
    },
    {
      type: 'API',
      name: 'ResizeObserver',
      strategy: 'property',
      params: { property: 'ResizeObserver' }
    },
    {
      type: 'API',
      name: 'serviceWorker',
      strategy: 'property',
      params: {
        context: 'navigator',
        property: 'serviceWorker'
      }
    },
    {
      type: 'API',
      name: 'cryptoSubtleDigest',
      strategy: 'property',
      params: {
        context: 'crypto.subtle',
        property: 'digest'
      }
    },
    {
      type: 'API',
      name: 'WebAssembly',
      strategy: 'property',
      params: {
        property: 'WebAssembly'
      }
    },
    {
      type: 'API',
      name: 'URL',
      strategy: 'ctor',
      params: {
        class: 'URL',
        args: ['https://example.com']
      }
    },
    {
      type: 'API',
      name: 'URLSearchParams',
      strategy: 'ctor',
      params: {
        class: 'URLSearchParams',
        args: ['https://example.com']
      }
    },
    {
      type: 'API',
      name: 'arrowFunction',
      strategy: 'ctor',
      params: {
        class: 'Function',
        args: ['return () => {}']
      }
    },
    {
      type: 'CSS',
      name: 'grid',
      strategy: 'property',
      params: { property: 'display', value: 'grid' }
    },
    {
      type: 'IMAGE',
      name: 'webp',
      async: true,
      strategy: 'format',
      params: { base64: 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==' }
    }
  ]
}
