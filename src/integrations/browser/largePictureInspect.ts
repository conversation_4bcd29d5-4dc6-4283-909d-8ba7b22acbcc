import Aegis from '../../core/Aegis'
import type { Integration } from '../../core/Aegis'
import htmlTreeStringify from '../../utils/browser/htmlTreeStringify'
import { createCleanupTask, reportSDKErrors, checkIsIgnored } from '../../utils'
import { requestIdleCallback, cancelIdleCallback, toArray } from '../../utils/polyfills'
import { attachPageLoadEvent, parseBackgroundImageUrls } from '../../utils/browser/utils'

interface IImageMetrics {
  bg: boolean
  url: string
  fileSize: number
  el: HTMLElement
  memorySize: number
}

interface IBackgroundImageRule {
  url: string
  selector: string
}

export type LargePictureInspectReport = {
  type: 'LPI'
  immediate?: boolean
  payload: {
    data: {
      bg: boolean
      url: string
      path: string
      size: string
      fileSize: number
      memorySize: number
    }[]
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    largePictureInspect(isReport?: boolean): IImageMetrics[] | void
    report<P extends LargePictureInspectReport>(report: P): void
  }
}

export type ignoreUrls = (string | RegExp)[]

export interface largePictureInspectOption extends Record<string, unknown> {
  dpr?: number
  ignoreUrls?: ignoreUrls
  reportMode?: 'manual' | 'auto'
}

let cachedBackgroundImageRules: IBackgroundImageRule[] = []

export default function (options?: largePictureInspectOption): Integration {
  options = options || {}

  let idleCallbackId = 0
  const dpr = options.dpr || 3 // 3x for picture quality
  const cleanupTasks = createCleanupTask()
  const ignoreUrls = options.ignoreUrls || []
  const reportMode = options.reportMode || 'auto'

  return {
    name: 'LPI',
    options: options,
    setup: function (aegis: Aegis) {
      if (!supportsPerformance()) {
        aegis.provide('largePictureInspect', function () {
          return []
        })

        return reportSDKErrors(new Error('performance or performance.getEntriesByType is not supported'))
      }

      aegis.provide('largePictureInspect', function (isReport?: boolean) {
        if (isReport) {
          return reportPageLoadedLargePictures(aegis, ignoreUrls, dpr)
        } else {
          return reportPageLoadedLargePictures(null, ignoreUrls, dpr)
        }
      })

      if (reportMode === 'manual') {
        return
      }

      cleanupTasks.add(
        attachPageLoadEvent(function () {
          idleCallbackId = <number>requestIdleCallback(function () {
            reportPageLoadedLargePictures(aegis, ignoreUrls, dpr)
          })

          if (supportsPerformanceObserver()) {
            cleanupTasks.add(scheduleLargePictureInspect(aegis, ignoreUrls, dpr))
          } else {
            reportSDKErrors(new Error('PerformanceObserver is not supported'))
          }
        })
      )
    },
    tearDown: function () {
      cleanupTasks.dispose()
      cancelIdleCallback(idleCallbackId)
      cachedBackgroundImageRules = []
    }
  }
}

function reportPageLoadedLargePictures(aegis: Aegis | null, ignoreUrls: ignoreUrls, dpr: number) {
  const resources = filterResource(performance.getEntriesByType('resource'), ignoreUrls)
  const largePictures = getLargePictures(resources, dpr)

  if (!aegis) {
    return largePictures
  }

  if (largePictures.length) {
    reportLargePictures(aegis, largePictures)
  }
}

function scheduleLargePictureInspect(aegis: Aegis, ignoreUrls: ignoreUrls, dpr: number) {
  const observer = new PerformanceObserver(function (list: PerformanceObserverEntryList) {
    let resources = list.getEntries().filter(function (entry) {
      return entry.entryType === 'resource'
    }) as PerformanceResourceTiming[]

    resources = filterResource(resources, ignoreUrls)
    const largePictures = getLargePictures(resources, dpr)

    if (largePictures.length) {
      reportLargePictures(aegis, largePictures)
    }
  })

  observer.observe({ entryTypes: ['resource'] })

  return function () {
    observer.disconnect()
  }
}

function getLargePictures(resources: PerformanceResourceTiming[], dpr: number) {
  const metrics = getImageMetrics(resources.map(normalizeResource), dpr)

  return metrics.filter(function (metric) {
    return metric.fileSize > metric.memorySize
  })
}

function reportLargePictures(aegis: Aegis, largePictures: IImageMetrics[]) {
  aegis.report({
    type: 'LPI',
    payload: {
      data: largePictures.map(function (metric) {
        const size = getElementSize(metric.el)
        const path = htmlTreeStringify(metric.el)

        return {
          path: path,
          bg: metric.bg,
          url: metric.url,
          fileSize: metric.fileSize,
          memorySize: metric.memorySize,
          size: size.width + 'x' + size.height
        }
      })
    }
  })
}

function supportsQuerySelectorAll() {
  return !!document.querySelectorAll
}

function supportsPerformance() {
  return !!(window.performance && window.performance.getEntriesByType)
}

function supportsPerformanceObserver() {
  return 'PerformanceObserver' in window && typeof PerformanceObserver === 'function'
}

function filterResource(resources: PerformanceResourceTiming[], ignoreUrls: ignoreUrls) {
  return resources.filter(function (resource) {
    const initiatorType = resource.initiatorType

    return resource.name && (initiatorType === 'img' || initiatorType === 'css') && isImageResource(resource.name) && !checkIsIgnored(ignoreUrls, resource.name)
  })
}

function isImageResource(url: string) {
  return ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.avif', '.apng'].some(function (ext) {
    return url.toLowerCase().indexOf(ext) > -1
  })
}

function normalizeResource(resource: PerformanceResourceTiming) {
  return {
    url: resource.name,
    fileSize: resource.decodedBodySize || 0
  }
}

function getImageMetrics(resources: { url: string; fileSize: number }[], dpr: number) {
  if (!supportsQuerySelectorAll()) {
    return []
  }

  const metrics: IImageMetrics[] = []

  for (let i = 0; i < resources.length; i++) {
    const resource = resources[i]

    try {
      const elements = findImageElements(resource.url)

      if (elements.length && resource.fileSize) {
        for (let j = 0; j < elements.length; j++) {
          const element = elements[j]

          metrics.push({
            bg: element.bg,
            el: element.ref,
            url: resource.url,
            fileSize: resource.fileSize,
            memorySize: getImageMemorySize(element.ref, resource.url.toLowerCase()) * dpr
          })
        }
      }
    } catch (e) {
      /* empty */
    }
  }

  return metrics
}

function findImageElements(url: string): { ref: HTMLElement; bg: boolean }[] {
  const elements = document.querySelectorAll('img[src="' + url + '"]')

  if (elements.length) {
    return toArray(elements).map(function (el) {
      return { ref: el as HTMLElement, bg: false }
    })
  }

  const inlineRules = findInlineRules(url)

  if (inlineRules.length) {
    return inlineRules.map(function (el) {
      return { ref: el as HTMLElement, bg: true }
    })
  }

  const cssRules = findCssRules(url)

  if (cssRules.length) {
    return cssRules
      .map(function (rule) {
        const element = document.querySelector(rule.selector)

        return element ? { ref: element as HTMLElement, bg: true } : null
      })
      .filter(Boolean) as { ref: HTMLElement; bg: boolean }[]
  }

  return []
}

function findInlineRules(url: string) {
  const baseURI = getBaseURI()
  const elements = document.querySelectorAll('[style*="background"], [style*="background-image"]')

  if (!elements.length) {
    return []
  }

  const eles: Element[] = []

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i]
    const urls = parseBackgroundImageUrls(window.getComputedStyle(element).backgroundImage, baseURI)

    urls.forEach(function (absoluteUrl) {
      if (absoluteUrl === url) {
        eles.push(element)
      }
    })
  }

  return eles
}

function findCssRules(url: string) {
  if (cachedBackgroundImageRules.length) {
    return cachedBackgroundImageRules.filter(function (rule) {
      return rule.url === url
    })
  }

  const rules = getBackgroundImageRules()

  return rules.filter(function (rule) {
    return rule.url === url
  })
}

function getBackgroundImageRules() {
  const baseURI = getBaseURI()
  const rules: IBackgroundImageRule[] = []

  for (let i = 0; i < document.styleSheets.length; i++) {
    const styleSheet = document.styleSheets[i]
    const styleSheetUrl = styleSheet.href || ''

    try {
      const cssRules = styleSheet.cssRules || styleSheet.rules || []

      for (let j = 0; j < cssRules.length; j++) {
        const rule = cssRules[j] as CSSStyleRule

        if (rule.style && rule.style.backgroundImage) {
          const urls = parseBackgroundImageUrls(rule.style.backgroundImage, styleSheetUrl || baseURI)

          urls.forEach(function (url) {
            rules.push({
              url: url,
              selector: rule.selectorText
            })
          })
        }
      }
    } catch (error) {
      reportSDKErrors(new Error('[Style cross-domain error]: ' + styleSheetUrl))
    }
  }

  cachedBackgroundImageRules = rules

  return rules
}

function getImageMemorySize(element: HTMLElement, url: string) {
  const isJPEG = url.indexOf('.jpg') > -1 || url.indexOf('.jpeg') > -1
  const bytesPerPixel = isJPEG ? 3 : 4
  const size = getElementSize(element)

  return size.width * size.height * bytesPerPixel
}

function getElementSize(element: HTMLElement) {
  return {
    width: parseInt(window.getComputedStyle(element).width, 10),
    height: parseInt(window.getComputedStyle(element).height, 10)
  }
}

function getBaseURI() {
  return document.baseURI || window.location.href
}
