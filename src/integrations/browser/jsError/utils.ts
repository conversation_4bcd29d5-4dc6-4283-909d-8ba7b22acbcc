import { now } from '../../../utils'
import json from '../../../utils/json'
import { stackParser } from './stack-parsers'
import type { StackFrame } from './stack-parsers'
import { assign } from '../../../utils/polyfills'
import { isObject, isString } from '../../../utils/is'

export type Exception = Error | ErrorEvent | PromiseRejectionEvent | CustomEvent<{ reason: unknown }>

export interface JSErrorReport {
  type: 'js_error'
  immediate?: boolean
  payload: {
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#error_types
    type: string
    value: string
    stack?: string
    timestamp: number
    stacktrace?: StackFrame[]
    cate: 'error' | 'promise'
  }
}

// export interface OriginalMethod {
//   (...args: anyType[]): anyType
//   __aop__?: OriginalMethod
// }

export function isJsError(event: ErrorEvent) {
  if (!event) {
    return false
  }

  const target = event.target || event.srcElement

  return !target || target === window
}

export function isErrorEvent(value: unknown): value is ErrorEvent {
  return value instanceof ErrorEvent
}

export function isError(value: unknown): value is Error {
  return value instanceof Error
}

export function isPromiseRejectionEvent(value: unknown): value is PromiseRejectionEvent {
  return value instanceof PromiseRejectionEvent || value instanceof CustomEvent
}

export function normalizeException(exception: Exception, timestamp?: number): JSErrorReport['payload'] {
  timestamp = timestamp || now()

  if (isErrorEvent(exception)) {
    return normalizeErrorEvent(exception, timestamp)
  } else if (isPromiseRejectionEvent(exception)) {
    return normalizeRejectionError(exception, timestamp)
  } else if (isError(exception)) {
    return normalizeError(exception, timestamp)
  } else {
    return {
      cate: 'error',
      type: 'Error',
      timestamp: timestamp,
      value: isString(exception) ? exception : json.stringify(exception)
    }
  }
}

function normalizeError(event: Error, timestamp: number): JSErrorReport['payload'] {
  return {
    cate: 'error',
    stack: event.stack,
    timestamp: timestamp,
    type: event.name || 'Error',
    value: extractMessage(event),
    stacktrace: stackParser(event.stack || '')
  }
}

function normalizeErrorEvent(event: ErrorEvent, timestamp: number): JSErrorReport['payload'] {
  if (isError(event.error)) {
    return normalizeError(event.error, timestamp)
  } else {
    return {
      cate: 'error',
      type: 'Error',
      timestamp: timestamp,
      value: event.message
    }
  }
}

function normalizeRejectionError(event: PromiseRejectionEvent | CustomEvent<{ reason: unknown }>, timestamp: number): JSErrorReport['payload'] {
  let reason: unknown
  let msg = 'Unknown error'

  if ('detail' in event && 'reason' in event.detail) {
    reason = event.detail.reason
  } else if ('reason' in event && event.reason) {
    reason = event.reason
  }

  if (reason && isString(reason)) {
    msg = reason
  }

  if (reason && isObject(reason)) {
    msg = json.stringify(reason)
  }

  return reason && isError(reason)
    ? assign({}, normalizeError(reason, timestamp), { cate: 'promise' })
    : {
        cate: 'promise',
        timestamp: timestamp,
        value: msg.toString(),
        type: 'UnhandledPromiseRejection'
      }
}

export function dedupe() {
  let previousError: JSErrorReport['payload'] | undefined

  function shouldDropEvent(current?: JSErrorReport['payload'], previous?: JSErrorReport['payload']) {
    return current && previous && current.type === previous.type && current.value === previous.value && current.stack === previous.stack
  }

  return function (current: JSErrorReport['payload']) {
    if (shouldDropEvent(current, previousError)) {
      previousError = current
      return
    }

    previousError = current
    return current
  }
}

export function extractMessage(ex: Error & { message: { error?: Error } }): string {
  const message = ex && ex.message
  if (!message) {
    return 'No error message'
  }
  if (message.error && typeof message.error.message === 'string') {
    return message.error.message
  }
  return message
}

// function aopHook<T, K extends keyof T>(obj: T, key: K, hookFunc: (originalMethod: T[K], ...args: anyType) => anyType) {
//   const original = obj[key]
//   // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
//   obj[key] = hookFunc(obj[key])
//   return function () {
//     obj[key] = original
//   }
// }

// export function hookeGlobalAsyncDangerous() {
//   let restoreFns: ((...args: anyType[]) => void)[] = []
//   const DEFAULT_TIME_FUNCTION = ['setTimeout', 'setInterval', 'requestAnimationFrame', 'requestIdleCallback'] as const
//   const XML_HTTP_REQUEST_PROPS = ['onload', 'onerror', 'onprogress', 'onreadystatechange'] as const
//   const DEFAULT_EVENT_TARGET = [
//     'EventTarget',
//     'Window',
//     'Node',
//     // 'ApplicationCache', // not in window
//     'ChannelMergerNode',
//     'EventSource',
//     'FileReader',
//     'HTMLUnknownElement',
//     'IDBDatabase',
//     'IDBRequest',
//     'IDBTransaction',
//     'MessagePort',
//     'Notification',
//     // 'SVGElementInstance', // not in window
//     // 'Screen', // no addEventListener
//     'TextTrack',
//     'TextTrackCue',
//     'TextTrackList',
//     'WebSocket',
//     'Worker',
//     'XMLHttpRequest',
//     'XMLHttpRequestEventTarget',
//     'XMLHttpRequestUpload'
//   ] as const
//   const ADD_EVENT_LISTENER = 'addEventListener'
//   const REMOVE_EVENT_LISTENER = 'removeEventListener'

//   const WRAP_FLAG = '__aop__'

//   const wrap = function <T extends OriginalMethod>(origin: T | null): OriginalMethod | null {
//     if (!origin || !isFunction(origin)) return origin
//     if (Object.prototype.hasOwnProperty.call(origin, WRAP_FLAG)) return origin
//     const wrapped = function (...args: anyType[]): anyType {
//       // eslint-disable-next-line no-useless-catch
//       try {
//         return origin.apply(origin, args)
//       } catch (error) {
//         throw error
//       }
//     }
//     origin[WRAP_FLAG] = wrapped
//     return wrapped
//   }

//   DEFAULT_TIME_FUNCTION.forEach(function (timeFunc) {
//     if (window[timeFunc]) {
//       const restoreTime = aopHook(window, timeFunc, function (origin) {
//         return function (handler: Parameters<typeof origin>[0]) {
//           const params = arguments.length > 1 ? Array.prototype.slice.call(arguments, 1) : []
//           // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
//           return origin && origin.call.apply(origin, [this, wrap(handler), ...params])
//         }
//       })
//       restoreFns.push(restoreTime)
//     }
//   })

//   const restoreXHR = aopHook(XMLHttpRequest.prototype, 'send', function (origin) {
//     return function () {
//       const _this = this as XMLHttpRequest
//       const params = arguments.length > 1 ? Array.prototype.slice.call(arguments, 1) : []
//       XML_HTTP_REQUEST_PROPS.forEach(function (prop) {
//         _this[prop] && aopHook(_this, prop, wrap)
//       })
//       return origin.apply(this, params)
//     }
//   })
//   restoreFns.push(restoreXHR)

//   DEFAULT_EVENT_TARGET.forEach(function (target) {
//     const proto = window[target] && window[target].prototype
//     if (!proto || !proto[ADD_EVENT_LISTENER]) {
//       return
//     }
//     restoreFns.push(
//       aopHook(proto, ADD_EVENT_LISTENER, function (origin) {
//         return function (eventName: typeof target, fn: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
//           const cb: EventListener = typeof fn === 'function' ? fn : fn.handleEvent.bind(fn)

//           return origin && origin.call(this, eventName, wrap(cb), options)
//         }
//       })
//     )
//     restoreFns.push(
//       aopHook(proto, REMOVE_EVENT_LISTENER, function (origin) {
//         return function (eventName: typeof target, fn: OriginalMethod, options?: boolean | AddEventListenerOptions) {
//           if (fn === null || fn === void 0 ? void 0 : fn[WRAP_FLAG]) {
//             origin.call(this, eventName, fn[WRAP_FLAG], options)
//           }
//           return origin.call(this, eventName, fn, options)
//         }
//       })
//     )
//   })

//   return function () {
//     restoreFns.forEach(function (fn) {
//       fn && fn()
//     })
//     restoreFns = []
//   }
// }
