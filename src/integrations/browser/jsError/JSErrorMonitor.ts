import Aegis from '../../../core/Aegis'
import { assign } from '../../../utils/polyfills'
import type { Exception, JSErrorReport } from './utils'
import { getRegexp, createCleanupTask } from '../../../utils'
import { dedupe, normalizeException, isJsError } from './utils'
import { attachEvent, detachEvent } from '../../../utils/browser/utils'

export interface JsErrorOptions extends Record<string, unknown> {
  dedupe?: boolean
  logErrorsInConsole?: boolean
  ignoreErrors?: (string | RegExp)[]
}

export class JSErrorMonitor {
  private __dedupe = true
  private __destroyed = false
  private __dedupeFn = dedupe()
  private __aegis: Aegis | undefined
  private __cleanupTasks = createCleanupTask()
  private __ignoreErrors: RegExp | null = null

  constructor(options?: JsErrorOptions) {
    options = options || {}

    this.__ignoreErrors = getRegexp(options.ignoreErrors || [])
    this.__dedupe = 'dedupe' in options ? options.dedupe! : true

    const logErrorsInConsole = 'logErrorsInConsole' in options ? options.logErrorsInConsole : true

    if (!logErrorsInConsole) {
      this.__stopConsolePropagation()
    }
  }

  /**
   * By default, js error messages are output in the devtools console, this method stops that behavior
   */
  private __stopConsolePropagation() {
    window.onerror = function () {
      return true
    }
  }

  private __attachJSErrorListener() {
    const handler = function (this: JSErrorMonitor, event: ErrorEvent) {
      if (isJsError(event)) {
        this.__sendError(normalizeException(event))
      }
    }.bind(this)

    attachEvent('error', handler, true)

    this.__cleanupTasks.add(function () {
      detachEvent('error', handler, true)
    })
  }

  private __attachPromiseRejectionErrorListener() {
    const handler = function (this: JSErrorMonitor, event: PromiseRejectionEvent) {
      this.__sendError(normalizeException(event))
    }.bind(this)

    attachEvent('unhandledrejection', handler, true)

    this.__cleanupTasks.add(function () {
      detachEvent('unhandledrejection', handler, true)
    })
  }

  private __sendError(payload: JSErrorReport['payload']) {
    const error = this.__dedupe ? this.__dedupeFn(payload) : payload

    if (!this.__aegis || !error || (this.__ignoreErrors && this.__ignoreErrors.test(error.value))) {
      return
    }

    // No need to send the stack string to the server
    const errorData: JSErrorReport['payload'] = assign({}, error)
    delete errorData.stack

    this.__aegis.report({
      type: 'js_error',
      payload: errorData
    })
  }

  init(aegis: Aegis) {
    this.__aegis = aegis
    this.__attachJSErrorListener()
    this.__attachPromiseRejectionErrorListener()
  }

  destroy() {
    this.__destroyed = true
    this.__aegis = undefined
    this.__cleanupTasks.dispose()
  }

  captureException(exception: Exception, timestamp?: number) {
    if (!this.__destroyed && isJsError(<ErrorEvent>exception)) {
      this.__sendError(normalizeException(exception, timestamp))
    }
  }
}
