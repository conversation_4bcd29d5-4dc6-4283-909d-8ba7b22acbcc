export interface StackFrame {
  colno?: number
  lineno?: number
  filename?: string
  function?: string
}

type StackLineParser = [number, StackLineParserFn]
type StackLineParserFn = (line: string) => StackFrame | undefined
type StackParser = (stack: string, skipFirstLines?: number, framesToPop?: number) => StackFrame[]

const UNKNOWN_FUNCTION = '?'
const CHROME_PRIORITY = 30
const GECKO_PRIORITY = 50

function createFrame(filename: string, func: string, lineno?: number, colno?: number): StackFrame {
  const frame: StackFrame = {
    filename,
    function: func === '<anonymous>' ? UNKNOWN_FUNCTION : func
  }

  if (lineno !== undefined) {
    frame.lineno = lineno
  }

  if (colno !== undefined) {
    frame.colno = colno
  }

  return frame
}

// This regex matches frames that have no function name (ie. are at the top level of a module).
// Frames _with_ function names usually look as follows: "at commitLayoutEffects (react-dom.development.js:23426:1)"
const chromeRegexNoFnName = /^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i

// This regex matches all the frames that have a function name.
const chromeRegex = /^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i

const chromeEvalRegex = /\((\S*)(?::(\d+))(?::(\d+))\)/

// Chromium based browsers: Chrome, Brave, new Opera, new Edge
// We cannot call this variable `chrome` because it can conflict with global `chrome` variable in certain environments
const chromeStackParserFn: StackLineParserFn = function (line) {
  // If the stack line has no function name, we need to parse it differently
  const noFnParts = chromeRegexNoFnName.exec(line) as null | [string, string, string, string]

  if (noFnParts) {
    const filename = noFnParts[1]
    const lineno = noFnParts[2]
    const colno = noFnParts[3]

    return createFrame(filename, UNKNOWN_FUNCTION, +lineno, +colno)
  }

  const parts = chromeRegex.exec(line) as null | [string, string, string, string, string]

  if (parts) {
    const isEval = parts[2] && parts[2].indexOf('eval') === 0 // start of line

    if (isEval) {
      const subMatch = chromeEvalRegex.exec(parts[2]) as null | [string, string, string, string]

      if (subMatch) {
        // throw out eval line/column and use top-most line/column number
        parts[2] = subMatch[1] // url
        parts[3] = subMatch[2] // line
        parts[4] = subMatch[3] // column
      }
    }

    // Kamil: One more hack won't hurt us right? Understanding and adding more rules on top of these regexps right now
    // would be way too time consuming. (TODO: Rewrite whole RegExp to be more readable)
    const details = extractSafariExtensionDetails(parts[1] || UNKNOWN_FUNCTION, parts[2])

    return createFrame(details[1], details[0], parts[3] ? +parts[3] : undefined, parts[4] ? +parts[4] : undefined)
  }

  return
}

const chromeStackLineParser: StackLineParser = [CHROME_PRIORITY, chromeStackParserFn]

// gecko regex: `(?:bundle|\d+\.js)`: `bundle` is for react native, `\d+\.js` also but specifically for ram bundles because it
// generates filenames without a prefix like `file://` the filenames in the stacktrace are just 42.js
// We need this specific case for now because we want no other regex to match.
const geckoREgex = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i
const geckoEvalRegex = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i

const gecko: StackLineParserFn = function (line) {
  const parts = geckoREgex.exec(line) as null | [string, string, string, string, string, string]

  if (parts) {
    const isEval = parts[3] && parts[3].indexOf(' > eval') > -1
    if (isEval) {
      const subMatch = geckoEvalRegex.exec(parts[3]) as null | [string, string, string]

      if (subMatch) {
        // throw out eval line/column and use top-most line number
        parts[1] = parts[1] || 'eval'
        parts[3] = subMatch[1]
        parts[4] = subMatch[2]
        parts[5] = '' // no column when eval
      }
    }

    const details = extractSafariExtensionDetails(parts[1] || UNKNOWN_FUNCTION, parts[3])

    return createFrame(details[1], details[0], parts[4] ? +parts[4] : undefined, parts[5] ? +parts[5] : undefined)
  }

  return
}

const geckoStackLineParser: StackLineParser = [GECKO_PRIORITY, gecko]

/**
 * Safari web extensions, starting version unknown, can produce "frames-only" stacktraces.
 * What it means, is that instead of format like:
 *
 * Error: wat
 *   at function@url:row:col
 *   at function@url:row:col
 *   at function@url:row:col
 *
 * it produces something like:
 *
 *   function@url:row:col
 *   function@url:row:col
 *   function@url:row:col
 *
 * Because of that, it won't be captured by `chrome` RegExp and will fall into `Gecko` branch.
 * This function is extracted so that we can use it in both places without duplicating the logic.
 * Unfortunately "just" changing RegExp is too complicated now and making it pass all tests
 * and fix this case seems like an impossible, or at least way too time-consuming task.
 */
const extractSafariExtensionDetails = function (func: string, filename: string): [string, string] {
  const isSafariExtension = func.indexOf('safari-extension') !== -1
  const isSafariWebExtension = func.indexOf('safari-web-extension') !== -1

  return isSafariExtension || isSafariWebExtension
    ? [func.indexOf('@') !== -1 ? func.split('@')[0] : UNKNOWN_FUNCTION, isSafariExtension ? `safari-extension:${filename}` : `safari-web-extension:${filename}`]
    : [func, filename]
}

const STACKTRACE_FRAME_LIMIT = 50
// Used to sanitize webpack (error: *) wrapped stack errors
const WEBPACK_ERROR_REGEXP = /\(error: (.*)\)/

/**
 * Creates a stack parser with the supplied line parsers
 *
 * StackFrames are returned in the correct order for Sentry Exception
 * frames and with Sentry SDK internal frames removed from the top and bottom
 *
 */
function createStackParser(parsers: StackLineParser[]): StackParser {
  const sortedParsers = parsers
    .sort(function (a, b) {
      return a[0] - b[0]
    })
    .map(function (p) {
      return p[1]
    })

  return function (stack: string, skipFirstLines: number = 0, framesToPop: number = 0): StackFrame[] {
    const frames: StackFrame[] = []
    const lines = stack.split('\n')

    for (let i = skipFirstLines; i < lines.length; i++) {
      const line = lines[i]
      // Ignore lines over 1kb as they are unlikely to be stack frames.
      // Many of the regular expressions use backtracking which results in run time that increases exponentially with
      if (line.length > 1024) {
        continue
      }

      // Remove webpack (error: *) wrappers
      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line

      // Skip Error: lines
      if (cleanedLine.match(/\S*Error: /)) {
        continue
      }

      for (const parser of sortedParsers) {
        const frame = parser(cleanedLine)

        if (frame) {
          frames.push(frame)
          break
        }
      }

      if (frames.length >= STACKTRACE_FRAME_LIMIT + framesToPop) {
        break
      }
    }

    return frames.slice(framesToPop).reverse()
  }
}

export const stackParser = createStackParser([chromeStackLineParser, geckoStackLineParser])
