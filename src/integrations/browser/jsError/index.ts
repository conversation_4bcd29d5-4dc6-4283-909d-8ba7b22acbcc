import Aegis from '../../../core/Aegis'
import { JSErrorReport } from './utils'
import type { Exception } from './utils'
import { JSErrorMonitor } from './JSErrorMonitor'
import { createCleanupTask } from '../../../utils'
import type { JsErrorOptions } from './JSErrorMonitor'
import type { Integration } from '../../../core/Aegis'
import { detectVueFramework } from '../../../utils/browser/utils'

declare module '../../../core/Aegis' {
  export default interface Aegis {
    captureException(exception: Exception, timestamp?: number): void
    report<P extends JSErrorReport>(report: P): void
  }
}

export default function jsError(options?: JsErrorOptions & { vueCheck?: boolean }): Integration {
  const cleanupTasks = createCleanupTask()
  let jsErrorMonitor: JSErrorMonitor | null = new JSErrorMonitor(options)

  return {
    name: 'js_error',
    options: options,
    setup(aegis: Aegis) {
      const vueCheck = options && 'vueCheck' in options ? options.vueCheck : true

      jsErrorMonitor!.init(aegis)
      aegis.provide('captureException', jsErrorMonitor!.captureException.bind(jsErrorMonitor))

      /**
       * The Vue framework needs to use its `errorHandler` configuration to report Vue errors.
       */
      if (vueCheck) {
        cleanupTasks.add(
          detectVueFramework(function (vue, version) {
            const message = 'Detecting that the Vue framework is being used, configure the errorHandler method to report Vue errors.'

            if (!hasVueErrorHandler(vue, version)) {
              return console.error(message)
            }

            if (!isUseAegisCaptureException(vue, version)) {
              return console.error(message)
            }
          })
        )
      }
    },
    tearDown() {
      jsErrorMonitor!.destroy()
      jsErrorMonitor = null
      cleanupTasks.dispose()
    }
  }
}

function hasVueErrorHandler(vue: Parameters<Parameters<typeof detectVueFramework>[0]>[0], version: number) {
  if (version === 2 && '$root' in vue) {
    const ctor = vue.$root!.constructor
    return ctor && ctor.config && ctor.config.errorHandler != null
  }

  if (version === 3 && 'config' in vue) {
    return vue.config!.errorHandler != null
  }

  return true
}

function isUseAegisCaptureException(vue: Parameters<Parameters<typeof detectVueFramework>[0]>[0], version: number) {
  let errorHandlerString = ''

  if (version === 2 && '$root' in vue) {
    errorHandlerString = vue.$root!.constructor!.config!.errorHandler!.toString()
  }

  if (version === 3 && 'config' in vue) {
    errorHandlerString = vue.config!.errorHandler!.toString()
  }

  return errorHandlerString ? errorHandlerString.indexOf('aegis') !== -1 && errorHandlerString.indexOf('captureException') !== -1 : true
}
