import Aegis from '../../../core/Aegis'
import type { Integration } from '../../../core/Aegis'
import { isFunction, isString } from '../../../utils/is'
import { createApiObserver } from '../../../utils/browser/apiObserver'
import { now, createCleanupTask, checkIsIgnored } from '../../../utils'
import { parseFetchArgs, urlToString, bodyToString, headersToString, isEmptyObject, splitUrl } from './utils'

type RequestReport = {
  type: 'api'
  immediate?: boolean
  payload: {
    url: string
    ok: boolean
    base: string
    query: string
    status: number
    method: string
    duration: number
    headers?: string
    requestData?: string
    responseData?: string
    requestType: 'fetch' | 'xhr'
  }
}

declare module '../../../core/Aegis' {
  export default interface Aegis {
    reportHttpRequest: (payload: RequestReport['payload']) => void
    report<P extends RequestReport>(report: P): void
  }
}

interface RequestOptions extends Record<string, unknown> {
  reportAll?: boolean
  ignoreUrls?: (string | RegExp)[]
  reportPolicy?: (response: anyType) => boolean
}

export default function (options?: RequestOptions): Integration {
  options = options || {}

  const cleanupTasks = createCleanupTask()
  const ignoreUrls = options.ignoreUrls || []
  const reportPolicy = options.reportPolicy || undefined
  const reportAll = 'reportAll' in options ? (options.reportAll as boolean) : false

  function reportApi(aegis: Aegis, payload: RequestReport['payload'], forceReport?: boolean) {
    try {
      const urls = ignoreUrls.concat(aegis.getReportUrls())
      const isBase64Url = payload.url.indexOf('data:') === 0
      const responseOk = payload.status >= 200 && payload.status < 300

      if (!isBase64Url && !checkIsIgnored(urls, payload.url) && (forceReport || !responseOk || reportAll)) {
        aegis.report({
          type: 'api',
          payload: payload
        })
      }
    } catch (e) {
      /* empty */
    }
  }

  return {
    name: 'api',
    options: options,
    setup: function (aegis: Aegis) {
      const apiObserver = createApiObserver({
        fetchInterceptor: function (original, args) {
          return interceptFetch(original, args, reportApi.bind(null, aegis), reportPolicy)
        },
        xhrInterceptor: function (this: XMLHttpRequest, original, args) {
          interceptXHR.call(this, original, args, reportApi.bind(null, aegis), reportPolicy)
        }
      })

      cleanupTasks.add(apiObserver.dispose)

      aegis.provide('reportHttpRequest', function (payload: RequestReport['payload']) {
        reportApi(aegis, payload, true)
      })
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function interceptFetch(original: typeof fetch, args: Parameters<typeof fetch>, reportCallback: (payload: RequestReport['payload']) => void, reportPolicy: RequestOptions['reportPolicy']) {
  const init = args[1]
  const input = args[0]
  const startTime = now()
  const fetchArgs = parseFetchArgs(input, init)

  return original.apply(window, args).then(
    function (response) {
      /**
       * Clone the response object to avoid a situation where the response is consumed and cannot be cloned subsequently.
       */
      const cloneResponse = response.clone()
      const urlParts = splitUrl(fetchArgs.url)
      const payload: RequestReport['payload'] = {
        ok: true,
        base: urlParts[0],
        query: urlParts[1],
        url: fetchArgs.url,
        requestType: 'fetch',
        status: response.status,
        method: fetchArgs.method,
        duration: now() - startTime
      }

      const reportErrorHandler = function () {
        payload.ok = false

        if (init && 'body' in init) {
          payload.requestData = bodyToString(init.body)
        }

        if (init && init.headers) {
          payload.headers = headersToString(init.headers)
        }

        cloneResponse
          .clone()
          .text()
          .then(function (responseText) {
            payload.responseData = responseText
            reportCallback(payload)
          })
          .catch(function (error: Error) {
            payload.responseData = error.message
            reportCallback(payload)
          })
      }

      if (response.ok && reportPolicy) {
        cloneResponse
          .clone()
          .text()
          .then(
            function (responseText) {
              if (reportPolicy(responseText)) {
                payload.status = 500
                reportErrorHandler()
              } else {
                reportCallback(payload)
              }
            },
            function () {
              /* empty */
            }
          )
      } else if (response.ok) {
        reportCallback(payload)
      } else {
        reportErrorHandler()
      }

      return response
    },
    function (error: Error) {
      const urlParts = splitUrl(fetchArgs.url)
      const payload: RequestReport['payload'] = {
        status: 0,
        ok: false,
        base: urlParts[0],
        query: urlParts[1],
        url: fetchArgs.url,
        requestType: 'fetch',
        method: fetchArgs.method,
        duration: now() - startTime,
        responseData: error.message
      }

      if (init && 'body' in init) {
        payload.requestData = bodyToString(init.body)
      }

      reportCallback(payload)

      throw error
    }
  )
}

/**
 * XMLHttpRequest onerror does not provide useful information about the error.
 * So the onerror event is not used here.
 */
function interceptXHR(
  this: XMLHttpRequest,
  original: XMLHttpRequest['open'],
  args: Parameters<XMLHttpRequest['open']>,
  reportCallback: (payload: RequestReport['payload']) => void,
  reportPolicy: RequestOptions['reportPolicy']
) {
  const url = args[1]
  const method = args[0]
  const startTime = now()

  if (!method || !url) {
    return original.apply(this, args)
  }

  this['__xhr_data__'] = {
    headers: {},
    url: urlToString(url),
    method: isString(method) ? method.toUpperCase() : undefined
  }

  const onreadystatechangeHandler = function (this: XMLHttpRequest) {
    const xhrData = this['__xhr_data__']

    if (!xhrData) {
      return
    }

    if (this.readyState === 4) {
      let responseOk = true

      try {
        xhrData.status = this.status
        responseOk = this.status >= 200 && this.status < 300
      } catch (e) {
        /* empty */
      }

      const urlParts = splitUrl(xhrData.url as string)
      const payload: RequestReport['payload'] = {
        ok: responseOk,
        base: urlParts[0],
        query: urlParts[1],
        requestType: 'xhr',
        url: xhrData.url as string,
        duration: now() - startTime,
        method: xhrData.method as string,
        status: xhrData.status as number
      }

      if (responseOk && reportPolicy && this.response && reportPolicy(this.response)) {
        payload.ok = false
        payload.status = 500
        payload.responseData = getResponseText(this)

        if (xhrData.body) {
          payload.requestData = bodyToString(xhrData.body)
        }

        if (!isEmptyObject(xhrData.headers)) {
          payload.headers = headersToString(xhrData.headers)
        }
      } else {
        if (!responseOk && xhrData.body) {
          payload.requestData = bodyToString(xhrData.body)
        }

        if (!responseOk && this.response) {
          payload.responseData = getResponseText(this)
        }

        if (!responseOk && !isEmptyObject(xhrData.headers)) {
          payload.headers = headersToString(xhrData.headers)
        }
      }

      reportCallback(payload)
    }
  }

  if ('onreadystatechange' in this && isFunction(this.onreadystatechange)) {
    const originalOnReadyStateChange = this.onreadystatechange

    this.onreadystatechange = function (ev) {
      try {
        onreadystatechangeHandler.call(this)
      } finally {
        originalOnReadyStateChange.call(this, ev)
      }
    }
  } else {
    this.addEventListener('readystatechange', onreadystatechangeHandler)
  }

  const originalSetRequestHeader = this.setRequestHeader

  this.setRequestHeader = function (header, value) {
    const xhrData = this['__xhr_data__']

    if (xhrData && isString(header) && isString(value)) {
      xhrData.headers[header.toLowerCase()] = value
    }

    originalSetRequestHeader.call(this, header, value)
  }

  original.apply(this, args)
}

function getResponseText(xhr: XMLHttpRequest) {
  if (xhr.responseType === '' || xhr.responseType === 'text') {
    return xhr.responseText
  } else if (xhr.responseType === 'json') {
    return JSON.stringify(xhr.response)
  } else {
    return '[' + xhr.responseType + ' data]'
  }
}
