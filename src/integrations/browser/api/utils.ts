import json from '../../../utils/json'
import { isArray, isString } from '../../../utils/is'

export function splitUrl(url: string) {
  if (!url || !url.trim()) {
    return ['', '']
  }

  const markIndex = url.indexOf('?')

  return markIndex === -1 ? [url, ''] : [url.slice(0, markIndex), url.slice(markIndex + 1)]
}

export function urlToString(url: string | URL): string | undefined {
  if (isString(url)) {
    return url
  }

  try {
    return url.toString()
  } catch (e) {
    /* empty */
  }

  return undefined
}

export function parseFetchArgs(input: URL | RequestInfo, init?: RequestInit) {
  if (!input && !init) {
    return { method: 'GET', url: '' }
  }

  if (input && init) {
    return {
      url: getUrlFromResource(input),
      method: 'method' in init ? String(init.method).toUpperCase() : 'GET'
    }
  }

  return {
    url: getUrlFromResource(input),
    method: hasProp(input, 'method') ? String(input.method).toUpperCase() : 'GET'
  }
}

function hasProp<T extends string>(obj: unknown, prop: T): obj is Record<string, string> {
  return !!obj && typeof obj === 'object' && !!(obj as Record<string, string>)[prop]
}

function getUrlFromResource(resource: string | { toString(): string } | { url: string }): string {
  if (typeof resource === 'string') {
    return resource
  }

  if (!resource) {
    return ''
  }

  if ('url' in resource) {
    return resource.url
  }

  if (resource.toString) {
    return resource.toString()
  }

  return ''
}

export function isEmptyObject(obj: Record<string, unknown>) {
  for (const prop in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, prop)) {
      return false
    }
  }

  return true
}

export function headersToString(headers: HeadersInit) {
  const obj: Record<string, string> = {}

  if (isHeadersInstance(headers)) {
    headers.forEach(function (value, key) {
      obj[key] = value
    })
  } else if (isArray(headers)) {
    if (headers.length) {
      headers.forEach(function (header) {
        obj[header[0]] = header[1]
      })
    } else {
      headers = {}
    }
  }

  return json.stringify(isEmptyObject(obj) ? headers : obj)
}

function isHeadersInstance(headers: unknown): headers is Headers {
  return typeof Headers !== 'undefined' && headers instanceof Headers && typeof headers.forEach === 'function'
}

export function bodyToString(body: Document | BodyInit | null | undefined): string | undefined {
  if (!body) {
    return undefined
  }

  if (typeof body === 'string') {
    return body
  } else if (body instanceof Blob) {
    return '[Blob type: ' + body.type + ', size: ' + body.size + ']'
  } else if (body instanceof FormData) {
    return '[FormData]'
  } else if (body instanceof URLSearchParams) {
    return body.toString()
  } else if (body instanceof ReadableStream) {
    return '[ReadableStream]'
  } else if (body instanceof Document) {
    return new XMLSerializer().serializeToString(body)
  } else {
    return '[Unknown Body Type]'
  }
}
