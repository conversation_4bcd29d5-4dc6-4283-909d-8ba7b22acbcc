import { now } from '../../utils'
import Aegis from '../../core/Aegis'
import type { Exception } from './jsError/utils'
import type { Integration } from '../../core/Aegis'

declare module '../../core/Aegis' {
  export default interface Aegis {
    precollect(type: 'sr' | 'js', data: EventTarget | null | Exception, timestamp?: number): void
  }
}

export default function (): Integration {
  const typeMap: {
    js: 'captureException'
    sr: 'reportResourceError'
  } = {
    js: 'captureException',
    sr: 'reportResourceError'
  }

  return {
    name: 'precollect',
    setup: function (aegis: Aegis) {
      const registrys: string[] = []

      aegis.provide('precollect', function (type: 'sr' | 'js', data: EventTarget | null | Exception, timestamp?: number) {
        const methodName = typeMap[type]

        if (registrys.indexOf(methodName) !== -1 || aegis[methodName]) {
          if (methodName === 'reportResourceError') {
            aegis[methodName](<EventTarget | null>data, timestamp || now())
          } else {
            aegis[methodName](<Exception>data, timestamp || now())
          }
        }
      })

      aegis.on('provide', function (name: string) {
        registrys.push(name)
      })
    }
  }
}
