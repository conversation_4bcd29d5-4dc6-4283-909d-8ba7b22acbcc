import Aegis from '../../core/Aegis'
import type { Integration } from '../../core/Aegis'
import { detectVueFramework } from '../../utils/browser/utils'
import { resolveURL, createCleanupTask, getViewId } from '../../utils'
import { createRouteObserver } from '../../utils/browser/routeObserver'

type Source = 'auto' | 'manually' | 'pushState' | 'replaceState' | 'hash' | 'history'

type PageviewReport = {
  type: 'pv'
  immediate?: boolean
  payload: {
    pid: string
    source: Source
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    sendPV(pid: string): void
    report<P extends PageviewReport>(report: P): void
  }
}

interface PageViewOptions extends Record<string, unknown> {
  syncCid?: boolean
  initSend?: boolean
  vueCheck?: boolean
  mode?: 'history' | 'hash'
  extractPid?: (url: string) => string
}

export default function (options?: PageViewOptions): Integration {
  options = options || {}

  const mode = options.mode || 'history'
  const syncCid = options.syncCid || false
  const cleanupTasks = createCleanupTask()
  const initSend = 'initSend' in options ? options.initSend : true
  const vueCheck = 'vueCheck' in options ? options.vueCheck : true

  function extractPageId(url: string) {
    return (options && options.extractPid && options.extractPid(url)) || extractPidFromURL(mode, url)
  }

  function reportPageView(aegis: Aegis, pid: string, source: Source) {
    aegis.setConfig({ viewId: getViewId(pid) })

    if (syncCid) {
      aegis.setConfig({ cid: pid })
    }

    aegis.report({
      type: 'pv',
      payload: {
        pid: pid,
        source: source
      }
    })
  }

  function historyChangeListener(aegis: Aegis, source: Source) {
    reportPageView(aegis, extractPageId(location.href), source)
  }

  return {
    name: 'pageview',
    options: options,
    setup: function (aegis: Aegis) {
      const routeObserver = createRouteObserver({
        mode: mode,
        onHashChange: historyChangeListener.bind(null, aegis, 'hash'),
        onPushState: historyChangeListener.bind(null, aegis, 'pushState'),
        onHistoryChange: historyChangeListener.bind(null, aegis, 'history'),
        onReplaceState: historyChangeListener.bind(null, aegis, 'replaceState')
      })

      cleanupTasks.add(routeObserver.dispose)

      aegis.provide('sendPV', function (pid: string) {
        reportPageView(aegis, toAbsoluteURL(normalize(pid), mode), 'manually')
      })

      if (initSend) {
        reportPageView(aegis, extractPageId(location.href), 'auto')

        /**
         * The Vue Router scrollBehavior option calls the `history.pushState` method, which can lead to duplicate PV data being sent.
         * 4000ms latency not detecting vue router, either the vue router is not used or the application is too slow.
         */
        if (vueCheck) {
          cleanupTasks.add(
            detectVueFramework(function (vue, version) {
              if (hasScrollBehavior(vue, version)) {
                console.error('You have set the Vue-Router scrollBehavior option, in "initSend" mode, which will result in sending more PV data, set the Vue-Router scrollBehavior to null.')
              }
            })
          )
        }
      }
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function hasScrollBehavior(vue: Parameters<Parameters<typeof detectVueFramework>[0]>[0], version: number) {
  if (version === 2 && '$router' in vue) {
    const options = vue.$router!.options

    return options && options.scrollBehavior != null
  }

  if (version === 3 && 'config' in vue) {
    const globalProperties = vue.config!.globalProperties
    const router = globalProperties && globalProperties.$router

    return router && router.options && router.options.scrollBehavior != null
  }

  return false
}

function extractPidFromURL(mode: 'history' | 'hash', url: string) {
  const parsedURL = resolveURL(url)

  if (mode === 'hash') {
    return toAbsoluteURL(parsedURL.hash ? normalize(parsedURL.hash.replace(/^#/, '').replace(/\?.*$/, '')) : '/', mode)
  } else {
    return toAbsoluteURL(normalize(parsedURL.path), mode)
  }
}

function normalize(pathname: string | undefined) {
  if (pathname && pathname[0] !== '/') {
    pathname = '/' + pathname
  }

  return pathname || '/'
}

function toAbsoluteURL(path: string, mode: 'history' | 'hash') {
  if (mode === 'hash') {
    return location.origin + location.pathname + '#' + path
  } else {
    return location.origin + path
  }
}
