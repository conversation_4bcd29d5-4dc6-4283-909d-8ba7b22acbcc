import Aegis from '../../core/Aegis'
import type { Integration } from '../../core/Aegis'
import { attachEvent, isInvalidWindow } from '../../utils/browser/utils'
import { createRouteObserver } from '../../utils/browser/routeObserver'
import { reportSDKErrors, createCleanupTask, checkIsIgnored } from '../../utils'
import { requestIdleCallback, cancelIdleCallback, assign } from '../../utils/polyfills'

export type BlankScreenReport = {
  type: 'blank'
  immediate?: boolean
  payload: {
    url: string
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends BlankScreenReport>(report: P): void
  }
}

interface BlankScreenOptions extends Record<string, unknown> {
  skeleton?: boolean
  threshold?: number
  mode?: 'history' | 'hash'
  ignoreUrls?: (string | RegExp)[]
  rootSelector?: string[] | string
}

interface Point {
  x: number
  y: number
}

interface IElement {
  empty: boolean
  point: Point
  selector: string
}

export default function (options?: BlankScreenOptions): Integration {
  options = assign(
    {
      threshold: 1,
      ignoreUrls: [],
      skeleton: false,
      rootSelector: []
    },
    options || {}
  )

  let idleCallbackId = 0
  let currentDetectionCount = 0
  const maxDetectionAttempts = 1
  let elementsArchive: IElement[][] = []
  const cleanupTasks = createCleanupTask()

  function isEmptyElement(element: IElement) {
    return options!.rootSelector!.indexOf(element.selector) >= 0
  }

  function runAsyncDetectBlankScreen(aegis: Aegis) {
    idleCallbackId = <number>requestIdleCallback(function () {
      const elements = getElements(getPoints())

      if (!elements.length) {
        return
      }

      elements.forEach(function (element) {
        if (element.selector === '' || isEmptyElement(element)) {
          element.empty = true
        }
      })

      elementsArchive.push(elements)

      if (currentDetectionCount < maxDetectionAttempts) {
        currentDetectionCount = currentDetectionCount + 1

        const shouldContinue = !options!.skeleton ? !measure(elementsArchive, false, options!.threshold!) : true

        if (shouldContinue) {
          setTimeout(function () {
            runAsyncDetectBlankScreen(aegis)
          }, 2000)
        }
      } else {
        if (!measure(elementsArchive, options!.skeleton!, options!.threshold!)) {
          aegis.report({
            type: 'blank',
            payload: {
              url: location.href
            }
          })
        }
      }
    })
  }

  return {
    name: 'blankscreen',
    options: options,
    setup: function (aegis: Aegis) {
      const boundDetectBlankScreenListener = function () {
        if (checkIsIgnored(options!.ignoreUrls!, location.href)) {
          return
        }

        elementsArchive = []
        currentDetectionCount = 0
        runAsyncDetectBlankScreen(aegis)
      }

      const getMode = function () {
        if (options && options.mode) {
          return options.mode
        }

        const pageviewIntegration = aegis.getIntegration('pageview')

        if (pageviewIntegration && pageviewIntegration.options && pageviewIntegration.options.mode) {
          return pageviewIntegration.options.mode as 'history' | 'hash'
        }

        return 'history'
      }

      const routeObserver = createRouteObserver({
        mode: getMode(),
        onPushState: boundDetectBlankScreenListener,
        onHashChange: boundDetectBlankScreenListener,
        onReplaceState: boundDetectBlankScreenListener,
        onHistoryChange: boundDetectBlankScreenListener
      })

      cleanupTasks.add(routeObserver.dispose)

      pageLoaded(boundDetectBlankScreenListener)
    },
    tearDown: function () {
      cleanupTasks.dispose()
      cancelIdleCallback(idleCallbackId)
    }
  }
}

function pageLoaded(callback: () => void) {
  if (document.readyState === 'complete') {
    callback()
  } else {
    attachEvent('load', callback, true)
  }
}

//
// +-------------------+
// | o       o       o | <- pointX & (height - pointY)
// |   o     o     o   |
// |     o   o   o     |
// |       o o o       |
// | o o o o o o o o o | <- pointX & (height / 2)
// |       o o o       |
// |     o   o   o     |
// |   o     o     o   |
// | o       o       o | <- pointX & pointY
// +-------------------+
//           ^------------- (width / 2) & pointY
//
function getPoints() {
  const points: Array<Point> = []
  const width = window.innerWidth
  const height = window.innerHeight

  for (let i = 1; i <= 9; i++) {
    const pointX = (width * i) / 10
    const pointY = (height * i) / 10

    points.push({ x: pointX, y: height / 2 })

    if (i === 5) {
      continue
    }

    points.push({ x: pointX, y: pointY })
    points.push({ x: width / 2, y: pointY })
    points.push({ x: pointX, y: height - pointY })
  }

  return points
}

function getElements(points: Array<Point>) {
  const elements: IElement[] = []

  if (!document.elementFromPoint) {
    reportSDKErrors(new Error('document.elementFromPoint is not supported'))

    return elements
  }

  for (let i = 0; i < points.length; i++) {
    const element = document.elementFromPoint(points[i].x, points[i].y)
    const selector = getElementSelector(element)

    elements.push({
      empty: false,
      point: points[i],
      selector: selector
    })
  }

  return elements
}

// #elementId or .className or tagName
function getElementSelector(element: Element | null) {
  if (!element || !element.tagName) {
    return ''
  }

  if (element.id) {
    return '#' + element.id
  } else if (element.className && typeof element.className === 'string') {
    return (
      '.' +
      element.className
        .split(' ')
        .filter(function (item) {
          return !!item
        })
        .join('.')
    )
  } else {
    return element.tagName.toLowerCase()
  }
}

// In non-skeleton mode, compare the ratio of empty elements to total elements against the threshold
// In skeleton mode, compare the initial and the latest snapshots of elements
function measure(elementsArchive: IElement[][], isSkeleton: boolean, threshold: number) {
  if (isInvalidWindow()) {
    return true
  }

  if (!isSkeleton) {
    const elements = elementsArchive[elementsArchive.length - 1]
    const emptyElements = elements.filter(function (element) {
      return element.empty
    })

    return emptyElements.length / elements.length < threshold
  } else {
    const initialElementSnapshot = getElementSnapshot(elementsArchive[0])
    const actualElementSnapshot = getElementSnapshot(elementsArchive[elementsArchive.length - 1])

    return !(initialElementSnapshot === actualElementSnapshot)
  }
}

function getElementSnapshot(elements: IElement[]) {
  return elements
    .map(function (element) {
      return element.selector
    })
    .join()
}
