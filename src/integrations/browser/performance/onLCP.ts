import { observe } from './lib/observe'
import { runOnce } from './lib/runOnce'
import { whenIdle } from './lib/whenIdle'
import type { LCPMetric } from './types/lcp'
import { assign } from '../../../utils/polyfills'
import { whenActivated } from './lib/whenActivated'
import { getActivationStart } from './lib/getActivationStart'
import { getNavigationEntry } from './lib/getNavigationEntry'
import htmlTreeStringify from '../../../utils/browser/htmlTreeStringify'

export const onLCP = function (onReport: (metric: LCPMetric) => void) {
  whenActivated(function () {
    let metric: LCPMetric = {
      name: 'LCP',
      value: -1,
      timeToFirstByte: 0,
      resourceLoadDelay: 0,
      resourceLoadDuration: 0,
      elementRenderDelay: -1,
      navigationType: 'navigate'
    }
    const report = runOnce(function () {
      onReport(metric)
    })
    const handleEntries = function (entries: PerformanceEntry[]) {
      if (entries.length) {
        metric.value = Math.max(entries[entries.length - 1].startTime - getActivationStart(), 0)
        const navigationEntry = getNavigationEntry()
        if (navigationEntry) {
          const activationStart = navigationEntry.activationStart || 0
          const lcpEntry = entries[entries.length - 1] as LargestContentfulPaint
          const lcpResourceEntry = lcpEntry.url
            ? performance.getEntriesByType('resource').find(function (e) {
                return e.name === lcpEntry.url
              })
            : null
          const ttfb = Math.max(0, navigationEntry.responseStart - activationStart)
          const lcpRequestStart = Math.max(
            ttfb,
            // Prefer `requestStart` (if TOA is set), otherwise use `startTime`.
            lcpResourceEntry ? (lcpResourceEntry.requestStart || lcpResourceEntry.startTime) - activationStart : 0
          )
          const lcpResponseEnd = Math.max(lcpRequestStart, lcpResourceEntry ? lcpResourceEntry.responseEnd - activationStart : 0)
          const lcpRenderTime = Math.max(lcpResponseEnd, lcpEntry.startTime - activationStart)
          metric = assign({}, metric, {
            element: htmlTreeStringify(lcpEntry.element),
            timeToFirstByte: ttfb,
            resourceLoadDelay: lcpRequestStart - ttfb,
            resourceLoadDuration: lcpResponseEnd - lcpRequestStart,
            elementRenderDelay: lcpRenderTime - lcpResponseEnd
          })
          if (lcpEntry.url) {
            metric.url = lcpEntry.url
          }
          report()
        }
      }
    }

    const po = observe('largest-contentful-paint', handleEntries)

    if (po) {
      const stopListening = runOnce(function () {
        handleEntries(po.takeRecords())
        po.disconnect()
        report()
      })
      ;['keydown', 'click'].forEach(function (type) {
        addEventListener(type, function () {
          whenIdle(stopListening)
        })
      })
    }
  })
}
