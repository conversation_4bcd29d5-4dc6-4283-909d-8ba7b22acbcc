import { observe } from './lib/observe'
import type { CLSMetric } from './types/cls'
import { assign } from '../../../utils/polyfills'
import { getLoadState } from './lib/getLoadState'
import htmlTreeStringify from '../../../utils/browser/htmlTreeStringify'

const getLargestLayoutShiftEntry = function (entries: LayoutShift[]) {
  return entries.reduce(function (a, b) {
    return a && a.value > b.value ? a : b
  })
}

const getLargestLayoutShiftSource = function (sources: LayoutShiftAttribution[]) {
  return (
    sources.find(function (s) {
      return s.node && s.node.nodeType === 1
    }) || sources[0]
  )
}

export const onCLS = function (callback: (entry: CLSMetric) => void) {
  const metric: CLSMetric = {
    name: 'CLS',
    navigationType: 'navigate',
    value: -1
  }
  let sessionValue = 0
  let sessionEntries: LayoutShift[] = []

  const po = observe('layout-shift', function (entries) {
    po && po.disconnect()

    entries.forEach(function (entry) {
      if (!entry.hadRecentInput) {
        const firstSessionEntry = sessionEntries[0]
        const lastSessionEntry = sessionEntries[sessionEntries.length - 1]

        // If the entry occurred less than 1 second after the previous entry
        // and less than 5 seconds after the first entry in the session,
        // include the entry in the current session. Otherwise, start a new
        // session.
        if (sessionValue && entry.startTime - lastSessionEntry.startTime < 1000 && entry.startTime - firstSessionEntry.startTime < 5000) {
          sessionValue += entry.value
          sessionEntries.push(entry)
        } else {
          sessionValue = entry.value
          sessionEntries = [entry]
        }
      }
    })
    if (sessionValue > metric.value) {
      metric.value = sessionValue
    }

    if (sessionEntries.length) {
      const largestEntry = getLargestLayoutShiftEntry(sessionEntries)
      if (largestEntry && largestEntry.sources && largestEntry.sources.length) {
        const largestSource = getLargestLayoutShiftSource(largestEntry.sources)
        assign(metric, {
          largestShiftTarget: htmlTreeStringify(largestSource.node),
          largestShiftTime: largestEntry.startTime,
          largestShiftValue: largestEntry.value,
          largestShiftSource: {
            node: htmlTreeStringify(largestSource.node),
            currentRect: largestSource.currentRect,
            previousRect: largestSource.previousRect
          },
          loadState: getLoadState(largestEntry.startTime)
        })
      }
    }

    callback(metric)
  })
}
