import type { TTFBMetric } from './types'
import { assign } from '../../../utils/polyfills'
import { whenActivated } from './lib/whenActivated'
import { getActivationStart } from './lib/getActivationStart'
import { getNavigationEntry } from './lib/getNavigationEntry'

const whenReady = function (callback: () => void) {
  if (document.prerendering) {
    whenActivated(function () {
      whenReady(callback)
    })
  } else if (document.readyState !== 'complete') {
    addEventListener(
      'load',
      function () {
        whenReady(callback)
      },
      true
    )
  } else {
    // Queue a task so the callback runs after `loadEventEnd`.
    setTimeout(callback, 0)
  }
}

export const onTTFB = function (callback: (entry: TTFBMetric) => void) {
  const metric: TTFBMetric = {
    name: 'TTFB',
    cacheDuration: 0,
    connectionDuration: 0,
    dnsDuration: 0,
    navigationType: 'navigate',
    requestDuration: 0,
    value: -1,
    waitingDuration: 0
  }
  const navEntry = getNavigationEntry()
  if (navEntry) {
    if (document.prerendering || getActivationStart() > 0) {
      metric.navigationType = 'prerender'
    } else if (document.wasDiscarded) {
      metric.navigationType = 'restore'
    } else if (navEntry.type) {
      metric.navigationType = navEntry.type.replace(/_/g, '-') as TTFBMetric['navigationType']
    }
  }

  whenReady(function () {
    const navigationEntry = getNavigationEntry()
    if (navigationEntry) {
      const activationStart = getActivationStart() || 0
      metric.value = Math.max(navigationEntry.responseStart - activationStart, 0)
      const waitEnd = Math.max((navigationEntry.workerStart || navigationEntry.fetchStart) - activationStart, 0)
      const dnsStart = Math.max(navigationEntry.domainLookupStart - activationStart, 0)
      const connectStart = Math.max(navigationEntry.connectStart - activationStart, 0)
      const connectEnd = Math.max(navigationEntry.connectEnd - activationStart, 0)
      assign(metric, {
        waitingDuration: waitEnd,
        cacheDuration: dnsStart - waitEnd,
        // dnsEnd usually equals connectStart but use connectStart over dnsEnd
        // for dnsDuration in case there ever is a gap.
        dnsDuration: connectStart - dnsStart,
        connectionDuration: connectEnd - connectStart,
        // There is often a gap between connectEnd and requestStart. Attribute
        // that to requestDuration so connectionDuration remains 0 for
        // service worker controlled requests were connectStart and connectEnd
        // are the same.
        requestDuration: metric.value - connectEnd
      })
    }
    callback(metric)
  })
}
