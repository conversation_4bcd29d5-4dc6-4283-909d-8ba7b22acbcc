import { observe } from './lib/observe'
import { assign } from '../../../utils/polyfills'
import { getLoadState } from './lib/getLoadState'
import { whenActivated } from './lib/whenActivated'
import type { FIDMetric, FirstInputPolyfillCallback } from './types'
import htmlTreeStringify from '../../../utils/browser/htmlTreeStringify'
import { firstInputPolyfill, resetFirstInputPolyfill } from './lib/firstInputPolyfill'

export const onFID = function (callback: (entry: FIDMetric) => void) {
  whenActivated(function () {
    const metric: FIDMetric = {
      name: 'FID',
      value: -1,
      navigationType: 'navigate',
      eventTarget: '',
      eventTime: 0,
      eventType: '',
      loadState: 'complete'
    }

    const handleEntry = function (entry: PerformanceEventTiming) {
      metric.value = entry.processingStart - entry.startTime
    }

    const po = observe('first-input', function (entries) {
      po && po.disconnect()
      entries.forEach(handleEntry)
      const fidEntry = entries[0]
      assign(metric, {
        eventTarget: htmlTreeStringify(fidEntry.target),
        eventType: fidEntry.name,
        eventTime: fidEntry.startTime,
        loadState: getLoadState(fidEntry.startTime)
      })
      callback(metric)
    })
    if (po) {
      resetFirstInputPolyfill()
      firstInputPolyfill(handleEntry as FirstInputPolyfillCallback)
    }
  })
}
