import type { LCPMetric } from './lcp'

// Update built-in types to be more accurate.
interface PerformanceEntryMap {
  navigation: PerformanceNavigationTiming
  resource: PerformanceResourceTiming
  paint: PerformancePaintTiming
}

declare global {
  interface Document {
    // https://wicg.github.io/nav-speculation/prerendering.html#document-prerendering
    prerendering?: boolean
    // https://wicg.github.io/page-lifecycle/#sec-api
    wasDiscarded?: boolean
  }

  interface Performance {
    getEntriesByType<K extends keyof PerformanceEntryMap>(type: K): PerformanceEntryMap[K][]
  }

  // https://w3c.github.io/event-timing/#sec-modifications-perf-timeline
  interface PerformanceObserverInit {
    durationThreshold?: number
  }

  // https://wicg.github.io/nav-speculation/prerendering.html#performance-navigation-timing-extension
  interface PerformanceNavigationTiming {
    activationStart?: number
  }

  // https://wicg.github.io/event-timing/#sec-performance-event-timing
  interface PerformanceEventTiming extends PerformanceEntry {
    duration: DOMHighResTimeStamp
    interactionId: number
  }

  // https://wicg.github.io/layout-instability/#sec-layout-shift-attribution
  interface LayoutShiftAttribution {
    node?: Node
    previousRect: DOMRectReadOnly
    currentRect: DOMRectReadOnly
  }

  // https://wicg.github.io/layout-instability/#sec-layout-shift
  interface LayoutShift extends PerformanceEntry {
    value: number
    sources: LayoutShiftAttribution[]
    hadRecentInput: boolean
  }

  // https://w3c.github.io/largest-contentful-paint/#sec-largest-contentful-paint-interface
  interface LargestContentfulPaint extends PerformanceEntry {
    renderTime: DOMHighResTimeStamp
    loadTime: DOMHighResTimeStamp
    size: number
    id: string
    url: string
    element?: Element
  }

  // https://w3c.github.io/long-animation-frame/#sec-PerformanceLongAnimationFrameTiming
  interface PerformanceLongAnimationFrameTiming extends PerformanceEntry {
    renderStart: DOMHighResTimeStamp
    duration: DOMHighResTimeStamp
  }

  interface TaskAttributionTiming extends PerformanceEntry {
    containerId: string
    containerName: string
    containerSrc: string
    containerType: 'iframe' | 'embed' | 'object'
  }

  interface PerformanceLongTaskTiming extends PerformanceEntry {
    entryType: 'longtask'
    name:
      | 'cross-origin-ancestor'
      | 'cross-origin-descendant'
      | 'cross-origin-unreachable'
      | 'multiple-contexts'
      | 'same-origin-ancestor'
      | 'same-origin-descendant'
      | 'same-origin-unreachable'
      | 'same-origin'
      | 'self'
    attribution: TaskAttributionTiming[]
  }
}

export interface Metric {
  name: string
  value: number
  navigationType?: 'navigate' | 'reload' | 'prerender' | 'restore'
}

export type MetricType = LCPMetric

export type LoadState = 'loading' | 'dom-interactive' | 'dom-content-loaded' | 'complete'
