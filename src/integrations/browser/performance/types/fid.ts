import type { LoadState, Metric } from './base'

export interface FIDMetric extends Metric {
  name: 'FID'
  /**
   * A selector identifying the element that the user interacted with. This
   * element will be the `target` of the `event` dispatched.
   */
  eventTarget: string
  /**
   * The time when the user interacted. This time will match the `timeStamp`
   * value of the `event` dispatched.
   */
  eventTime: number
  /**
   * The `type` of the `event` dispatched from the user interaction.
   */
  eventType: string
  /**
   * The `PerformanceEventTiming` entry corresponding to FID.
   */
  eventEntry?: PerformanceEventTiming
  /**
   * The loading state of the document at the time when the first interaction
   * occurred (see `LoadState` for details). If the first interaction occurred
   * while the document was loading and executing script (e.g. usually in the
   * `dom-interactive` phase) it can result in long input delays.
   */
  loadState: LoadState
}
