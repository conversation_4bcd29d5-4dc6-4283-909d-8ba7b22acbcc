import type { Metric } from './base'

export interface SPALoadMetric extends Metric {
  value: number
  name: 'SPA_LOAD'
  startTime: number
  records: Record[]
  source: 'timeout' | 'stable' | 'router'
  startLoadTime?: number
  initTime?: number
  onStartedTime?: number
  isLowPowerMode?: boolean
}

export interface Record {
  url: string
  duration: number
  startTime?: number
  type: 'api' | 'image' | 'background' | 'bridge' | 'resource'
  requestId?: string
  endTime?: number
}
