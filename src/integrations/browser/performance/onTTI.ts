import { observe } from './lib/observe'
import type { TTIMetric } from './types/tti'

export function onTTI(callback: (metric: TTIMetric) => void) {
  let fcpTime = 0
  let domContentLoadedEnd = 0
  let longTasks: { start: number; end: number; attribution: TaskAttributionTiming[] }[] = []
  let inProgressNetworkRequests = 0
  let ttiCandidate = 0
  let timeoutRef: number | null = null

  const fcpPo = observe('paint', function (entries) {
    const fcpEntires = entries.filter(function (entry) {
      return entry.name === 'first-contentful-paint'
    })
    if (fcpEntires.length > 0) {
      fcpTime = fcpEntires[0].startTime
      checkTTI()
    }
  })

  document.addEventListener('DOMContentLoaded', function () {
    domContentLoadedEnd = performance.now()
    checkTTI()
  })

  const taskPo = observe('longtask', function (entires) {
    entires.forEach(function (entry) {
      longTasks.push({ start: entry.startTime, end: entry.startTime + entry.duration, attribution: entry.attribution })
      longTasks = longTasks.filter(function (task) {
        return task.end > fcpTime
      })
    })
    checkTTI()
  })

  const xhrPo = observe('resource', function (entries) {
    for (const entry of entries) {
      if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {
        if (entry.startTime < fcpTime) continue
        if (entry.responseEnd > 0) {
          inProgressNetworkRequests--
        } else {
          inProgressNetworkRequests++
        }
      }
    }
    checkTTI()
  })

  function checkTTI() {
    if (!fcpTime || !domContentLoadedEnd) return

    let silenceWindowStart = fcpTime

    for (let i = 0; i < longTasks.length; i++) {
      const task = longTasks[i]
      if (task.start - silenceWindowStart >= 5000 && inProgressNetworkRequests <= 2) {
        ttiCandidate = task.end
        break
      }
      silenceWindowStart = task.end
    }

    if (!ttiCandidate) {
      ttiCandidate = fcpTime
    }

    ttiCandidate = Math.max(ttiCandidate, domContentLoadedEnd)

    if (ttiCandidate && fcpPo && taskPo && xhrPo) {
      fcpPo.disconnect()
      taskPo.disconnect()
      xhrPo.disconnect()

      if (timeoutRef) clearTimeout(timeoutRef)
      timeoutRef = setTimeout(function () {
        callback(JSON.parse(JSON.stringify({ name: 'TTI', value: ttiCandidate })) as TTIMetric)
      }, 0) as unknown as number
    }
  }
}
