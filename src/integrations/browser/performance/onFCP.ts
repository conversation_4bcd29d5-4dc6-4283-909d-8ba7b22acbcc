import { assign } from '../../../utils/polyfills'
import { getActivationStart } from './lib/getActivationStart'
import { getLoadState } from './lib/getLoadState'
import { getNavigationEntry } from './lib/getNavigationEntry'
import { observe } from './lib/observe'
import type { FCPMetric } from './types/fcp'

export const onFCP = function (callback: (entry: FCPMetric) => void) {
  const metric: FCPMetric = {
    value: -1,
    name: 'FCP',
    navigationType: 'navigate',
    firstByteToFCP: -1,
    loadState: 'complete',
    timeToFirstByte: 0
  }

  const po = observe('paint', function (entries) {
    if (entries.length) {
      const navigationEntry = getNavigationEntry()
      const fcpEntry = entries[entries.length - 1]

      po && po.disconnect()
      metric.value = Math.max(fcpEntry.startTime - getActivationStart(), 0)

      if (navigationEntry) {
        const activationStart = navigationEntry.activationStart || 0
        const ttfb = Math.max(0, navigationEntry.responseStart - activationStart)

        assign(metric, {
          timeToFirstByte: ttfb,
          firstByteToFCP: metric.value - ttfb,
          loadState: getLoadState(entries[0].startTime)
        })
      }
      callback(metric)
    }
  })
}
