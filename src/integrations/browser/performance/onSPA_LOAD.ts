import Aegis from '../../../core/Aegis'
import { checkIsIgnored, events, now } from '../../../utils'
import { createApiObserver } from '../../../utils/browser/apiObserver'
import { MockedMutationRecord, createDomObserver } from '../../../utils/browser/domObserver'
import { createRouteObserver } from '../../../utils/browser/routeObserver'
import { parseBackgroundImageUrls, toAbsoluteURL } from '../../../utils/browser/utils'
import { BrowserTiming } from '../../../utils/worldwide'
import { parseFetchArgs, urlToString } from '../api/utils'
import type { Record, SPALoadMetric } from './types'

export interface SPALoadOptions {
  loadTimeout: number
  stableTimeout: number
  mode?: 'history' | 'hash'
  ignoreUrls?: (string | RegExp)[]
}

export function onSPA_LOAD(aegis: Aegis, options: SPALoadOptions, callback: (metric: SPALoadMetric) => void): () => void {
  const loadTimeout = options.loadTimeout
  const stableTimeout = options.stableTimeout
  const ignoreUrls = options.ignoreUrls || []
  const supportResources = supportsPerformanceObserver() && PerformanceObserver.supportedEntryTypes.indexOf('resource') !== -1 && typeof performance.getEntriesByType === 'function'

  // Feature-detect module/nomodule execution without runtime injection
  const supportsModuleExecution = (function detectModuleSupport() {
    type HTMLScriptElementConstructor = { supports?: (type: string) => boolean }
    let result: boolean | undefined

    // Prefer HTMLScriptElement.supports when available
    if (typeof HTMLScriptElement !== 'undefined') {
      const ctor = HTMLScriptElement as unknown as HTMLScriptElementConstructor
      const supportsFn = ctor && typeof ctor.supports === 'function' ? ctor.supports : undefined
      if (supportsFn) {
        try {
          result = !!supportsFn('module')
        } catch (err) {
          // In case calling supports throws in some environments, fall back
          result = undefined
        }
      }
    }

    if (typeof result === 'boolean') return result

    try {
      // Fallback: if the 'noModule' property exists on script elements,
      // it indicates module-supporting browsers
      const probe = document.createElement('script')
      return 'noModule' in probe
    } catch (err) {
      // Extremely defensive default: assume modern environment
      return true
    }
  })()

  let requestId = 0
  let pageStartTime = now()
  if (!window.__SPA_LOAD_INITIALIZED__) {
    pageStartTime = performance.timeOrigin || performance.timing.navigationStart
    window.__SPA_LOAD_INITIALIZED__ = true
  }
  let latestEndTime = pageStartTime
  let records: Record[] = []
  let isObserverDisposed = false

  let pendingRequests: (number | string)[] = []
  let pendingImages: HTMLImageElement[] = []
  let pendingBgDomList: { dom: HTMLElement; urls: string[] }[] = []
  let pendingScriptDomList: HTMLScriptElement[] = []
  let pendingStyleSheets: HTMLLinkElement[] = []

  let stabilityCheckTimer: number | null = null
  let apiObserver: ReturnType<typeof createApiObserver> | null = null
  let domObserver: ReturnType<typeof createDomObserver> | null = null

  function checkPageStability() {
    if (now() - pageStartTime >= loadTimeout) {
      callback({
        name: 'SPA_LOAD',
        source: 'timeout',
        records: records,
        value: loadTimeout,
        startTime: pageStartTime,
        isLowPowerMode: BrowserTiming.isLowPowerMode,
        initTime: BrowserTiming.initTime,
        startLoadTime: BrowserTiming.startLoadTime,
        onStartedTime: BrowserTiming.onStartedTime
      })

      resetObservers()
      return
    }

    pendingScriptDomList = pendingScriptDomList.filter(function (item) {
      return document.contains(item)
    })

    pendingImages = pendingImages.filter(function (item) {
      return document.contains(item)
    })

    if (supportResources && pendingBgDomList.length) {
      const itemsToRemove = new Set<number>()
      const baseURI = getBaseURI()

      for (let i = 0; i < pendingBgDomList.length; i++) {
        const { dom, urls } = pendingBgDomList[i]

        // 如果DOM元素已经不在文档中，标记为移除
        if (!document.contains(dom)) {
          itemsToRemove.add(i)
          continue
        }

        // 获取当前的计算样式（与添加时保持一致）
        const style = window.getComputedStyle(dom)
        const currentBgImage = style.backgroundImage && style.backgroundImage !== 'none' ? style.backgroundImage : dom.style.backgroundImage
        const currentUrls = parseBackgroundImageUrls(currentBgImage, baseURI)

        // 检查每个URL的状态
        let shouldRemove = true
        for (let j = 0; j < urls.length; j++) {
          const url = urls[j]

          // URL仍在当前样式中
          if (currentUrls.includes(url)) {
            // 检查资源是否已加载
            const entry = findResourceEntry(url)
            if (entry !== void 0) {
              // 资源已加载，更新记录
              for (let k = 0; k < records.length; k++) {
                if (records[k].url === url && records[k].startTime) {
                  records[k].duration = entry
                  records[k].endTime = now()
                  latestEndTime = now()
                  break
                }
              }
            } else {
              // 资源还未加载，检查是否在样式表中
              // 只有在CSS已加载且确认不在样式表中时才移除
              if (!isBgInStyleSheet(url) && isCssFullyLoaded()) {
                records = records.filter(function (record) {
                  return record.url !== url
                })
              } else {
                // 保留在待处理列表中
                shouldRemove = false
              }
            }
          } else {
            // URL不在当前样式中，从records中移除
            records = records.filter(function (record) {
              return record.url !== url
            })
          }
        }

        if (shouldRemove) {
          itemsToRemove.add(i)
        }
      }

      // 根据索引集合过滤pendingBgDomList
      pendingBgDomList = pendingBgDomList.filter(function (_, index) {
        return !itemsToRemove.has(index)
      })
    }

    const isAllResourcesLoaded = pendingRequests.length === 0 && pendingImages.length === 0 && pendingBgDomList.length === 0 && pendingScriptDomList.length === 0 && pendingStyleSheets.length === 0

    if (isAllResourcesLoaded) {
      callback({
        name: 'SPA_LOAD',
        source: 'stable',
        records: records,
        startTime: pageStartTime,
        value: latestEndTime - pageStartTime,
        startLoadTime: BrowserTiming.startLoadTime,
        initTime: BrowserTiming.initTime,
        onStartedTime: BrowserTiming.onStartedTime,
        isLowPowerMode: BrowserTiming.isLowPowerMode
      })

      resetObservers()
      return
    }

    startStabilityCheck()
  }

  function startStabilityCheck() {
    if (isObserverDisposed) {
      return
    }

    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
    }

    stabilityCheckTimer = setTimeout(function () {
      checkPageStability()
    }, stableTimeout) as unknown as number
  }

  function resetObservers() {
    events.off('bridgeRequest', bridgeRequestHandler)
    events.off('bridgeResponse', bridgeResponseHandler)
    for (let i = 0; i < pendingImages.length; i++) {
      pendingImages[i].removeEventListener('load', onImageLoad)
      pendingImages[i].removeEventListener('error', onImageLoad)
    }
    for (let i = 0; i < pendingStyleSheets.length; i++) {
      pendingStyleSheets[i].removeEventListener('load', onStyleSheetLoad)
      pendingStyleSheets[i].removeEventListener('error', onStyleSheetLoad)
    }

    records = []
    requestId = 0
    pendingImages = []
    pendingRequests = []
    pendingBgDomList = []
    pendingScriptDomList = []
    pendingStyleSheets = []
    pageStartTime = now()
    latestEndTime = pageStartTime
    isObserverDisposed = true

    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
      stabilityCheckTimer = null
    }

    if (apiObserver) {
      apiObserver.dispose()
      apiObserver = null
    }

    if (domObserver) {
      domObserver.dispose()
      domObserver = null
    }
  }

  function onImageLoad(event: Event) {
    const target = event.target as HTMLImageElement
    const idx = pendingImages.indexOf(target)

    if (idx !== -1) {
      pendingImages = pendingImages.filter(function (item) {
        return item !== target
      })

      const current = now()
      /* istanbul ignore else -- @preserve */
      if (current > latestEndTime) {
        latestEndTime = current
      }

      for (let i = 0; i < records.length; i++) {
        if (records[i].url === target.src && records[i].startTime) {
          records[i].duration = now() - records[i].startTime!
          records[i].endTime = now()
          break
        }
      }

      startStabilityCheck()
    }
  }

  function addPendingImage(img: HTMLImageElement) {
    if (!img.getAttribute('src')) {
      return
    }
    pendingImages.push(img)
    const fullUrl = toAbsoluteURL(img.src, location.origin + location.pathname)
    const record = records.find(function (record) {
      return record.url === fullUrl
    })

    img.addEventListener('load', onImageLoad, { once: true })
    img.addEventListener('error', onImageLoad, { once: true })

    if (!record) {
      records.push({
        duration: 0,
        type: 'image',
        url: fullUrl,
        startTime: now()
      })
    }
    setTimeout(function () {
      if (!isObserverDisposed) {
        if (!isElementInViewport(img) && !img.complete) {
          img.removeEventListener('load', onImageLoad)
          img.removeEventListener('error', onImageLoad)
          pendingImages = pendingImages.filter(function (item) {
            return item !== img
          })
          records = records.filter(function (record) {
            return record.url !== fullUrl
          })
        }
      }
    }, 10)
  }

  function onScriptLoad(event: Event) {
    const target = event.target as HTMLScriptElement
    const idx = pendingScriptDomList.indexOf(target)

    if (idx !== -1) {
      pendingScriptDomList = pendingScriptDomList.filter(function (item) {
        return item !== target
      })

      const current = now()
      /* istanbul ignore else -- @preserve */
      if (current > latestEndTime) {
        latestEndTime = current
      }

      for (let i = 0; i < records.length; i++) {
        /* istanbul ignore else -- @preserve */
        if (records[i].url === target.src && records[i].startTime) {
          records[i].duration = now() - records[i].startTime!
          records[i].endTime = now()
          break
        }
      }

      startStabilityCheck()
    }
  }

  function onStyleSheetLoad(event: Event) {
    const target = event.target as HTMLLinkElement
    const idx = pendingStyleSheets.indexOf(target)

    if (idx !== -1) {
      pendingStyleSheets = pendingStyleSheets.filter(function (item) {
        return item !== target
      })

      // CSS加载完成后，重新检查待处理的背景图片
      startStabilityCheck()
    }
  }

  function addPendingScript(script: HTMLScriptElement) {
    pendingScriptDomList.push(script)

    script.addEventListener('load', onScriptLoad, { once: true })
    script.addEventListener('error', onScriptLoad, { once: true })
    const isModule = script.type === 'module'
    const isNoModule = script.noModule === true
    // If the browser does not execute module scripts, ignore module ones
    if (isModule && !supportsModuleExecution) {
      pendingScriptDomList = pendingScriptDomList.filter(function (item) {
        return item !== script
      })
      script.removeEventListener('load', onScriptLoad)
      script.removeEventListener('error', onScriptLoad)
      records = records.filter(function (record) {
        return record.url !== script.src
      })
    }

    // If the browser executes module scripts, it will not execute nomodule ones
    if (isNoModule && supportsModuleExecution) {
      pendingScriptDomList = pendingScriptDomList.filter(function (item) {
        return item !== script
      })
      script.removeEventListener('load', onScriptLoad)
      script.removeEventListener('error', onScriptLoad)
      records = records.filter(function (record) {
        return record.url !== script.src
      })
    }
  }

  function bridgeRequestHandler(requestId: string, _method: string, url: string) {
    /* istanbul ignore else -- @preserve */
    if (!isObserverDisposed) {
      pendingRequests.push(requestId)
      records.push({
        type: 'bridge',
        requestId,
        url,
        duration: 0,
        startTime: now()
      })
    }
  }

  function bridgeResponseHandler(requestId: string) {
    /* istanbul ignore else -- @preserve */
    if (!isObserverDisposed && pendingRequests.indexOf(requestId) !== -1) {
      latestEndTime = now()
      for (let i = 0; i < records.length; i++) {
        /* istanbul ignore else -- @preserve */
        if (records[i].requestId === requestId && records[i].startTime) {
          records[i].duration = now() - records[i].startTime!
          records[i].endTime = now()
          break
        }
      }
    }
    pendingRequests = pendingRequests.filter(function (item) {
      return item !== requestId
    })
  }

  function initObservers() {
    isObserverDisposed = false

    const trackRequest = function () {
      const startTime = now()
      const currentRequestId = ++requestId
      pendingRequests.push(currentRequestId)

      startStabilityCheck()

      return {
        complete: function (url: string) {
          pendingRequests = pendingRequests.filter(function (item) {
            return item !== currentRequestId
          })
          const current = now()
          /* istanbul ignore else -- @preserve */
          if (current > latestEndTime) {
            latestEndTime = current
          }
          records.push({
            url: url,
            type: 'api',
            duration: now() - startTime
          })

          startStabilityCheck()
        }
      }
    }

    apiObserver = createApiObserver({
      fetchInterceptor: function (original, args) {
        const fetchArgs = parseFetchArgs(args[0], args[1])

        if (checkIsIgnored(ignoreUrls, fetchArgs.url)) {
          return original.apply(window, args)
        }

        const tracker = trackRequest()

        return original.apply(window, args).then(
          function (res) {
            tracker.complete(fetchArgs.url)
            return res
          },
          function (err) {
            tracker.complete(fetchArgs.url)
            throw err
          }
        )
      },
      xhrInterceptor: function (this: XMLHttpRequest, original, args) {
        const ctx = this
        const url = urlToString(args[1]) as string

        if (checkIsIgnored(ignoreUrls, url)) {
          original.apply(ctx, args)
          return
        }

        const tracker = trackRequest()

        const onLoadEnd = function () {
          tracker.complete(url)
          ctx.removeEventListener('loadend', onLoadEnd)
        }

        ctx.addEventListener('loadend', onLoadEnd)

        original.apply(ctx, args)
      }
    })

    domObserver = createDomObserver(document, function (list) {
      const baseURI = getBaseURI()

      for (let i = 0; i < list.length; i++) {
        const record = list[i]

        if ((record as MockedMutationRecord).isMocked) {
          continue
        }

        for (let j = 0; j < record.addedNodes.length; j++) {
          const item = record.addedNodes[j]

          if (item instanceof HTMLElement) {
            const nodeList = getFlatNodeList(item)
            for (let n = 0; n < nodeList.length; n++) {
              const node = nodeList[n]
              if (!(node instanceof HTMLElement)) {
                continue
              }
              if (supportResources) {
                if (node instanceof HTMLScriptElement) {
                  if (node.src && pendingScriptDomList.indexOf(node) === -1) {
                    const fullUrl = toAbsoluteURL(node.src, location.origin + location.pathname)
                    if (!checkIsIgnored(ignoreUrls, fullUrl) && findResourceEntry(fullUrl) === undefined) {
                      const record = records.filter(function (record) {
                        return record.url === fullUrl
                      })[0]

                      if (!record) {
                        records.push({
                          duration: 0,
                          type: 'resource',
                          url: fullUrl,
                          startTime: now()
                        })
                      }
                      addPendingScript(node)
                    }
                  }
                } else if (node instanceof HTMLLinkElement && node.rel === 'stylesheet' && node.href) {
                  // 监听CSS加载
                  if (pendingStyleSheets.indexOf(node) === -1) {
                    pendingStyleSheets.push(node)
                    node.addEventListener('load', onStyleSheetLoad, { once: true })
                    node.addEventListener('error', onStyleSheetLoad, { once: true })
                  }
                } else {
                  setTimeout(() => {
                    if (nodeIsVisible(node)) {
                      const style = window.getComputedStyle(item)
                      const backgroundImage = style.backgroundImage && style.backgroundImage !== 'none' ? /* istanbul ignore next */ style.backgroundImage : item.style.backgroundImage
                      const urls = parseBackgroundImageUrls(backgroundImage, baseURI)
                      if (urls.length) {
                        pendingBgDomList.push({ dom: item, urls })
                        for (let i = 0; i < urls.length; i++) {
                          const url = urls[i]
                          const record = records.filter(function (record) {
                            return record.url === url
                          })[0]

                          if (!record) {
                            records.push({
                              duration: 0,
                              type: 'background',
                              url: url,
                              startTime: now()
                            })
                          }
                        }
                      }
                    }
                  })
                }
              }
              if (node instanceof HTMLImageElement && pendingImages.indexOf(node) === -1 && !node.complete && !/^data:/.test(node.src)) {
                if (!checkIsIgnored(ignoreUrls, node.src)) {
                  addPendingImage(node)
                }
              }
            }
          }
        }

        for (let j = 0; j < record.removedNodes.length; j++) {
          const item = record.removedNodes[j]

          if (item instanceof HTMLElement) {
            const nodeList = getFlatNodeList(item)
            for (let n = 0; n < nodeList.length; n++) {
              const node = nodeList[n]
              if (!(node instanceof HTMLElement)) {
                continue
              }
              if (node instanceof HTMLScriptElement) {
                if (node.src) {
                  const index = pendingScriptDomList.indexOf(node)
                  if (index !== -1) {
                    const fullUrl = toAbsoluteURL(node.src, location.origin + location.pathname)
                    pendingScriptDomList = pendingScriptDomList.filter(function (item) {
                      return item !== node
                    })
                    node.removeEventListener('load', onScriptLoad)
                    node.removeEventListener('error', onScriptLoad)

                    const current = now()
                    /* istanbul ignore else -- @preserve */
                    if (current > latestEndTime) {
                      latestEndTime = current
                    }

                    for (let i = 0; i < records.length; i++) {
                      /* istanbul ignore else -- @preserve */
                      if (records[i].url === fullUrl && records[i].startTime) {
                        records[i].endTime = now()
                        break
                      }
                    }
                  }
                }
              } else if (node instanceof HTMLImageElement) {
                node.removeEventListener('load', onImageLoad)
                node.removeEventListener('error', onImageLoad)
                pendingImages = pendingImages.filter(function (item) {
                  return item !== node
                })

                const current = now()
                /* istanbul ignore else -- @preserve */
                if (current > latestEndTime) {
                  latestEndTime = current
                }

                for (let i = 0; i < records.length; i++) {
                  /* istanbul ignore else -- @preserve */
                  if (records[i].url === node.src && records[i].startTime) {
                    records[i].endTime = now()
                    break
                  }
                }
              } else if (node instanceof HTMLLinkElement && node.rel === 'stylesheet') {
                node.removeEventListener('load', onStyleSheetLoad)
                node.removeEventListener('error', onStyleSheetLoad)
                pendingStyleSheets = pendingStyleSheets.filter(function (item) {
                  return item !== node
                })
              } else {
                const style = window.getComputedStyle(node)
                const backgroundImage = style.backgroundImage && style.backgroundImage !== 'none' ? /* istanbul ignore next */ style.backgroundImage : node.style.backgroundImage
                const urls = parseBackgroundImageUrls(backgroundImage, baseURI)
                pendingBgDomList = pendingBgDomList.filter(function (item) {
                  return item.dom !== node
                })
                if (urls.length) {
                  for (let k = 0; k < records.length; k++) {
                    if (urls.indexOf(records[k].url) !== -1) {
                      records[k].endTime = now()
                      latestEndTime = now()
                    }
                  }
                }
              }
            }
          }
        }
      }

      startStabilityCheck()
    })

    events.on('bridgeRequest', bridgeRequestHandler)
    events.on('bridgeResponse', bridgeResponseHandler)
  }

  function handleRouteChange() {
    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
      stabilityCheckTimer = null

      callback({
        source: 'router',
        name: 'SPA_LOAD',
        records: records,
        startTime: pageStartTime,
        value: now() - pageStartTime,
        startLoadTime: BrowserTiming.startLoadTime,
        initTime: BrowserTiming.initTime,
        onStartedTime: BrowserTiming.onStartedTime
      })
    }

    resetObservers()
    initObservers()
    startStabilityCheck()
  }

  const getMode = function () {
    if (options && options.mode) {
      return options.mode
    }

    const pageviewIntegration = aegis.getIntegration('pageview')

    /* istanbul ignore else -- @preserve */
    if (pageviewIntegration && pageviewIntegration.options && pageviewIntegration.options.mode) {
      return pageviewIntegration.options.mode as 'history' | 'hash'
    }

    /* istanbul ignore next -- @preserve */
    return 'history'
  }

  const routeObserver = createRouteObserver({
    mode: getMode(),
    onHashChange: handleRouteChange,
    onHistoryChange: handleRouteChange,
    onPushState: handleRouteChange,
    onReplaceState: handleRouteChange
  })

  function initialScanScripts() {
    if (!(supportsPerformanceObserver() && PerformanceObserver.supportedEntryTypes.indexOf('resource') !== -1 && typeof performance.getEntriesByType === 'function')) {
      return
    }

    const allScripts = document.querySelectorAll('script')
    for (let i = 0; i < allScripts.length; i++) {
      const script = allScripts.item(i)
      const url = allScripts[i].src

      if (url && /^http/.test(url)) {
        const fullUrl = toAbsoluteURL(url, location.origin + location.pathname)
        const loaded = findResourceEntry(fullUrl) !== void 0
        if (!checkIsIgnored(ignoreUrls, fullUrl) && !pendingScriptDomList.includes(script) && !loaded) {
          const record = records.filter(function (record) {
            return record.url === fullUrl
          })[0]
          if (!record) {
            records.push({
              duration: 0,
              type: 'resource',
              url: fullUrl,
              startTime: now()
            })
          }
          addPendingScript(script)
        }
      }
    }
  }

  // Start observers immediately; do not rely on runtime-injected scripts
  initObservers()
  startStabilityCheck()
  initialScanScripts()

  return function cleanup(): void {
    resetObservers()
    routeObserver.dispose()
  }
}

function supportsPerformanceObserver() {
  return 'PerformanceObserver' in window && typeof PerformanceObserver === 'function'
}

function findResourceEntry(url: string) {
  if (!supportsPerformanceObserver() || !('PerformanceObserver' in window) || typeof performance.getEntriesByType !== 'function') {
    return undefined
  }
  const resources = performance.getEntriesByType('resource')

  for (let i = 0; i < resources.length; i++) {
    if (resources[i].name === url) {
      return resources[i].duration
    }
  }
}

function getBaseURI() {
  return document.baseURI || window.location.href
}

function getFlatNodeList(node: Node) {
  const nodeList = []
  const stack = [node]
  while (stack.length) {
    const child = stack.pop()
    if (child instanceof HTMLElement) {
      nodeList.push(child)
      if (child.childNodes && child.childNodes.length) {
        Array.prototype.push.apply(stack, Array.prototype.slice.call(child.childNodes) as HTMLElement[])
      }
    }
  }
  return nodeList
}

function nodeIsVisible(node: HTMLElement) {
  const rect = node.getBoundingClientRect()
  return rect.width > 0 || rect.height > 0
}

function isBgInStyleSheet(url: string) {
  const styleSheets = document.styleSheets
  let flag = false
  for (let i = 0; i < styleSheets.length; i++) {
    if (flag) {
      break
    }
    const styleSheet = styleSheets.item(i)
    if (styleSheet) {
      try {
        flag = searchRulesForBg(styleSheet.cssRules, url, styleSheet.href || getBaseURI())
      } catch (err) {
        console.error('access cssRules denied', err)
      }
    }
  }

  return flag
}

// 辅助函数：递归搜索所有类型的规则
function searchRulesForBg(cssRules: CSSRuleList, url: string, baseURL: string): boolean {
  for (let j = 0; j < cssRules.length; j++) {
    const rule = cssRules.item(j)

    if (!rule) continue

    // 处理标准样式规则
    if (rule instanceof CSSStyleRule) {
      const bg = rule.style.backgroundImage
      const urls = parseBackgroundImageUrls(bg, baseURL)
      if (urls.length && urls.indexOf(url) !== -1) {
        return true
      }
    }
    // 处理媒体查询规则
    else if (rule instanceof CSSMediaRule || rule instanceof CSSSupportsRule) {
      if (searchRulesForBg(rule.cssRules, url, baseURL)) {
        return true
      }
    }
    // 可以添加其他类型的规则处理
  }
  return false
}

function isElementInViewport(el: HTMLElement) {
  const rect = el.getBoundingClientRect()
  return rect.top >= 0 && rect.bottom <= window.innerHeight && rect.left >= 0 && rect.right <= window.innerWidth && nodeIsVisible(el)
}

// 检查所有CSS是否已完全加载
function isCssFullyLoaded() {
  const links = document.querySelectorAll('link[rel="stylesheet"]')
  for (let i = 0; i < links.length; i++) {
    const link = links[i] as HTMLLinkElement
    // 检查link元素的sheet属性是否存在
    // 在某些浏览器中，如果CSS还未加载，sheet为null
    if (!link.sheet) {
      // 额外检查：如果link有href且不是data URL，认为CSS还未加载
      if (link.href && !link.href.startsWith('data:')) {
        return false
      }
    }
  }
  return true
}
