import Aegis from '../../../core/Aegis'
import { checkIsIgnored, events, now } from '../../../utils'
import { createApiObserver } from '../../../utils/browser/apiObserver'
import { createRouteObserver } from '../../../utils/browser/routeObserver'
import { BrowserTiming } from '../../../utils/worldwide'
import { parseFetchArgs, urlToString } from '../api/utils'
import type { Record, SPALoadMetric } from './types'

export interface SPALoadOptions {
  loadTimeout: number
  stableTimeout: number
  mode?: 'history' | 'hash'
  ignoreUrls?: (string | RegExp)[]
}

export function onSPA_LOAD(aegis: Aegis, options: SPALoadOptions, callback: (metric: SPALoadMetric) => void): () => void {
  const loadTimeout = options.loadTimeout
  const stableTimeout = options.stableTimeout
  const ignoreUrls = options.ignoreUrls || []
  const supportResources = supportsPerformanceObserver() && PerformanceObserver.supportedEntryTypes.indexOf('resource') !== -1 && typeof performance.getEntriesByType === 'function'

  let requestId = 0
  let pageStartTime = now()
  if (!window.__SPA_LOAD_INITIALIZED__) {
    pageStartTime = performance.timeOrigin || performance.timing.navigationStart
    window.__SPA_LOAD_INITIALIZED__ = true
  }
  let latestEndTime = pageStartTime
  let records: Record[] = []
  let isObserverDisposed = false

  let pendingRequests: (number | string)[] = []
  const resourceLoadTimes: number[] = []

  let stabilityCheckTimer: number | null = null
  let apiObserver: ReturnType<typeof createApiObserver> | null = null
  let resourceObserver: PerformanceObserver | null = null

  function checkPageStability() {
    if (now() - pageStartTime >= loadTimeout) {
      callback({
        name: 'SPA_LOAD',
        source: 'timeout',
        records: records,
        value: loadTimeout,
        startTime: pageStartTime,
        isLowPowerMode: BrowserTiming.isLowPowerMode,
        initTime: BrowserTiming.initTime,
        startLoadTime: BrowserTiming.startLoadTime,
        onStartedTime: BrowserTiming.onStartedTime
      })

      resetObservers()
      return
    }

    // 检查资源稳态：寻找第一个超过 stableTimeout 时间间隔的稳态记录点
    const allEndTimes = [...resourceLoadTimes].sort((a, b) => a - b)

    if (allEndTimes.length > 0) {
      const currentTime = now()
      const lastResourceTime = allEndTimes[allEndTimes.length - 1]

      // 如果最后一个资源加载完成时间距离现在超过了稳定超时时间，且没有待处理的请求
      if (currentTime - lastResourceTime >= stableTimeout && pendingRequests.length === 0) {
        callback({
          name: 'SPA_LOAD',
          source: 'stable',
          records: records,
          startTime: pageStartTime,
          value: latestEndTime - pageStartTime,
          startLoadTime: BrowserTiming.startLoadTime,
          initTime: BrowserTiming.initTime,
          onStartedTime: BrowserTiming.onStartedTime,
          isLowPowerMode: BrowserTiming.isLowPowerMode
        })

        resetObservers()
        return
      }
    }

    startStabilityCheck()
  }

  function startStabilityCheck() {
    if (isObserverDisposed) {
      return
    }

    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
    }

    stabilityCheckTimer = setTimeout(function () {
      checkPageStability()
    }, stableTimeout) as unknown as number
  }

  function resetObservers() {
    events.off('bridgeRequest', bridgeRequestHandler)
    events.off('bridgeResponse', bridgeResponseHandler)

    records = []
    requestId = 0
    pendingRequests = []
    resourceLoadTimes.length = 0
    pageStartTime = now()
    latestEndTime = pageStartTime
    isObserverDisposed = true

    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
      stabilityCheckTimer = null
    }

    if (apiObserver) {
      apiObserver.dispose()
      apiObserver = null
    }

    if (resourceObserver) {
      resourceObserver.disconnect()
      resourceObserver = null
    }
  }

  function bridgeRequestHandler(requestId: string, _method: string, url: string) {
    /* istanbul ignore else -- @preserve */
    if (!isObserverDisposed) {
      pendingRequests.push(requestId)
      records.push({
        type: 'bridge',
        requestId,
        url,
        duration: 0,
        startTime: now()
      })
    }
  }

  function bridgeResponseHandler(requestId: string) {
    /* istanbul ignore else -- @preserve */
    if (!isObserverDisposed && pendingRequests.indexOf(requestId) !== -1) {
      latestEndTime = now()
      for (let i = 0; i < records.length; i++) {
        /* istanbul ignore else -- @preserve */
        if (records[i].requestId === requestId && records[i].startTime) {
          records[i].duration = now() - records[i].startTime!
          records[i].endTime = now()
          break
        }
      }
    }
    pendingRequests = pendingRequests.filter(function (item) {
      return item !== requestId
    })
  }

  function initObservers() {
    isObserverDisposed = false

    // 初始化 PerformanceObserver 监控资源加载
    if (supportResources) {
      resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        for (const entry of entries) {
          if (entry.entryType === 'resource') {
            const url = entry.name

            // 检查是否需要忽略此 URL
            if (checkIsIgnored(ignoreUrls, url)) {
              continue
            }

            const endTime = entry.startTime + entry.duration
            resourceLoadTimes.push(endTime)

            if (endTime > latestEndTime) {
              latestEndTime = endTime
            }

            // 添加到 records，如果还没有记录的话
            const existingRecord = records.find((record) => record.url === url)
            if (!existingRecord) {
              records.push({
                url: url,
                type: 'resource',
                duration: entry.duration,
                startTime: entry.startTime,
                endTime: endTime
              })
            }
          }
        }

        startStabilityCheck()
      })

      resourceObserver.observe({ entryTypes: ['resource'], buffered: true })
    }

    const trackRequest = function () {
      const startTime = now()
      const currentRequestId = ++requestId
      pendingRequests.push(currentRequestId)

      startStabilityCheck()

      return {
        complete: function (url: string) {
          pendingRequests = pendingRequests.filter(function (item) {
            return item !== currentRequestId
          })
          const current = now()
          /* istanbul ignore else -- @preserve */
          if (current > latestEndTime) {
            latestEndTime = current
          }
          records.push({
            url: url,
            type: 'api',
            duration: now() - startTime
          })

          startStabilityCheck()
        }
      }
    }

    apiObserver = createApiObserver({
      fetchInterceptor: function (original, args) {
        const fetchArgs = parseFetchArgs(args[0], args[1])

        if (checkIsIgnored(ignoreUrls, fetchArgs.url)) {
          return original.apply(window, args)
        }

        const tracker = trackRequest()

        return original.apply(window, args).then(
          function (res) {
            tracker.complete(fetchArgs.url)
            return res
          },
          function (err) {
            tracker.complete(fetchArgs.url)
            throw err
          }
        )
      },
      xhrInterceptor: function (this: XMLHttpRequest, original, args) {
        const ctx = this
        const url = urlToString(args[1]) as string

        if (checkIsIgnored(ignoreUrls, url)) {
          original.apply(ctx, args)
          return
        }

        const tracker = trackRequest()

        const onLoadEnd = function () {
          tracker.complete(url)
          ctx.removeEventListener('loadend', onLoadEnd)
        }

        ctx.addEventListener('loadend', onLoadEnd)

        original.apply(ctx, args)
      }
    })

    events.on('bridgeRequest', bridgeRequestHandler)
    events.on('bridgeResponse', bridgeResponseHandler)
  }

  function handleRouteChange() {
    if (stabilityCheckTimer) {
      clearTimeout(stabilityCheckTimer)
      stabilityCheckTimer = null

      callback({
        source: 'router',
        name: 'SPA_LOAD',
        records: records,
        startTime: pageStartTime,
        value: now() - pageStartTime,
        startLoadTime: BrowserTiming.startLoadTime,
        initTime: BrowserTiming.initTime,
        onStartedTime: BrowserTiming.onStartedTime
      })
    }

    resetObservers()
    initObservers()
    startStabilityCheck()
  }

  const getMode = function () {
    if (options && options.mode) {
      return options.mode
    }

    const pageviewIntegration = aegis.getIntegration('pageview')

    /* istanbul ignore else -- @preserve */
    if (pageviewIntegration && pageviewIntegration.options && pageviewIntegration.options.mode) {
      return pageviewIntegration.options.mode as 'history' | 'hash'
    }

    /* istanbul ignore next -- @preserve */
    return 'history'
  }

  const routeObserver = createRouteObserver({
    mode: getMode(),
    onHashChange: handleRouteChange,
    onHistoryChange: handleRouteChange,
    onPushState: handleRouteChange,
    onReplaceState: handleRouteChange
  })

  // Start observers immediately; do not rely on runtime-injected scripts
  initObservers()
  startStabilityCheck()

  return function cleanup(): void {
    resetObservers()
    routeObserver.dispose()
  }
}

function supportsPerformanceObserver() {
  return 'PerformanceObserver' in window && typeof PerformanceObserver === 'function'
}
