import { setObjectProperty } from '../../../../utils/browser/utils'
import { isArray } from '../../../../utils/is'

export interface IRecord {
  [key: string]: string | number | number[] | string[] | IRecord | IRecord[]
}

export function reducePrecision(record: IRecord, depth?: number) {
  const maxDepth = 50
  const precision = 2

  depth = depth || 0

  if (!record || typeof record !== 'object' || depth > maxDepth) {
    return
  }

  for (const key in record) {
    const value = record[key]
    if (value && typeof value === 'number') {
      if (!isInteger(value as unknown as number)) {
        setObjectProperty(record, key, +(value as unknown as number).toFixed(precision))
      }
    }

    if (value && typeof value === 'object') {
      if (isArray(value)) {
        ;(value as IRecord[]).forEach(function (item: IRecord, index: number) {
          if (typeof item === 'number') {
            if (!isInteger(item)) {
              setObjectProperty(value, index, +(item as number).toFixed(precision))
            }
          } else {
            reducePrecision(item, (depth as number) + 1)
          }
        })
      } else {
        reducePrecision(value as unknown as IRecord, depth + 1)
      }
    }
  }
}

export function isInteger(value: number) {
  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value
}
