import type { Integration } from '../../../core/Aegis'
import Aegis from '../../../core/Aegis'
import { isInvalidWindow } from '../../../utils/browser/utils'
import { includes } from '../../../utils/polyfills'
import { IRecord, reducePrecision } from './lib/reducePrecision'
import { onCLS } from './onCLS'
import { onFCP } from './onFCP'
import { onFID } from './onFID'
import { onLCP } from './onLCP'
import { SPALoadOptions, onSPA_LOAD } from './onSPA_LOAD'
import { onTTFB } from './onTTFB'
import { onTTI } from './onTTI'
import type { CLSMetric, FCPMetric, FIDMetric, LCPMetric, SPALoadMetric, TTFBMetric, TTIMetric } from './types'

interface IPerformanceEntryMap {
  FCP: FCPMetric
  CLS: CLSMetric
  LCP: LCPMetric
  TTI: TTIMetric
  FID: FIDMetric
  TTFB: TTFBMetric
  SPA_LOAD: SPALoadMetric
}

type PerformanceEntryType = keyof IPerformanceEntryMap

export interface IPerformanceReport<T extends PerformanceEntryType> {
  type: 'perf'
  immediate?: boolean
  payload: IPerformanceEntryMap[T]
}

declare module '../../../core/Aegis' {
  export default interface Aegis {
    report<P extends IPerformanceReport<PerformanceEntryType>>(report: P): void
  }
}

interface IPerformanceOptions extends Record<string, unknown> {
  spaLoad?: SPALoadOptions
  reportType?: ('FCP' | 'LCP' | 'TTFB' | 'CLS' | 'TTI' | 'FID' | 'SPA_LOAD')[]
}

export default function (options?: IPerformanceOptions): Integration {
  options = options || {}
  const spaLoad = options.spaLoad || { loadTimeout: 30000, stableTimeout: 300 }

  const reportType = options.reportType || ['FCP', 'LCP', 'TTFB', 'CLS', 'TTI', 'FID', 'SPA_LOAD']

  return {
    name: 'performance',
    options: options,
    setup(aegis: Aegis) {
      if (isInvalidWindow()) {
        return
      }
      if (includes(reportType, 'FCP')) {
        onFCP(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'FCP'>(aegis, entry)
        })
      }

      if (includes(reportType, 'LCP')) {
        onLCP(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'LCP'>(aegis, entry)
        })
      }

      if (includes(reportType, 'TTI')) {
        onTTI(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'TTI'>(aegis, entry)
        })
      }

      if (includes(reportType, 'FID')) {
        onFID(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'FID'>(aegis, entry)
        })
      }

      if (includes(reportType, 'TTFB')) {
        onTTFB(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'TTFB'>(aegis, entry)
        })
      }

      if (includes(reportType, 'CLS')) {
        onCLS(function (entry) {
          reducePrecision(entry as unknown as IRecord)
          reportPerfMetric<'CLS'>(aegis, entry)
        })
      }

      if (includes(reportType, 'SPA_LOAD')) {
        spaLoad.ignoreUrls = (spaLoad.ignoreUrls || []).concat(aegis.getReportUrls())

        onSPA_LOAD(aegis, spaLoad, function (entry) {
          reportPerfMetric<'SPA_LOAD'>(aegis, entry)
        })
      }
    }
  }
}

function reportPerfMetric<T extends PerformanceEntryType>(aegis: Aegis, entry: IPerformanceEntryMap[T]) {
  aegis.report<IPerformanceReport<T>>({
    type: 'perf',
    payload: entry
  })
}
