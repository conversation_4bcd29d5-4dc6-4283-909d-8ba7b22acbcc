import Aegis from '../../core/Aegis'
import { createCleanupTask } from '../../utils'
import type { Integration } from '../../core/Aegis'
import { attachEvent, detachEvent } from '../../utils/browser/utils'
import htmlTreeStringify from '../../utils/browser/htmlTreeStringify'

type ActionReport = {
  type: 'action'
  immediate?: boolean
  payload: {
    type: string
    target: {
      text: string
      path: string
    }
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends ActionReport>(report: P): void
  }
}

export default function (): Integration {
  const cleanupTasks = createCleanupTask()

  return {
    name: 'action',
    setup: function (aegis: Aegis) {
      const supportPointerEvent = !!window.PointerEvent
      const eventName = supportPointerEvent ? 'pointerdown' : 'click'

      const clickOrPointerHandler = function (event: Event) {
        eventListener(event, aegis)
      }

      attachEvent(eventName, clickOrPointerHandler, true)

      cleanupTasks.add(function () {
        detachEvent(eventName, clickOrPointerHandler, true)
      })
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function eventListener(event: Event, aegis: Aegis) {
  if (event.target instanceof Element) {
    let text = ''

    if ('innerText' in event.target && event.target.innerText) {
      text = event.target.innerText as string
    } else if (event.target.textContent) {
      text = event.target.textContent
    }

    aegis.report({
      type: 'action',
      immediate: true,
      payload: {
        type: event.type,
        target: {
          text: text || '',
          path: htmlTreeStringify(event.target)
        }
      }
    })
  }
}
