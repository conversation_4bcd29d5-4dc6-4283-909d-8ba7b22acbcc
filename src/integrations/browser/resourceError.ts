import Aegis from '../../core/Aegis'
import type { Integration } from '../../core/Aegis'
import { createTiming } from '../../utils/resource'
import { createCleanupTask, now } from '../../utils'
import { attachEvent, detachEvent } from '../../utils/browser/utils'
import htmlTreeStringify from '../../utils/browser/htmlTreeStringify'

interface ResourceError {
  url: string
  xpath?: string
  tagName: string
  timestamp?: number
  integrity?: string
  timing?: ReturnType<typeof createTiming>
}

export type ResourceErrorReport = {
  type: 'resource_error'
  immediate?: boolean
  payload: ResourceError
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends ResourceErrorReport>(report: P): void
    reportResourceError(target: EventTarget | null, timestamp?: number): void
  }
}

export default function (): Integration {
  const cleanupTasks = createCleanupTask()

  return {
    name: 'resource_error',
    setup: function (aegis: Aegis) {
      function resourceErrorListener(event: ErrorEvent) {
        event = event || window.event
        const target = event.target || event.srcElement

        validateParams(target, function (url, xpath) {
          reportResourceError(aegis, buildPayload(url, xpath, <Element>event.target))
        })
      }

      attachEvent('error', resourceErrorListener, true)

      cleanupTasks.add(function () {
        detachEvent('error', resourceErrorListener, true)
      })

      aegis.provide('reportResourceError', function (target: EventTarget | null, timestamp?: number) {
        validateParams(target, function (url, xpath) {
          reportResourceError(aegis, buildPayload(url, xpath, <Element>target, timestamp))
        })
      })
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function validateParams(target: EventTarget | null, callback: (url: string, xpath: string) => void) {
  if (!target || !(target instanceof Element)) {
    return
  }

  const url = getElementUrl(target)
  const xpath = htmlTreeStringify(target)

  if (!url || !xpath || isPageUrl(url)) {
    return
  }

  callback(url, xpath)
}

function reportResourceError(aegis: Aegis, resourceError: ResourceError) {
  aegis.report({
    type: 'resource_error',
    payload: resourceError
  })
}

function buildPayload(url: string, xpath: string, target: Element, timestamp?: number) {
  const timing = getResourceTiming(url)
  const payload: ResourceError = {
    url: url,
    xpath: xpath,
    timestamp: timestamp || now(),
    tagName: target.tagName.toLowerCase()
  }

  if (target.hasAttribute('integrity')) {
    payload.integrity = target.getAttribute('integrity') as string
  }

  if (timing instanceof PerformanceResourceTiming) {
    payload.timing = createTiming(timing)
  }

  return payload
}

/**
 * <img src="" /> this element src is location.href
 */
function isPageUrl(url: string) {
  return location.href === url
}

function getElementUrl(target: Element) {
  const href = (target as HTMLLinkElement).href

  if (href) {
    // this is a SVGAnimatedString
    if (typeof href === 'object' && 'baseVal' in href) {
      return (href as SVGAnimatedString).baseVal
    }

    return href
  }

  return (target as HTMLObjectElement).data || (target as HTMLImageElement).src
}

function getResourceTiming(name: string) {
  try {
    return performance.getEntriesByName(name)[0]
  } catch (error) {
    return null
  }
}
