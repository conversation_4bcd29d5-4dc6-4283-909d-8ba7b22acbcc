import json from '../../utils/json'
import uuid from '../../utils/uuid'
import Aegis from '../../core/Aegis'
import { isString } from '../../utils/is'
import { assign } from '../../utils/polyfills'
import type { Integration } from '../../core/Aegis'

import { aop, checkIsIgnored, createCleanupTask, now, events } from '../../utils'

type RequestReport = {
  type: 'api'
  immediate?: boolean
  payload: {
    url: string
    ok: boolean
    base: string
    query: string
    status: number
    method: string
    duration: number
    requestType: 'bridge'
    requestData?: string
    responseData?: string
  }
}

type RequestPayloadWithoutRequestType = Omit<RequestReport['payload'], 'requestType'>

declare module '../../core/Aegis' {
  export default interface Aegis {
    reportBridgeRequest: (payload: RequestPayloadWithoutRequestType) => void
    report<P extends RequestReport>(report: P): void
  }
}

interface RequestOptions extends Record<string, unknown> {
  reportAll?: boolean
  ignoreUrls?: (string | RegExp)[]
}

export default function (options?: RequestOptions): Integration {
  options = options || {}

  const cleanupTasks = createCleanupTask()
  const ignoreUrls = options.ignoreUrls || []
  const reportAll = 'reportAll' in options ? (options.reportAll as boolean) : false

  function reportApi(aegis: Aegis, payload: RequestPayloadWithoutRequestType, reportAll: boolean) {
    try {
      const isBase64Url = payload.url.indexOf('data:') === 0

      if (isBase64Url || checkIsIgnored(ignoreUrls, payload.url)) {
        return
      }

      if (payload.status === 200 && !reportAll) {
        return
      }

      aegis.report({
        type: 'api',
        payload: assign({}, payload, { requestType: 'bridge' }) as RequestReport['payload']
      })
    } catch (e) {
      /* empty */
    }
  }

  return {
    name: 'bridge',
    options: options,
    setup: function (aegis: Aegis) {
      bridgeReady(function () {
        const restoreBridgeRequest = interceptBridgeRequest(function (payload: RequestPayloadWithoutRequestType) {
          reportApi(aegis, payload, reportAll)
        })

        cleanupTasks.add(function () {
          restoreBridgeRequest()
        })
      })

      aegis.provide('reportBridgeRequest', function (payload: RequestPayloadWithoutRequestType) {
        reportApi(aegis, payload, true)
      })
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function bridgeReady(callback: () => void) {
  if (isBridgeReady()) {
    callback()
  } else {
    document.addEventListener('AEJSBridgeReady', function () {
      callback()
    })
  }
}

function isBridgeReady() {
  const isIOS = /iPad|iPod|iPhone|iOS/i.test(navigator.userAgent)
  const isBridgeAvailable = !!(window.AEJSBridge && window.AEJSBridge.dispatch)

  return isIOS ? !!(isBridgeAvailable && window.WebViewJavascriptBridge) : isBridgeAvailable
}

function isRequestOptions(options: Parameters<NonNullable<Window['AEJSBridge']>['dispatch']>[0]) {
  return !!(options.handlerName === 'action_network_request' && options.params && options.params.url && options.params.method)
}

function interceptBridgeRequest(reportCallback: (payload: RequestPayloadWithoutRequestType) => void) {
  return aop(window.AEJSBridge as NonNullable<Window['AEJSBridge']>, 'dispatch', function (original, options) {
    if (options[0] && isRequestOptions(options[0])) {
      const startTime = now()
      const requestId = uuid()
      const originalCallback = options[0].callback

      events.emit('bridgeRequest', requestId, options[0].params.method as string, options[0].params.url as string, options[0].params.data)

      options[0].callback = function (result) {
        events.emit('bridgeResponse', requestId, result)

        try {
          originalCallback(result)
        } finally {
          const responseOk = result.code === 0
          const urlParts = splitUrl(options[0].params.url as string)
          const payload: RequestPayloadWithoutRequestType = {
            ok: responseOk,
            base: urlParts[0],
            query: urlParts[1],
            duration: now() - startTime,
            status: responseOk ? 200 : 500,
            url: options[0].params.url as string,
            method: options[0].params.method as string
          }

          if (responseOk && result.data) {
            const data = parseResponseData(result.data)

            if (data && data.code !== 10001) {
              payload.ok = false
              payload.status = 500
              payload.responseData = data.message || 'Unknown server error'
              payload.requestData = normalizeRequestData(options[0].params.data)
            }
          } else {
            payload.responseData = result.msg || 'Unknown network error'
            payload.requestData = normalizeRequestData(options[0].params.data)
          }

          reportCallback(payload)
        }
      }
    }

    original.apply(window.AEJSBridge, options)
  })
}

function splitUrl(url: string) {
  if (!url || !url.trim()) {
    return ['', '']
  }

  const markIndex = url.indexOf('?')

  return markIndex === -1 ? [url, ''] : [url.slice(0, markIndex), url.slice(markIndex + 1)]
}

function normalizeRequestData(data: unknown) {
  return data ? (isString(data) ? data : json.stringify(data)) : undefined
}

function parseResponseData(data: string | Record<string, unknown>) {
  return (isString(data) ? json.parse(data) : data) as { code: number; data: unknown; message: string; success: boolean }
}
