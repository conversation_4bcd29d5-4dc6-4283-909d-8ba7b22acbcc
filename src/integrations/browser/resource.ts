import Aegis from '../../core/Aegis'
import { includes } from '../../utils/polyfills'
import type { Integration } from '../../core/Aegis'
import { createTiming } from '../../utils/resource'
import { attachPageLoadEvent } from '../../utils/browser/utils'
import { createCleanupTask, reportSDKErrors, checkIsIgnored } from '../../utils'

type ResourceTimingMetrics = ReturnType<typeof createTiming>

export type ResourceReport = {
  type: 'resource'
  immediate?: boolean
  payload: {
    data: ResourceTimingMetrics[]
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends ResourceReport>(report: P): void
  }
}

export type ignoreUrlTypes = (string | RegExp)[]

export interface ResourceOption extends Record<string, unknown> {
  batchInterval?: number
  ignoreTypes?: string[]
  ignoreUrls?: ignoreUrlTypes
}

export default function (options?: ResourceOption): Integration {
  options = options || {}

  const cleanupTasks = createCleanupTask()
  const ignoreUrls = options.ignoreUrls || []
  const batchInterval = options.batchInterval || 10000
  const ignoreTypes = ['xmlhttprequest', 'fetch', 'beacon'].concat(options.ignoreTypes || [])

  return {
    name: 'resource',
    options: options,
    setup: function (aegis: Aegis) {
      if (!supportsPerformance()) {
        return reportSDKErrors(new Error('performance or performance.getEntriesByType is not supported'))
      }

      cleanupTasks.add(
        attachPageLoadEvent(function () {
          reportPageLoadedResources(aegis, ignoreTypes, ignoreUrls)

          if (supportsPerformanceObserver()) {
            cleanupTasks.add(scheduleResourceReporting(aegis, batchInterval, ignoreTypes, ignoreUrls))
          } else {
            reportSDKErrors(new Error('PerformanceObserver is not supported'))
          }
        })
      )
    },
    tearDown: function () {
      cleanupTasks.dispose()
    }
  }
}

function reportPageLoadedResources(aegis: Aegis, ignoreTypes: string[], ignoreUrls: ignoreUrlTypes) {
  const resources = filterResource(performance.getEntriesByType('resource'), ignoreTypes, ignoreUrls)

  reportResourceMetrics(
    aegis,
    resources.map(function (resource) {
      return createTiming(resource)
    })
  )
}

function reportResourceMetrics(aegis: Aegis, resources: ResourceTimingMetrics[]) {
  if (resources.length === 0) {
    return
  }

  aegis.report({
    type: 'resource',
    payload: {
      data: resources
    }
  })
}

function scheduleResourceReporting(aegis: Aegis, batchInterval: number, ignoreTypes: string[], ignoreUrls: ignoreUrlTypes) {
  let batchCache: ResourceTimingMetrics[] = []

  const batchTimeoutID = setInterval(function () {
    reportResourceMetrics(aegis, batchCache.slice())
    batchCache = []
  }, batchInterval)

  const observer = new PerformanceObserver(function (list: PerformanceObserverEntryList) {
    let resources = list.getEntries().filter(function (entry) {
      return entry.entryType === 'resource'
    }) as PerformanceResourceTiming[]

    resources = filterResource(resources, ignoreTypes, ignoreUrls)

    for (let i = 0; i < resources.length; i++) {
      batchCache.push(createTiming(resources[i]))
    }
  })

  observer.observe({ entryTypes: ['resource'] })

  return function () {
    observer.disconnect()
    clearInterval(batchTimeoutID)
  }
}

function filterResource(resources: PerformanceResourceTiming[], ignoreTypes: string[], ignoreUrls: ignoreUrlTypes): PerformanceResourceTiming[] {
  return filterResourceByType(filterResourceByUrls(resources, ignoreUrls), ignoreTypes)
}

function filterResourceByType(resources: PerformanceResourceTiming[], ignoreTypes: string[]): PerformanceResourceTiming[] {
  return resources.filter(function (resource) {
    return !includes(ignoreTypes, resource.initiatorType)
  })
}

function filterResourceByUrls(resources: PerformanceResourceTiming[], ignoreUrls: ignoreUrlTypes): PerformanceResourceTiming[] {
  return resources.filter(function (resource) {
    return !checkIsIgnored(ignoreUrls, resource.name)
  })
}

function supportsPerformance() {
  return !!(window.performance && window.performance.getEntriesByType)
}

function supportsPerformanceObserver() {
  return 'PerformanceObserver' in window && typeof PerformanceObserver === 'function'
}
