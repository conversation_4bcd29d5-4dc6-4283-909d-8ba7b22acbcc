import json from '../utils/json'
import { isBrowser } from '../utils/is'
import AbstractTransport from './transport'
import { assign } from '../utils/polyfills'
import type { SendType } from '../core/Aegis'
import { attachPageUnloadEvent } from '../utils/browser/utils'
import { createCleanupTask, reportSDKErrors, request } from '../utils'

interface HttpTransportOptions {
  batch?: boolean
  retries?: number
  retryDelay?: number
  batchCount?: number
  batchInterval?: number
}

export default class HttpTransport extends AbstractTransport {
  private __batchCache: SendType[]
  private __isUnloadHandlerAttached: boolean
  private __batchTimeoutID: number | NodeJS.Timeout
  private __options: Required<HttpTransportOptions>
  private __cleanupTasks: ReturnType<typeof createCleanupTask>

  constructor(reportUrl: string, options?: HttpTransportOptions) {
    super(reportUrl)

    const ctx = this

    this.__batchCache = []
    this.__cleanupTasks = createCleanupTask()
    this.__batchTimeoutID = -1
    this.__isUnloadHandlerAttached = false
    this.__options = assign(
      {
        retries: 3,
        batch: false,
        batchCount: 10,
        retryDelay: 2000,
        batchInterval: 5000
      },
      options || {}
    )

    this.__cleanupTasks.add(function () {
      ctx.__batchCache = []
      ctx.__isUnloadHandlerAttached = false
      clearTimeout(ctx.__batchTimeoutID)
      ctx.__batchTimeoutID = -1
    })
  }

  send(data: SendType, immediate?: boolean): void {
    if (this.__options.batch && !immediate) {
      this.__doBatch(data)

      if (!this.__isUnloadHandlerAttached && isBrowser) {
        this.__attachUnloadHandlers()
      }
    } else {
      this.__doRequest([data])
    }
  }

  destroy() {
    this.__cleanupTasks.dispose()
  }

  private __attachUnloadHandlers() {
    this.__isUnloadHandlerAttached = true

    const ctx = this

    const sendBeaconData = function () {
      if (ctx.__batchCache.length > 0 && 'sendBeacon' in navigator) {
        navigator.sendBeacon(ctx.__reportUrl, json.stringify({ d: ctx.__batchCache }))
        ctx.__batchCache = []
      }
    }

    this.__cleanupTasks.add(attachPageUnloadEvent(sendBeaconData))
  }

  private __doBatch(data: SendType) {
    this.__batchCache.push(data)

    if (this.__batchCache.length === 1) {
      const ctx = this

      this.__batchTimeoutID = setTimeout(function () {
        ctx.__batchTimeoutID = -1
        ctx.__doBatchRequest()
      }, this.__options.batchInterval)
    }

    if (this.__batchCache.length >= this.__options.batchCount) {
      this.__doBatchRequest()
    }
  }

  private __doBatchRequest() {
    if (<number>this.__batchTimeoutID > 0) {
      clearTimeout(this.__batchTimeoutID)
      this.__batchTimeoutID = -1
    }

    const cacheCopy = this.__batchCache.slice()

    this.__batchCache = []

    this.__doRequest(cacheCopy)
  }

  private __doRequest(data: SendType[]) {
    const ctx = this

    request({
      url: ctx.__reportUrl,
      data: { d: data },
      retries: ctx.__options.retries,
      retryDelay: ctx.__options.retryDelay,
      callback: function (error) {
        if (error) {
          reportSDKErrors(new Error('HttpTransport: Failed to send data to ' + ctx.__reportUrl + ', message: ' + error.message))
        }
      }
    })
  }
}
