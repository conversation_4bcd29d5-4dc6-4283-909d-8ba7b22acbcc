import json from '../../utils/json'
import uuid from '../../utils/uuid'
import { SendType } from '../../core/Aegis'
import AbstractTransport from '../transport'
import { reportSDKErrors, URIEncode } from '../../utils'

export default class PixelTransport extends AbstractTransport {
  private __maxUrlLength = 2000

  constructor(reportUrl: string) {
    if (!reportUrl || !isValidGifUrl(reportUrl)) {
      throw new Error('Please use a 1x1 transparent GIF image as it is small enough.')
    }

    super(reportUrl)
  }

  send(data: SendType): void {
    const serializedData = json.stringify(data)

    if (serializedData.length > this.__maxUrlLength) {
      const chunks = splitIntoChunks(serializedData)

      this.__sendChunks(uuid(), chunks)
    } else {
      this.__send(buildUrl(this.__reportUrl, serializedData))
    }
  }

  __sendChunks(cid: string, chunks: string[]) {
    for (let i = 0; i < chunks.length; i++) {
      const isLastChunk = i === chunks.length - 1
      const url = buildUrl(this.__reportUrl, chunks[i]) + '&cid=' + cid + '&n=' + i + '&done=' + (isLastChunk ? '1' : '0')

      this.__send(url)
    }
  }

  __send(url: string) {
    const ctx = this
    const img = new Image()

    img.onerror = function () {
      reportSDKErrors(new Error('PixelTransport: Failed to send data to ' + ctx.__reportUrl))
    }

    img.src = url
  }
}

function buildUrl(baseUrl: string, serializedData: string) {
  const separator = baseUrl.indexOf('?') !== -1 ? '&' : '?'

  return baseUrl + separator + 'd=' + URIEncode(serializedData)
}

function isValidGifUrl(url: string) {
  const protocolRegex = /^https?:\/\//i
  const gifExtensionRegex = /\.gif($|\?)/i

  return gifExtensionRegex.test(url) && protocolRegex.test(url)
}

function splitIntoChunks(data: string) {
  const chunks = []
  const chunkSize = 1500

  for (let i = 0; i < data.length; i += chunkSize) {
    chunks.push(data.slice(i, i + chunkSize))
  }

  return chunks
}
