type EventMap = Record<string, (...args: anyType[]) => anyType>

type EventHandler<K extends keyof E, E extends EventMap> = (...args: [...Parameters<E[K]>, ReturnType<E[K]> | undefined]) => ReturnType<E[K]>

class Events<E extends EventMap> {
  private __events!: Record<keyof E, EventHandler<keyof E, E>[]>

  on<K extends keyof E>(eventName: K, callback?: EventHandler<K, E>) {
    if (callback) {
      if (!this.__events) {
        this.__events = {} as Record<keyof E, EventHandler<keyof E, E>[]>
      }

      if (!this.__events[eventName]) {
        this.__events[eventName] = []
      }

      this.__events[eventName].push(callback)
    }

    return this
  }

  off<K extends keyof E>(eventName?: K, callback?: EventHandler<K, E>) {
    if (!this.__events) {
      return this
    }

    if (!eventName) {
      this.__events = {} as Record<keyof E, <PERSON><PERSON><PERSON><PERSON><keyof E, E>[]>
      return this
    }

    if (!callback) {
      delete this.__events[eventName]
      return this
    }

    const tasks = this.__events[eventName]
    if (!tasks) {
      return this
    }

    for (let index = tasks.length - 1; index >= 0; index--) {
      if (tasks[index] === callback) {
        tasks.splice(index, 1)
      }
    }

    if (!tasks.length) {
      delete this.__events[eventName]
    }

    return this
  }

  contains<K extends keyof E>(eventName: K) {
    return !!(this.__events && this.__events[eventName])
  }

  emit<K extends keyof E>(eventName: K, ...args: Parameters<E[K]>) {
    let returned: ReturnType<E[K]> | undefined = undefined

    // reduce polyfill code generation
    args = <Parameters<E[K]>>(arguments.length > 1 ? Array.prototype.slice.call(arguments, 1) : [])

    if (this.__events && this.__events[eventName]) {
      const tasks = this.__events[eventName].slice()

      for (let i = 0; i < tasks.length; i++) {
        returned = tasks[i].apply(this, <Parameters<EventHandler<K, E>>>args.concat(returned))
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return returned
  }
}

export default Events
