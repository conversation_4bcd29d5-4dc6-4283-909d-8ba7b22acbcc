import { now } from '../utils'
import uuid from '../utils/uuid'
import { isBrowser } from '../utils/is'
import ConfigManager from './ConfigManager'
import type { CustomReport, SendType } from './Aegis'
import { BrowserTiming, GLOBAL_OBJ } from '../utils/worldwide'
import { SDK_NAME, SDK_VERSION, STORAGE_KEYS } from '../constants'

const getSessionId = (function () {
  let sessionId: string | undefined

  return function () {
    if (isBrowser) {
      let sid = sessionStorage.getItem(STORAGE_KEYS.CONFIG_SID)

      if (!sid) {
        sid = uuid()
        sessionStorage.setItem(STORAGE_KEYS.CONFIG_SID, sid)
      }

      return sid
    }

    return sessionId ? sessionId : (sessionId = uuid())
  }
})()

export function browserBuilder(data: CustomReport, configManager: ConfigManager) {
  const url = isBrowser ? location.href : ''
  const common: Record<string, string | number | undefined> = {
    url: url,
    os: 'Unknown',
    timestamp: now(),
    sdk_name: SDK_NAME,
    sid: getSessionId(),
    sdk_version: SDK_VERSION,
    id: configManager.getConfigValue('id'),
    env: configManager.getConfigValue('env'),
    uid: configManager.getConfigValue('uid'),
    vid: configManager.getConfigValue('viewId'),
    sample: configManager.getConfigValue('sample'),
    network: BrowserTiming.networkType || getNetworkType(),
    cid: configManager.getConfigValue('cid') || getBrowserCommonId(url)
  }

  const release = configManager.getConfigValue('release')
  const did = BrowserTiming.deviceId || configManager.getConfigValue('did')

  if (did) {
    common.did = did
  }

  if (release) {
    common.release = release
  }

  if (isBrowser) {
    common.os = navigator ? getBrowserOS(navigator.userAgent) : 'Unknown'
  }

  const result: SendType = {
    common,
    type: data.type,
    payload: data.payload
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}

export function nodejsBuilder(data: CustomReport, configManager: ConfigManager) {
  const common: Record<string, string | number | undefined> = {
    timestamp: now(),
    sdk_name: SDK_NAME,
    sdk_version: SDK_VERSION,
    id: configManager.getConfigValue('id'),
    uid: configManager.getConfigValue('uid')
  }

  const result: SendType = {
    common,
    type: data.type,
    payload: data.payload
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}

export function miniappBuilder(data: CustomReport, configManager: ConfigManager) {
  const common: Record<string, string | number | undefined> = {
    timestamp: now(),
    sdk_name: SDK_NAME,
    sdk_version: SDK_VERSION,
    id: configManager.getConfigValue('id'),
    uid: configManager.getConfigValue('uid')
  }

  const result: SendType = {
    common,
    type: data.type,
    payload: data.payload
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}

export function reactNativeBuilder(data: CustomReport, configManager: ConfigManager) {
  const common: Record<string, string | number | undefined> = {
    timestamp: now(),
    sdk_name: SDK_NAME,
    sdk_version: SDK_VERSION,
    id: configManager.getConfigValue('id'),
    uid: configManager.getConfigValue('uid')
  }

  const result: SendType = {
    common,
    type: data.type,
    payload: data.payload
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}

function getNetworkType() {
  const navigator = GLOBAL_OBJ.navigator

  if (navigator) {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection

    return connection ? connection.effectiveType || connection.type : 'unknown'
  } else {
    return 'unknown'
  }
}

export function getBrowserCommonId(url: string) {
  const urlWithoutQuery = url.replace(/\?[^#]*/, '')

  const hashIndex = urlWithoutQuery.indexOf('#')

  if (hashIndex !== -1) {
    const mainPart = urlWithoutQuery.slice(0, hashIndex)
    const hashPart = urlWithoutQuery.slice(hashIndex + 1)

    const cleanHashPart = hashPart.replace(/\?.*$/, '')

    return mainPart + '#' + cleanHashPart
  }

  return urlWithoutQuery
}

export function getBrowserOS(userAgent: string) {
  if (/Android/.test(userAgent)) return 'Android'
  if (/iPhone|iPad|iPod/.test(userAgent)) return 'IOS'
  if (/Mac OS X/.test(userAgent)) return 'MacOS'
  if (/Windows NT|Windows Phone/.test(userAgent)) return 'Windows'
  if (/CrOS/.test(userAgent)) return 'Chrome OS'
  if (/Ubuntu/.test(userAgent)) return 'Ubuntu'
  if (/FreeBSD/.test(userAgent)) return 'FreeBSD'
  if (/Linux/.test(userAgent)) return 'Linux'
  return 'Unknown'
}
