import Events from './Events'
import { isFunction } from '../utils/is'
import ConfigManager from './ConfigManager'
import { includes } from '../utils/polyfills'
import { SDK_NAME, SDK_VERSION } from '../constants'
import type { ConfigOptions } from './ConfigManager'
import { aop, createCleanupTask, events, noop, now, reportSDKErrors, request } from '../utils'

export type Falsy = false | null | undefined

export interface Integration {
  name: string
  isStub?: boolean
  tearDown?: () => void
  setup: (aegis: Aegis) => void
  options?: Record<string, unknown>
}

export type CustomReport = {
  type: 'custom'
  immediate?: boolean
  payload: {
    name: string
    metrics?: { [key: string]: number }
    categories?: { [key: string]: string }
  }
}

export type SendType = CustomReport & {
  common: Partial<{
    id: string
    cid?: string
    did?: string
    url?: string
    env?: string
    uid?: string
    sid?: string
    sample?: number
    release?: string
    network?: string
    timestamp: number
    sdk_name?: string
    sdk_version?: string
  }>
}

type AegisEventMap = {
  init: () => void
  beforeDestroy: () => void
  provide: (name: string) => void
  beforeSend: (data: SendType) => SendType | Falsy
  beforeBuild: (data: CustomReport) => CustomReport | Falsy
  beforeReport: (data: CustomReport) => CustomReport | Falsy
  beforeConfig: (configOptions: Partial<Omit<ConfigOptions, 'id'>>) => void
}

const instanceManager = {
  __instances: [] as Aegis[],
  add: function (instance: Aegis) {
    this.__instances.push(instance)
  },
  isMultiple: function () {
    return this.__instances.length > 1
  },
  remove: function (instance: Aegis) {
    this.__instances.splice(this.__instances.indexOf(instance), 1)
  },
  isLast: function (instance: Aegis) {
    return this.__instances[this.__instances.length - 1] === instance
  }
}

class Aegis extends Events<AegisEventMap> {
  [key: string]: unknown
  static [key: string]: unknown
  static transports: Record<string, unknown> = {}

  destroyed: boolean = false

  __inited: boolean = false
  __SDKReportUrl: string = ''
  __reportUrls: string[] = []
  __isSampleHit: boolean = false
  __configManager: ConfigManager
  __cleanupTasks = createCleanupTask()
  __pendingTimeouts: { [key: number]: boolean } = {}

  constructor(configOptions: ConfigOptions) {
    super()

    this.__SDKReportUrl = (configOptions || {}).SDKReportUrl || ''

    const startSDKErrorTracker = createSDKErrorTrack(this, this.__SDKReportUrl)
    this.__configManager = new ConfigManager(configOptions || {})

    const sample = this.__configManager.getConfigValue('sample')
    this.__isSampleHit = sample ? Math.random() < sample : this.__isSampleHit

    startSDKErrorTracker()
    pluginInstallHandler(this, this.__configManager.getConfigValue('integrations'))

    this.emit('init')
    this.__inited = true

    instanceManager.add(this)

    if (instanceManager.isMultiple()) {
      console.warn && console.warn('Multiple instances of [' + SDK_NAME + '] detected, Please call the `destroy` method in an orderly manner to destroy.')
    }
  }

  getIntegration(integrationName: string) {
    const integrations = this.__configManager.getConfigValue('integrations')

    if (!integrations || !integrations.length) {
      return
    }

    for (let i = 0; i < integrations.length; i++) {
      if (integrations[i].name === integrationName) {
        return integrations[i]
      }
    }
  }

  addReportUrl(url: string) {
    this.__reportUrls.push(url)
  }

  getReportUrls() {
    const transports = this.__configManager.getConfigValue('transports')
    const urls: string[] = this.__SDKReportUrl ? [this.__SDKReportUrl].concat(this.__reportUrls) : this.__reportUrls

    if (!(transports && transports.length)) {
      return urls
    }

    transports.forEach(function (transport) {
      if (isFunction(transport.getReportUrl)) {
        const url = transport.getReportUrl()

        url && urls.push(url)
      }
    })

    return urls
  }

  setConfig(configOptions: Partial<Omit<ConfigOptions, 'id' | 'integrations' | 'SDKReportUrl' | 'transports'>>) {
    this.emit('beforeConfig', configOptions)
    this.__configManager.updateConfig(configOptions)
  }

  provide<T>(name: string, value: T) {
    this[name] = value
    this.emit('provide', name)
  }

  report(data: CustomReport) {
    if (this.destroyed) {
      return
    }

    const self = this

    const timeoutId = setTimeout(function () {
      delete self.__pendingTimeouts[timeoutId as unknown as number]

      const result = self.contains('beforeReport') ? self.emit('beforeReport', data) : data

      if (!result) {
        return
      }

      self.build(result)
    }, 0)

    this.__pendingTimeouts[timeoutId as unknown as number] = true
  }

  build(data: CustomReport) {
    if (this.destroyed) {
      return
    }

    if (!this.__isSampleHit) {
      return
    }

    const builder = this.__configManager.getConfigValue('builder')
    const result = this.contains('beforeBuild') ? this.emit('beforeBuild', data) : data

    if (!result || !builder) {
      return
    }

    const built = builder(result, this.__configManager)

    if (!built) {
      return
    }

    this.send(built)
  }

  send(data: SendType) {
    if (this.destroyed) {
      return
    }

    const transports = this.__configManager.getConfigValue('transports')
    const result = this.contains('beforeSend') ? this.emit('beforeSend', data) : data

    if (!result || !(transports && transports.length)) {
      return
    }

    for (let i = 0; i < transports.length; i++) {
      if (isFunction(transports[i].send)) {
        transports[i].send(result, result.immediate)
      }
    }
  }

  destroy() {
    if (this.destroyed) {
      return
    }

    if (!instanceManager.isLast(this)) {
      return console.error && console.error('Destroy failed, the instance is not the last instance, please call the `destroy` method in an orderly manner to destroy.')
    }

    this.emit('beforeDestroy')

    this.__inited = false
    this.destroyed = true

    const transports = this.__configManager.getConfigValue('transports')

    if (transports && transports.length) {
      for (let i = 0; i < transports.length; i++) {
        if (isFunction(transports[i].destroy)) {
          transports[i].destroy!()
        }
      }
    }

    for (const timeoutId in this.__pendingTimeouts) {
      clearTimeout(timeoutId as unknown as number)
      delete this.__pendingTimeouts[timeoutId as unknown as number]
    }

    instanceManager.remove(this)
    this.__cleanupTasks.dispose()
    this.off()
  }
}

function pluginInstallHandler(aegis: Aegis, plugins: ConfigOptions['integrations']) {
  const installed: string[] = []

  plugins = plugins || []

  for (let i = 0; i < plugins.length; i++) {
    if (plugins[i] && !includes(installed, plugins[i].name) && !plugins[i].isStub) {
      installed.push(plugins[i].name)

      try {
        plugins[i].setup(aegis)
        plugins[i].tearDown && aegis.on('beforeDestroy', plugins[i].tearDown)
      } catch (error) {
        reportSDKErrors(new Error('[' + SDK_NAME + '] Error in integration "' + plugins[i].name + '": ' + (error as Error).message))
      }
    } else if (plugins[i] && plugins[i].isStub) {
      console.warn && console.warn('[' + SDK_NAME + '] Mock integration "' + plugins[i].name + '" cannot be installed as a real plugin.')
    } else {
      throw new Error('[' + SDK_NAME + '] Integration "' + (plugins[i] ? plugins[i].name : 'unknown') + '" already installed.')
    }
  }
}

function createSDKErrorTrack(aegis: Aegis, reportUrl?: string) {
  let id = ''
  let ready = false
  let errors: Error[] = []
  let uid: string | undefined = ''
  let handleError: (err: Error) => void = noop

  if (reportUrl) {
    handleError = function (err: Error) {
      if (ready) {
        request({
          url: reportUrl,
          data: { d: [getSDKErrorPayload(id, uid!, err.message)] },
          callback: noop
        })
      } else {
        errors.push(err)
      }
    }

    events.on('error', handleError)

    aegis.__cleanupTasks.add(function () {
      events.off('error', handleError)
    })
  }

  return function () {
    ready = true
    id = aegis.__configManager.getConfigValue('id')
    uid = aegis.__configManager.getConfigValue('uid')

    for (let i = 0; i < errors.length; i++) {
      handleError(errors[i])
    }

    errors = []
  }
}

function getSDKErrorPayload(id: string, uid: string, message: string) {
  return {
    type: 'sdk',
    payload: {
      message: message
    },
    common: {
      id: id,
      uid: uid,
      timestamp: now(),
      url: location.href,
      sdk_version: SDK_VERSION
    }
  }
}

aop(Aegis.prototype, 'on', function (originalOn, args) {
  const callback = args[1]
  const eventName = args[0]

  if (eventName === 'init' && this.__inited) {
    ;(callback as AegisEventMap['init']).call(this)
  } else if (eventName === 'beforeDestroy' && this.destroyed) {
    ;(callback as AegisEventMap['beforeDestroy']).call(this)
  } else {
    originalOn.apply(this, args)
  }

  return this
})

/**
 * Solve the scenario where calling a plugin reports an error when the plugin source code is not loaded
 */
;(function presetStaticMethods() {
  const staticTransports = ['Console', 'Pixel', 'Http']
  const staticMethods = ['precollect', 'pageview', 'blankScreen', 'action', 'device', 'api', 'bridge', 'resource', 'resourceError', 'jsError', 'perf', 'largePictureInspect', 'feature']

  for (let i = 0; i < staticMethods.length; i++) {
    const methodName = staticMethods[i]

    Aegis[methodName] = function () {
      console.warn && console.warn('[' + SDK_NAME + '] ' + methodName + ' does not exist')

      return {
        isStub: true,
        name: methodName
      }
    }
  }

  for (let i = 0; i < staticTransports.length; i++) {
    const transportName = staticTransports[i]

    Aegis.transports[transportName] = function () {
      console.warn && console.warn('[' + SDK_NAME + '] ' + transportName + ' does not exist')

      return {
        isStub: true,
        name: transportName
      }
    }
  }
})()

export default Aegis
