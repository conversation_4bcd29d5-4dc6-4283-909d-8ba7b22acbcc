import uuid from '../utils/uuid'
import store from '../utils/store'
import { getViewId } from '../utils'
import { assign } from '../utils/polyfills'
import { STORAGE_KEYS } from '../constants'
import AbstractTransport from '../transports/transport'
import type { CustomReport, SendType, Falsy, Integration } from './Aegis'
import { isObject, isNodejs, isMiniapp, isReactNative } from '../utils/is'
import { browserBuilder, miniappBuilder, nodejsBuilder, reactNativeBuilder } from './builder'

export interface ConfigOptions {
  /**
   * 监控应用ID - 由后端生成
   */
  id: string
  /**
   * 用户ID，用于标识访问用户，可手动配置，默认由SDK随机生成
   */
  uid?: string
  /**
   * 设备ID
   */
  did?: string
  /**
   * 通用ID，用于关联各类日志数据
   * 在浏览器环境中，一般默认是页面URL
   */
  cid?: string
  /**
   * 应用版本号
   */
  release?: string
  /**
   * 日志采样配置，值为0~1的整数
   */
  sample?: number
  /**
   * 环境字段
   * prod: 线上环境
   * gray: 灰度环境
   * pre: 预发环境
   * test: 测试环境
   */
  env?: 'prod' | 'gray' | 'pre' | 'test'
  /**
   * 插件配置
   */
  integrations?: Array<Integration>
  /**
   * 自定义上报前的数据处理
   */
  builder?: (data: CustomReport, configManager: ConfigManager) => SendType | Falsy
  /**
   * 自定义上报方式
   */
  transports?: AbstractTransport[]
  /**
   * SDK 自身错误上报地址
   */
  SDKReportUrl?: string
  /**
   * 视图ID
   */
  viewId?: string
}

class ConfigManager {
  private __configOptions: ConfigOptions

  private __getDefaultConfigOptions(): ConfigOptions {
    return {
      id: '',
      uid: '',
      did: '',
      sample: 1,
      env: 'prod',
      release: '',
      transports: [],
      integrations: [],
      SDKReportUrl: '',
      viewId: getViewId()
    }
  }

  private __getBuilder() {
    const platform = this.getPlatform()

    if (isNodejs(platform)) {
      return nodejsBuilder
    } else if (isMiniapp(platform)) {
      return miniappBuilder
    } else if (isReactNative(platform)) {
      return reactNativeBuilder
    } else {
      return browserBuilder
    }
  }

  constructor(userConfig: ConfigOptions) {
    this.__configOptions = assign({}, this.__getDefaultConfigOptions(), this.isConfigOptionsValid(userConfig) ? userConfig : {})

    this.__configOptions.builder = this.__configOptions.builder || this.__getBuilder()

    if (!this.__configOptions.uid) {
      const uid = store.getItemSync<string>(STORAGE_KEYS.CONFIG_UID)

      if (uid) {
        this.__configOptions.uid = uid
      } else {
        this.__configOptions.uid = uuid()
        store.setItemSync(STORAGE_KEYS.CONFIG_UID, this.__configOptions.uid)
      }
    }
  }

  getPlatform() {
    const platformCode = this.__configOptions.id ? this.__configOptions.id.slice(0, 2) : ''
    const platforms: Record<string, string> = {
      '00': 'web',
      '01': 'nodejs',
      '02': 'miniapp',
      '03': 'rn'
    }

    return platforms[platformCode] || 'web'
  }

  updateConfig(userConfig: Partial<ConfigOptions>) {
    if (!isObject(userConfig)) {
      return
    }

    if (userConfig.id) {
      delete userConfig.id
    }

    this.__configOptions = assign({}, this.__configOptions, userConfig)
  }

  addTransport(transport: AbstractTransport) {
    if (this.__configOptions.transports && transport instanceof AbstractTransport) {
      this.__configOptions.transports.push(transport)
    }
  }

  removeTransport(transport: AbstractTransport) {
    if (!(this.__configOptions.transports && transport instanceof AbstractTransport)) {
      return
    }

    const index = this.__configOptions.transports.indexOf(transport)

    if (index === -1) {
      return
    }

    this.__configOptions.transports.splice(index, 1)
  }

  getConfigValue<K extends keyof ConfigOptions>(key: K): ConfigOptions[K] {
    return this.__configOptions[key]
  }

  isConfigOptionsValid(options: Partial<ConfigOptions>) {
    return isObject(options) && 'id' in options
  }
}

export default ConfigManager
