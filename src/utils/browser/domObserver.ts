import { createCleanupTask } from '../'

export interface MockedMutationRecord {
  target: Node
  isMocked: true
  oldValue: null
  type: 'childList'
  nextSibling: null
  attributeName: null
  addedNodes: NodeList
  previousSibling: null
  removedNodes: NodeList
  attributeNamespace: null
}

type ExtendedMutationRecord = MockedMutationRecord | MutationRecord

export function createDomObserver(target: Node, onMutation: (mutationsList: ExtendedMutationRecord[]) => void) {
  if (!target) {
    throw new Error('Target node is required')
  }

  const cleanupTasks = createCleanupTask()

  if (window.MutationObserver) {
    const observer = new MutationObserver(function (mutationsList) {
      onMutation(mutationsList as ExtendedMutationRecord[])
    })

    observer.observe(target, {
      subtree: true,
      childList: true
    })

    cleanupTasks.add(function () {
      observer.disconnect()
    })
  } else {
    let timer: NodeJS.Timeout | null = null

    const handleMutation = function (event: Event) {
      if (timer) {
        return
      }

      timer = setTimeout(function () {
        timer = null
      }, 16)

      onMutation([
        {
          isMocked: true,
          oldValue: null,
          type: 'childList',
          nextSibling: null,
          attributeName: null,
          previousSibling: null,
          attributeNamespace: null,
          target: event.target as Node,
          addedNodes: [] as unknown as NodeList,
          removedNodes: [] as unknown as NodeList
        }
      ])
    }

    target.addEventListener('DOMNodeInserted', handleMutation, true)
    target.addEventListener('DOMNodeRemoved', handleMutation, true)

    cleanupTasks.add(function () {
      target.removeEventListener('DOMNodeInserted', handleMutation, true)
      target.removeEventListener('DOMNodeRemoved', handleMutation, true)
    })
  }

  return {
    dispose: function () {
      cleanupTasks.dispose()
    }
  }
}
