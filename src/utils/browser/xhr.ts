/* eslint-disable */
import json from '../json'

export interface XHRRequestOptions<T> {
  url: string
  data: T
  retries?: number
  retryDelay?: number
  method?: 'POST' | 'GET' | 'PUT' | 'DELETE'
  callback: (err: Error | null, responseText?: string) => void
}

export default function xhrRequest<T>(options: XHRRequestOptions<T>) {
  const url = options.url
  const data = options.data
  const callback = options.callback
  const method = options.method || 'POST'
  const retryDelay = options.retryDelay || 2000
  const retries = options.retries || options.retries === 0 ? options.retries : 3
  const XHR = typeof window['__AEGIS_XHR__'] !== 'undefined' ? window['__AEGIS_XHR__'] : XMLHttpRequest

  const request = new XHR()

  request.open(method, url, true)
  request.setRequestHeader('Content-Type', 'application/json;charset=UTF-8')

  function retryCallback() {
    let retryUrl = url

    if (url.indexOf('retry=') === -1) {
      retryUrl = url + (url.indexOf('?') === -1 ? '?' : '&') + 'retry=' + retries
    } else {
      retryUrl = url.replace(/retry=\d+/, 'retry=' + retries)
    }

    xhrRequest({
      data: data,
      url: retryUrl,
      method: method,
      callback: callback,
      retries: retries - 1,
      retryDelay: retryDelay
    })
  }

  request.onreadystatechange = function () {
    if (request.readyState === 4) {
      callback(null, request.responseText)

      // if (request.status >= 200 && request.status < 300) {
      //   callback(null, request.responseText)
      // } else {
      //   retryHandler(retries!, retryDelay!, retryCallback, function () {
      //     callback(new Error('Request failed with status: ' + request.status))
      //   })
      // }
    }
  }

  request.send(json.stringify(data))
}

function retryHandler(retries: number, retryDelay: number, retryCallback: () => void, errorCallback: () => void) {
  if (retries > 0) {
    setTimeout(retryCallback, retryDelay)
  } else {
    errorCallback()
  }
}
