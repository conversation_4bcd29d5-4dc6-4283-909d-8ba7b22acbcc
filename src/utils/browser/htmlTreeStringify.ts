type SimpleNode = {
  parentNode: SimpleNode
} | null

/**
 * ref: https://github.com/getsentry/sentry-javascript/blob/5.30.0/packages/utils/src/browser.ts
 */
export default function htmlTreeStringify(elem: unknown) {
  if (!elem) {
    return '<unknown>'
  }

  try {
    let len = 0
    let height = 0
    const out = []
    const separator = ' > '
    const MAX_TRAVERSE_HEIGHT = 5
    const sepLength = separator.length
    const DEFAULT_MAX_STRING_LENGTH = 80
    let currentElem = elem as SimpleNode

    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {
      const nextStr = _htmlElementAsString(currentElem)

      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= DEFAULT_MAX_STRING_LENGTH)) {
        break
      }

      out.push(nextStr)

      len += nextStr.length
      currentElem = currentElem.parentNode
    }

    return out.reverse().join(separator)
  } catch (err) {
    return '<unknown>'
  }
}

function _htmlElementAsString(el: unknown): string {
  const elem = el as {
    tagName?: string
    id?: string
    className?: string
    getAttribute(key: string): string
  }

  const out = []

  if (!elem || !elem.tagName) {
    return ''
  }

  out.push(elem.tagName.toLowerCase())

  if (elem.id) {
    out.push('#' + elem.id)
  }

  const className = elem.className
  if (className && typeof className === 'string') {
    const classes = className.split(/\s+/)
    for (let i = 0; i < classes.length; i++) {
      out.push('.' + classes[i])
    }
  }

  const allowedAttrs = ['type', 'name', 'title', 'alt']
  for (let i = 0; i < allowedAttrs.length; i++) {
    const key = allowedAttrs[i]
    const attr = elem.getAttribute(key)

    if (attr) {
      out.push('[' + key + '="' + attr + '"]')
    }
  }
  return out.join('')
}
