import { toArray } from '../polyfills'

export const attachPageUnloadEvent = function (callback: () => void) {
  let hasBeenCalled = false
  const unloadEvents: ['unload', 'pagehide', 'beforeunload'] = ['unload', 'pagehide', 'beforeunload']

  const wrappedCallback = function () {
    if (!hasBeenCalled) {
      hasBeenCalled = true
      callback()
    }
  }

  for (let i = 0; i < unloadEvents.length; i++) {
    attachEvent(unloadEvents[i], wrappedCallback)
  }

  return function detachPageUnloadEvent() {
    for (let i = 0; i < unloadEvents.length; i++) {
      detachEvent(unloadEvents[i], wrappedCallback)
    }
  }
}

export const attachPageLoadEvent = function (callback: () => void) {
  attachEvent('load', callback, true)

  return function detachPageLoadEvent() {
    detachEvent('load', callback, true)
  }
}

export const attachEvent = function <K extends keyof WindowEventMap>(type: K, listener: (this: Window, ev: WindowEventMap[K]) => anyType, options?: boolean | AddEventListenerOptions) {
  window.addEventListener(type, listener, options)
}

export const detachEvent = function <K extends keyof WindowEventMap>(type: K, listener: (this: Window, ev: WindowEventMap[K]) => anyType, options?: boolean | EventListenerOptions) {
  window.removeEventListener(type, listener, options)
}

export const setObjectProperty = function <T extends object, K extends keyof T>(obj: T, path: K, value: T[K]) {
  const desc = Object.getOwnPropertyDescriptor(obj, path)
  if (desc && desc.writable) {
    obj[path] = value
  } else {
    console.warn(obj, `[setObjectProperty] property ${String(path)} is not writable`)
  }
}

let detectVueTimer: NodeJS.Timeout | null = null
let detectVueCallbacks: ((vue: Vue2Instance | Vue3Instance, version: number) => void)[] = []

export const detectVueFramework = function (callback: (vue: Vue2Instance | Vue3Instance, version: number) => void) {
  detectVueCallbacks.push(callback)

  if (detectVueTimer === null) {
    detectVueTimer = setTimeout(function () {
      const vueInstance = findVueInstance()

      if (vueInstance) {
        const version = vueInstance.version ? parseInt(vueInstance.version, 10) : 0

        detectVueCallbacks.forEach(function (callback) {
          callback(vueInstance.vue, version)
        })
      }

      detectVueTimer = null
      detectVueCallbacks = []
    }, 4000)
  }

  return function () {
    if (detectVueTimer !== null) {
      clearTimeout(detectVueTimer)
      detectVueTimer = null
      detectVueCallbacks = []
    }
  }
}

export const findVueInstance = function (): { vue: Vue2Instance | Vue3Instance; version: string } | null {
  const elements = toArray(document.getElementsByTagName('*'))
  const element = elements.filter(function (item) {
    return item.__vue__ || item.__vue_app__
  })[0]

  if (element && element.__vue__) {
    return getVue2Instance(element.__vue__)
  } else if (element && element.__vue_app__) {
    return getVue3Instance(element.__vue_app__)
  }

  return null
}

export const getVue2Instance = function (vue: Vue2Instance): { vue: Vue2Instance; version: string } {
  const options = vue.$root && vue.$root.$options
  const base = options && options._base
  const version = base && base.version ? base.version : ''

  return {
    vue,
    version
  }
}

export const getVue3Instance = function (vue: Vue3Instance): { vue: Vue3Instance; version: string } {
  const version = vue && vue.version ? vue.version : ''

  return {
    vue,
    version
  }
}

export const parseBackgroundImageUrls = function (backgroundImage: string, baseURI: string) {
  if (!backgroundImage || backgroundImage === 'none') {
    return []
  }

  let match
  const urls: string[] = []
  const urlRegex = /url\(['"]?([^'"()]*)['"]?\)/gi

  while ((match = urlRegex.exec(backgroundImage)) !== null) {
    const currentMatch = match[1]

    if (!currentMatch) {
      continue
    }

    let url = currentMatch.trim()

    if (!url) {
      continue
    }

    if (/^data:/i.test(url)) {
      continue
    }

    url = toAbsoluteURL(match[1], baseURI)

    if (urls.indexOf(url) === -1) {
      urls.push(url)
    }
  }

  return urls
}

export function toAbsoluteURL(url: string, base: string) {
  if (url.toLowerCase().indexOf('http:/' + '/') === 0 || url.toLowerCase().indexOf('https:/' + '/') === 0) {
    return url
  }

  if (supportsURL()) {
    return new URL(url, base).href
  }

  const baseParts = base.split('/')
  const protocol = baseParts[0]
  const host = baseParts[2]

  if (url.charAt(0) === '/') {
    return protocol + '/' + '/' + host + url
  }

  baseParts.pop()

  const urlParts = url.split('/')

  for (let i = 0; i < urlParts.length; i++) {
    if (urlParts[i] === '..' && baseParts.length > 3) {
      baseParts.pop()
    } else if (urlParts[i] !== '.') {
      baseParts.push(urlParts[i])
    }
  }

  return baseParts.join('/')
}

function supportsURL() {
  return !!window.URL
}

export function isInvalidWindow(): boolean {
  return window.innerWidth <= 5 || window.innerHeight <= 5
}
