import { createCleanupTask, aop, noop } from '../'
import { attachEvent, detachEvent } from './utils'

interface RouteObserverOptions {
  mode?: 'history' | 'hash'
  onPushState?: () => void
  onHashChange?: () => void
  onReplaceState?: () => void
  onHistoryChange?: () => void
}

export function createRouteObserver(options?: RouteObserverOptions) {
  options = options || {}

  const mode = options.mode || 'history'
  const onPushState = options.onPushState || noop
  const onHashChange = options.onHashChange || noop
  const onReplaceState = options.onReplaceState || noop
  const onHistoryChange = options.onHistoryChange || noop

  const cleanupTasks = createCleanupTask()

  if (mode === 'hash') {
    attachEvent('hashchange', onHashChange, true)

    cleanupTasks.add(function () {
      detachEvent('hashchange', onHashChange, true)
    })
  } else {
    attachEvent('popstate', onHistoryChange, true)

    cleanupTasks.add(function () {
      detachEvent('popstate', onHistoryChange, true)
    })
  }

  if (supportsHistoryAPI()) {
    const restorePushState = aop(
      history,
      'pushState',
      function (original, args) {
        try {
          original.apply(history, args)
        } finally {
          onPushState()
        }
      },
      true
    )

    const restoreReplaceState = aop(
      history,
      'replaceState',
      function (original, args) {
        try {
          original.apply(history, args)
        } finally {
          wrapCallback(onReplaceState)()
        }
      },
      true
    )

    cleanupTasks.add(function () {
      restorePushState()
      restoreReplaceState()
    })
  }

  return {
    dispose: function () {
      cleanupTasks.dispose()
    }
  }
}

let lastURL = location.href

function wrapCallback(callback: () => void) {
  return function () {
    const currentURL = location.href

    if (lastURL === currentURL) {
      return
    }

    lastURL = currentURL
    callback()
  }
}

function supportsHistoryAPI() {
  return !!(window.history && window.history.pushState)
}
