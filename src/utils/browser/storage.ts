import { isBrowser } from '../../utils/is'

const storage = {
  available: (function () {
    if (!isBrowser) {
      return false
    }

    try {
      localStorage.setItem('_t', '1')
      localStorage.removeItem('_t')
      return true
    } catch (e) {
      /* istanbul ignore next -- @preserve */
      return false
    }
  })(),
  get: function (key: string) {
    if (!this.available) {
      return undefined
    }

    const value = localStorage.getItem(key)

    if (value === null) {
      return undefined
    }

    return value
  },
  set: function (key: string, value: string) {
    if (this.available) {
      localStorage.setItem(key, value)
    }
  }
}

export default storage
