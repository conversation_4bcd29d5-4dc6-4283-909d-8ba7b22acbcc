/* eslint-disable no-useless-escape */

interface DeviceMatcher {
  regex: RegExp
  brand: string
}

export const DEVICE_MATCHERS: DeviceMatcher[] = [
  // Samsung
  { regex: /\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i, brand: 'SAMSUNG' },
  { regex: /\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i, brand: 'SAMSUNG' },
  { regex: /samsung[- ]([-\w]+)/i, brand: 'SAMSUNG' },
  { regex: /sec-(sgh\w+)/i, brand: 'SAMSUNG' },

  // Apple
  { regex: /(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i, brand: 'APPLE' },
  { regex: /\((ipad);[-\w\),; ]+apple/i, brand: 'APPLE' },
  { regex: /applecoremedia\/[\w\.]+ \((ipad)/i, brand: 'APPLE' },
  { regex: /\b(ipad)\d\d?,\d\d?[;\]].+ios/i, brand: 'APPLE' },
  { regex: /(macintosh);/i, brand: 'APPLE' },

  // Huawei
  { regex: /\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i, brand: 'HUAWEI' },
  { regex: /(?:huawei|honor)([-\w ]+)[;\)]/i, brand: 'HUAWEI' },
  { regex: /\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i, brand: 'HUAWEI' },

  // Xiaomi
  { regex: /\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i, brand: 'XIAOMI' },
  { regex: /\b; (\w+) build\/hm\1/i, brand: 'XIAOMI' },
  { regex: /\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i, brand: 'XIAOMI' },
  { regex: /\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i, brand: 'XIAOMI' },
  { regex: /oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i, brand: 'XIAOMI' },
  { regex: /\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i, brand: 'XIAOMI' },
  { regex: /oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i, brand: 'XIAOMI' },
  { regex: /\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i, brand: 'XIAOMI' },
  { regex: /(mitv-\w{5}) bui/i, brand: 'XIAOMI' },

  // OPPO
  { regex: /; (\w+) bui.+ oppo/i, brand: 'OPPO' },
  { regex: /\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i, brand: 'OPPO' },
  { regex: /\b(opd2\d{3}a?) bui/i, brand: 'OPPO' },

  // Vivo
  { regex: /vivo (\w+)(?: bui|\))/i, brand: 'VIVO' },
  { regex: /\b(v[12]\d{3}\w?[at])(?: bui|;)/i, brand: 'VIVO' },

  // Realme
  { regex: /\b(rmx[1-3]\d{3})(?: bui|;|\))/i, brand: 'REALME' },

  // Motorola
  { regex: /\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i, brand: 'MOTOROLA' },
  { regex: /\bmot(?:orola)?[- ](\w*)/i, brand: 'MOTOROLA' },
  { regex: /((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i, brand: 'MOTOROLA' },
  { regex: /\b(mz60\d|xoom[2 ]{0,2}) build\//i, brand: 'MOTOROLA' },

  // LG
  { regex: /((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i, brand: 'LG' },
  { regex: /(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i, brand: 'LG' },
  { regex: /\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i, brand: 'LG' },
  { regex: /\blg-?([\d\w]+) bui/i, brand: 'LG' },

  // Lenovo
  { regex: /(ideatab[-\w ]+)/i, brand: 'LENOVO' },
  { regex: /lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i, brand: 'LENOVO' },

  // Nokia
  { regex: /(?:maemo|nokia).*(n900|lumia \d+)/i, brand: 'NOKIA' },

  // Google
  { regex: /droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i, brand: 'GOOGLE' },
  { regex: /(pixel c)\b/i, brand: 'GOOGLE' },
  { regex: /crkey/i, brand: 'GOOGLE' },
  { regex: /droid.+; (glass) \d/i, brand: 'GOOGLE' },

  // Sony
  { regex: /droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i, brand: 'SONY' },
  { regex: /sony tablet [ps]/i, brand: 'SONY' },
  { regex: /\b(?:sony)?sgp\w+(?: bui|\))/i, brand: 'SONY' },
  { regex: /(bravia[\w ]+)( bui|\))/i, brand: 'SONY' },
  { regex: /(playstation \w+)/i, brand: 'SONY' },

  // OnePlus
  { regex: /(one)?(?:plus)? (a\d0\d\d)(?: b|\))/i, brand: 'ONEPLUS' },

  // Amazon
  { regex: /(alexa)webm/i, brand: 'AMAZON' },
  { regex: /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i, brand: 'AMAZON' },
  { regex: /(kf[a-z]+)( bui|\)).+silk\//i, brand: 'AMAZON' },
  { regex: /((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i, brand: 'AMAZON' },
  { regex: /droid.+aft(\w+)( bui|\))/i, brand: 'AMAZON' },
  { regex: /(aeobc)\b/i, brand: 'AMAZON' },

  // Microsoft
  { regex: /(surface duo)/i, brand: 'MICROSOFT' },
  { regex: /(kin\.[onetw]{3})/i, brand: 'MICROSOFT' },
  { regex: /\b(xbox(?: one)?(?!; xbox))[\); ]/i, brand: 'MICROSOFT' },

  // Asus
  { regex: /(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i, brand: 'ASUS' },

  // ZTE
  { regex: /(zte)[- ]([\w ]+?)(?: bui|\/|\))/i, brand: 'ZTE' },
  { regex: /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i, brand: 'ALCATEL' },

  // Meizu
  { regex: /(meizu)[\w ]+ bui/i, brand: 'MEIZU' },

  // Ulefone
  { regex: /; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i, brand: 'ULEFONE' },

  { regex: /(hp) ([\w ]+\w)/i, brand: 'HP' },
  { regex: /(asus)-?(\w+)/i, brand: 'ASUS' },
  { regex: /(microsoft); (lumia[\w ]+)/i, brand: 'MICROSOFT' },
  { regex: /(lenovo)[-_ ]?([-\w]+)/i, brand: 'LENOVO' },
  { regex: /(jolla)/i, brand: 'Jolla' },
  { regex: /(oppo) ?([\w ]+) bui/i, brand: 'OPPO' },

  // TCL
  { regex: /tcl (xess p17aa)/i, brand: 'TCL' },
  { regex: /droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i, brand: 'TCL' },
  {
    regex:
      /droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i,
    brand: 'TCL'
  },

  // itel
  { regex: /(itel) ((\w+))/i, brand: 'ITEL' },

  // Acer
  { regex: /droid.+; ([ab][1-7]-?[0178a]\d\d?)/i, brand: 'ACER' }
]
