import { URIEncode } from '..'
import { isBrowser } from '../../utils/is'

const cookie = {
  available: (function () {
    if (!isBrowser) {
      return false
    }

    document.cookie = '_t=1; path=/;'
    /* istanbul ignore next -- @preserve */
    const isCookieSet = document.cookie.indexOf('_t=1') != -1 ? true : false
    document.cookie = '_t=1; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

    return isCookieSet
  })(),
  get: function (key: string) {
    if (!this.available) {
      return undefined
    }

    const cookieStr = document.cookie

    if (cookieStr === '') {
      return undefined
    }

    const cookies = cookieStr.split(';')

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].split('=')

      if (cookie[0].trim() === key) {
        return decodeURIComponent(cookie[1])
      }
    }

    return undefined
  },
  set: function (key: string, value: string, expires: Date, path: string = '/') {
    if (this.available) {
      document.cookie = `${URIEncode(key)}=${URIEncode(value)}; expires=${expires.toUTCString()}; path=${path};`
    }
  }
}

export default cookie
