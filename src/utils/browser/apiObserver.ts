import { isFunction } from '../is'
import { createCleanupTask, aop } from '../../utils'

interface ApiObserverOptions {
  fetchInterceptor?: (original: typeof fetch, args: Parameters<typeof fetch>) => Promise<Response>
  xhrInterceptor?: (original: XMLHttpRequest['open'], args: Parameters<XMLHttpRequest['open']>) => void
}

export function createApiObserver(options?: ApiObserverOptions) {
  options = options || {}

  const fetchInterceptor =
    options.fetchInterceptor ||
    function (original, args) {
      return original.apply(window, args)
    }

  const xhrInterceptor =
    options.xhrInterceptor ||
    function (this: XMLHttpRequest, original, args) {
      original.apply(this, args)
    }

  const cleanupTasks = createCleanupTask()

  if (supportsNativeFetch()) {
    const restoreFetch = aop(
      window,
      'fetch',
      function (original, args) {
        return fetchInterceptor(original, args)
      },
      true
    )

    cleanupTasks.add(function () {
      restoreFetch()
    })
  }

  if (window.XMLHttpRequest) {
    const restoreXHROpen = aop(XMLHttpRequest.prototype, 'open', xhrInterceptor, true)
    const restoreXHRSend = aop(
      XMLHttpRequest.prototype,
      'send',
      function (original, args) {
        const xhrData = this['__xhr_data__']

        if (!xhrData) {
          return original.apply(this, args)
        }

        if (args[0]) {
          xhrData.body = args[0]
        }

        original.apply(this, args)
      },
      true
    )

    cleanupTasks.add(function () {
      restoreXHROpen()
      restoreXHRSend()
    })
  }

  return {
    dispose: function () {
      cleanupTasks.dispose()
    }
  }
}

/**
 * refer: https://github.com/getsentry/sentry-javascript/blob/develop/packages/utils/src/supports.ts
 */
export function supportsNativeFetch() {
  if (!supportsFetch()) {
    return false
  }

  if (isNativeFetch(window.fetch)) {
    return true
  }

  let result = false
  const doc = window.document

  if (doc && isFunction(doc.createElement)) {
    try {
      const sandbox = doc.createElement('iframe')
      sandbox.hidden = true
      doc.head.appendChild(sandbox)

      if (sandbox.contentWindow && sandbox.contentWindow.fetch) {
        result = isNativeFetch(sandbox.contentWindow.fetch)
      }
      doc.head.removeChild(sandbox)
    } catch (err) {
      /* empty */
    }
  }

  return result
}

function supportsFetch() {
  if (!('fetch' in window)) {
    return false
  }

  try {
    const url = 'http:' + '/' + '/' + 'www.example.com'

    new Headers()
    new Request(url)
    new Response()
    return true
  } catch (e) {
    return false
  }
}

function isNativeFetch(func: typeof fetch): boolean {
  return func && /^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(func.toString())
}
