/**
 * refer: https://github.com/uuidjs/uuid/blob/main/src/v4.js
 */

const byteToHex: string[] = []

for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 0x100).toString(16).slice(1))
}

function unsafeStringify(arr: number[]) {
  const offset = 0
  return (
    byteToHex[arr[offset + 0]] +
    byteToHex[arr[offset + 1]] +
    byteToHex[arr[offset + 2]] +
    byteToHex[arr[offset + 3]] +
    '-' +
    byteToHex[arr[offset + 4]] +
    byteToHex[arr[offset + 5]] +
    '-' +
    byteToHex[arr[offset + 6]] +
    byteToHex[arr[offset + 7]] +
    '-' +
    byteToHex[arr[offset + 8]] +
    byteToHex[arr[offset + 9]] +
    '-' +
    byteToHex[arr[offset + 10]] +
    byteToHex[arr[offset + 11]] +
    byteToHex[arr[offset + 12]] +
    byteToHex[arr[offset + 13]] +
    byteToHex[arr[offset + 14]] +
    byteToHex[arr[offset + 15]]
  ).toLowerCase()
}

function rng() {
  let r = 0
  const rnds = new Array(16) as number[]

  for (let i = 0; i < 16; i++) {
    if ((i & 0x03) === 0) {
      r = Math.random() * 0x100000000
    }
    rnds[i] = (r >>> ((i & 0x03) << 3)) & 0xff
  }

  return rnds
}

export default function uuid() {
  if (crypto && crypto.randomUUID) {
    return crypto.randomUUID().replace(/-/g, '')
  }

  const rnds = rng()

  rnds[6] = (rnds[6] & 0x0f) | 0x40
  rnds[8] = (rnds[8] & 0x3f) | 0x80

  return unsafeStringify(rnds).replace(/-/g, '')
}
