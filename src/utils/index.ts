import fetchRequest from './fetch'
import Events from '../core/Events'
import { toArray } from './polyfills'
import xhrRequest from './browser/xhr'
import type { FetchRequestOptions } from './fetch'
import { isBrowser, isString, isFunction } from './is'
import type { XHRRequestOptions } from './browser/xhr'

type Method<T, K extends keyof T> = Extract<T[K], (...args: anyType[]) => anyType> & { [AOP_SYMBOL]?: boolean }

export const events = new Events<{
  error: (error: Error) => void
  bridgeRequest: (requestId: string, method: string, url: string, data?: anyType) => void
  bridgeResponse: (requestId: string, data?: anyType) => void
}>()

export const noop = function () {}

export const now = function () {
  return Date.now ? Date.now() : +new Date()
}

export const getViewId = function (prefix?: string) {
  prefix = prefix || '_'

  return prefix + '_' + now()
}

export const mixin = function <T extends object, U extends object>(target: T, source: U) {
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      // @ts-ignore
      target[key] = source[key]
    }
  }
}

const AOP_SYMBOL = '__aop_symbol__'
export const AOP_STACK: Record<string, Method<anyType, anyType>[]> = {}

function generateKey(target: Record<string, anyType>, name: string) {
  if (typeof target['__aop_id__'] === 'undefined') {
    target['__aop_id__'] = Math.random().toString(36).substring(2, 15)
  }

  return target.constructor.name + '-' + target['__aop_id__'] + '.' + name
}

/**
 *  `checkNative` will check if the original method is a [native code]. Please use it when proxying native methods.
 */
export const aop = function <T extends object, K extends keyof T>(
  target: T,
  name: K,
  callback: (this: T, originalMethod: Method<T, K>, args: Parameters<Method<T, K>>) => ReturnType<Method<T, K>>,
  checkNative?: boolean
) {
  if (!target || !isFunction(target[name])) {
    return noop
  }

  const original = target[name] as Method<T, K>
  const key = generateKey(target, String(name))

  if (checkNative) {
    const isAOP = !!original[AOP_SYMBOL]
    const isNative = /\[native code\]/.test(Function.prototype.toString.call(original))

    if (!isNative && !isAOP) {
      reportSDKErrors(new Error('AOP Warning: ' + key.replace(/-[a-z0-9]+\./, '.') + ' is not a [native code]'))
    }
  }

  if (AOP_STACK[key] && !original[AOP_SYMBOL] && AOP_STACK[key].indexOf(original) === -1) {
    AOP_STACK[key].unshift(original)
  }

  if (!AOP_STACK[key]) {
    AOP_STACK[key] = []
  }

  const proxiedMethod = function (this: T): ReturnType<Method<T, K>> {
    const currentIndex = AOP_STACK[key] ? AOP_STACK[key].indexOf(proxiedMethod) : -1

    if (currentIndex === -1) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return original.apply(this, toArray(arguments))
    }

    const nextIndex = currentIndex + 1
    const nextHandler = nextIndex < AOP_STACK[key].length ? (AOP_STACK[key][nextIndex] as Method<T, K>) : original

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return callback.call(this, nextHandler.bind(this) as Method<T, K>, toArray(arguments) as Parameters<Method<T, K>>)
  } as Method<T, K>

  proxiedMethod[AOP_SYMBOL] = true

  AOP_STACK[key].unshift(proxiedMethod)
  target[name] = proxiedMethod

  return function restore() {
    if (!AOP_STACK[key]) {
      return
    }

    const index = AOP_STACK[key].indexOf(proxiedMethod)

    AOP_STACK[key].splice(index, 1)

    if (target[name] === proxiedMethod) {
      if (AOP_STACK[key].length === 0) {
        target[name] = original

        delete (target as Record<string, anyType>)['__aop_id__']
        delete AOP_STACK[key]
      } else {
        target[name] = AOP_STACK[key][0] as unknown as Method<T, K>
      }
    }

    if (AOP_STACK[key] && AOP_STACK[key].every((proxy: Method<T, K>) => !proxy[AOP_SYMBOL])) {
      AOP_STACK[key].length = 0

      delete (target as Record<string, anyType>)['__aop_id__']
      delete AOP_STACK[key]
    }
  }
}

export const resolveURL = function (url: string) {
  if (!url) {
    return {}
  }

  const match = url.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/)

  if (!match) {
    return {}
  }

  const query = match[6] || ''
  const fragment = match[8] || ''

  return {
    search: query,
    host: match[4],
    path: match[5],
    hash: fragment,
    protocol: match[2],
    relative: match[5] + query + fragment
  }
}

export const reportSDKErrors = function (error: Error) {
  events.emit('error', error)
}

export const URIEncode = function (value: string) {
  return encodeURIComponent(value)
}

export const createCleanupTask = function () {
  let tasks: Array<() => void> = []

  return {
    add: function (task: () => void) {
      tasks.push(task)
    },
    dispose: function () {
      tasks.forEach(function (task) {
        task()
      })

      tasks = []
    }
  }
}

export function getRegexp(patterns: (string | RegExp)[]) {
  if (!patterns.length) {
    return null
  }

  const sources = []

  for (let i = 0; i < patterns.length; i++) {
    const pattern = patterns[i]

    if (isString(pattern)) {
      sources.push(pattern.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1'))
    } else if (pattern && (pattern as unknown as RegExp).source) {
      sources.push((pattern as unknown as RegExp).source)
    }
  }

  return new RegExp(sources.join('|'), 'i')
}

export function checkIsIgnored(ignoreUrls: (string | RegExp)[], url: string) {
  const regexp = getRegexp(ignoreUrls || [])

  return !!regexp && regexp.test(url)
}

export function request<T>(options: XHRRequestOptions<T> | FetchRequestOptions<T>) {
  if (isBrowser) {
    xhrRequest(options)
  } else {
    fetchRequest(options)
  }
}
