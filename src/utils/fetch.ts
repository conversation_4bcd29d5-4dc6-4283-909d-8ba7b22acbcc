import json from './json'

export interface FetchRequestOptions<T> {
  url: string
  data: T
  retries?: number
  retryDelay?: number
  method?: 'POST' | 'GET' | 'PUT' | 'DELETE'
  callback: (err: Error | null, responseText?: string) => void
}

export default function fetchRequest<T>(options: FetchRequestOptions<T>) {
  const url = options.url
  const data = options.data
  const callback = options.callback
  const method = options.method || 'POST'
  const retryDelay = options.retryDelay || 2000
  const retries = options.retries || options.retries === 0 ? options.retries : 3

  function retryCallback() {
    let retryUrl = url

    if (url.indexOf('retry=') === -1) {
      retryUrl = url + (url.indexOf('?') === -1 ? '?' : '&') + 'retry=' + retries
    } else {
      retryUrl = url.replace(/retry=\d+/, 'retry=' + retries)
    }

    fetchRequest({
      url: retryUrl,
      data: data,
      method: method,
      callback: callback,
      retries: retries - 1,
      retryDelay: retryDelay
    })
  }

  const fetchFn = typeof window !== 'undefined' && typeof window['__AEGIS_FETCH__'] !== 'undefined' ? window['__AEGIS_FETCH__'] : fetch

  fetchFn(url, {
    method: method,
    body: json.stringify(data),
    headers: { 'Content-Type': 'application/json;charset=UTF-8' }
  })
    .then(function (response) {
      if (response.ok) {
        return response.text()
      } else {
        throw new Error(`Request failed with status: ${response.status}`)
      }
    })
    .then(function (text) {
      callback(null, text)
    })
    .catch(function (error) {
      retryHandler(retries, retryDelay, retryCallback, function () {
        callback(<Error>error)
      })
    })
}

function retryHandler(retries: number, retryDelay: number, retryCallback: () => void, errorCallback: () => void) {
  if (retries > 0) {
    setTimeout(retryCallback, retryDelay)
  } else {
    errorCallback()
  }
}
