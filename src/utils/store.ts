import json from './json'
import { reportSDKErrors } from './'
import cookie from './browser/cookie'
import browserStorage from './browser/storage'

const store = {
  __cache: {} as Record<string, string>,
  getItemSync<T>(key: string) {
    try {
      let value: string | undefined

      if (browserStorage.available) {
        value = browserStorage.get(key)
      } else if (cookie.available) {
        value = cookie.get(key)
      } else {
        value = this.__cache[key]
      }

      return value ? (json.parse(value) as T) : undefined
    } catch (error) {
      errorHandler(error)

      return undefined
    }
  },
  setItemSync<T>(key: string, value: T) {
    try {
      if (browserStorage.available) {
        browserStorage.set(key, json.stringify(value))
      } else if (cookie.available) {
        const expires = new Date()
        expires.setFullYear(expires.getFullYear() + 20)

        cookie.set(key, json.stringify(value), expires)
      } else {
        this.__cache[key] = json.stringify(value)
      }
    } catch (error) {
      errorHandler(error)
    }
  }
}

function errorH<PERSON><PERSON>(error: unknown) {
  if (error instanceof DOMException && (error.name === 'QuotaExceededError' || error.name === 'SecurityError')) {
    reportSDKErrors(new Error('storage DOMException: ' + error.message))
  } else {
    reportSDKErrors(new Error('storage an unexpected error occurred: ' + (error as Error).message))
  }
}

export default store
