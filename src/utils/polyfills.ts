import { GLOBAL_OBJ } from './worldwide'
import { isFunction, isPolyfill, isBrowser } from './is'

export function assign<T extends object, U>(target: T, source: U): T & U
export function assign<T extends object, U, V>(target: T, source1: U, source2: V): T & U & V
export function assign<T extends object, U, V, W>(target: T, source1: U, source2: V, source3: W): T & U & V & W
export function assign(target: object, ...sources: Record<string, unknown>[]): Record<string, unknown>
export function assign(target: object) {
  if (target == null) {
    throw new TypeError('Cannot convert undefined or null to object')
  }

  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && Object.assign) {
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return Object.assign.apply(Object, arguments)
  }

  const to = target as Record<string, unknown>

  for (let i = 1; i < arguments.length; i++) {
    const source = arguments[i] as Record<string, unknown>

    if (source != null) {
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          to[key] = source[key]
        }
      }
    }
  }

  return to
}

export function includes<T>(array: T[], searchElement: T, fromIndex?: number) {
  if (!array) {
    throw new TypeError('The array cannot be null or undefined')
  }

  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && isFunction(Array.prototype.includes)) {
    return array.includes(searchElement, fromIndex)
  }

  const len = array.length
  if (len === 0) {
    return false
  }

  let startIndex = fromIndex != null ? fromIndex : 0
  if (startIndex < 0) {
    startIndex = Math.max(len + startIndex, 0)
  }

  for (let i = startIndex; i < len; i++) {
    if (searchElement === array[i] || (searchElement !== searchElement && array[i] !== array[i])) {
      return true
    }
  }

  return false
}

export function requestIdleCallback(callback: IdleRequestCallback, options?: IdleRequestOptions) {
  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && isFunction(GLOBAL_OBJ.requestIdleCallback)) {
    return GLOBAL_OBJ.requestIdleCallback(callback, options)
  }

  const start = Date.now()

  return GLOBAL_OBJ.setTimeout(function () {
    callback({
      didTimeout: false,
      timeRemaining: function () {
        return Math.max(0, 50 - (Date.now() - start))
      }
    })
  }, 1)
}

export function cancelIdleCallback(id: number | NodeJS.Timeout) {
  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && isFunction(GLOBAL_OBJ.cancelIdleCallback)) {
    return GLOBAL_OBJ.cancelIdleCallback(<number>id)
  }

  return GLOBAL_OBJ.clearTimeout(id)
}

export function findIndex<T>(array: T[], predicate: (value: T, index: number, obj: T[]) => boolean, thisArg?: unknown) {
  if (!array) {
    throw new TypeError('The array cannot be null or undefined')
  }

  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && isFunction(Array.prototype.findIndex)) {
    return array.findIndex(predicate, thisArg)
  }

  const len = array.length
  if (len === 0) {
    return -1
  }

  for (let i = 0; i < len; i++) {
    if (predicate.call(thisArg, array[i], i, array)) {
      return i
    }
  }

  return -1
}

export function toArray<T>(arrayLike: ArrayLike<T>): T[] {
  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && isFunction(Array.from)) {
    return Array.from(arrayLike)
  }

  return Array.prototype.slice.call(arrayLike) as T[]
}

export function cssSupports(property: string, value: string) {
  let result: boolean = true

  if ((process.env.NODE_ENV as string) === 'test') {
    result = isPolyfill()
  }

  if (result && 'CSS' in GLOBAL_OBJ && 'supports' in CSS) {
    return CSS.supports(property, value)
  }

  if (isBrowser) {
    const element = document.createElement('div')

    element.style.setProperty(property, value)
    return element.style.getPropertyValue(property) === value
  }

  return false
}
