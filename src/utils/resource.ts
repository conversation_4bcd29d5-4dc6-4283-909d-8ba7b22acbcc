import { normalizeTimestamp } from './time'

export function createTiming(data: PerformanceResourceTiming) {
  return {
    url: data.name,
    encodedBodySize: data.encodedBodySize || 0,
    duration: normalizeTimestamp(data.duration),
    startTime: normalizeTimestamp(data.startTime),
    connectEnd: normalizeTimestamp(data.connectEnd),
    fetchStart: normalizeTimestamp(data.fetchStart),
    redirectEnd: normalizeTimestamp(data.redirectEnd),
    responseEnd: normalizeTimestamp(data.responseEnd),
    connectStart: normalizeTimestamp(data.connectStart),
    requestStart: normalizeTimestamp(data.requestStart),
    redirectStart: normalizeTimestamp(data.redirectStart),
    responseStart: normalizeTimestamp(data.responseStart),
    domainLookupEnd: normalizeTimestamp(data.domainLookupEnd),
    domainLookupStart: normalizeTimestamp(data.domainLookupStart),
    tagName: data.initiatorType && data.initiatorType.toLowerCase(),
    secureConnectionStart: normalizeTimestamp(data.secureConnectionStart),
    timeOrigin: normalizeTimestamp(performance.timeOrigin || (performance.timing && performance.timing.navigationStart) || 0),
    transferSize: 'transferSize' in data ? data.transferSize : -1
  }
}
