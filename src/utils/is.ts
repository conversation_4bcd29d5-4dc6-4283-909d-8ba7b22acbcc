const objectToString = Object.prototype.toString

export const isBrowser = typeof self !== 'undefined'

export const isNode = objectToString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'

export const isObject = function (value: unknown): value is Record<string, unknown> {
  return objectToString.call(value) === '[object Object]'
}

export const isFunction = function (value: unknown): value is (...args: unknown[]) => unknown {
  return typeof value === 'function'
}

export const isString = function (value: unknown): value is string {
  return objectToString.call(value) === '[object String]'
}

export const isArray = function (value: unknown): value is unknown[] {
  return objectToString.call(value) === '[object Array]'
}

export const isWeb = function (platform: string) {
  return platform === 'web'
}

export const isNodejs = function (platform: string) {
  return platform === 'nodejs'
}

export const isMiniapp = function (platform: string) {
  return platform === 'miniapp'
}

export const isReactNative = function (platform: string) {
  return platform === 'rn'
}

export const isPolyfill = function () {
  return !('polyfill' in globalThis)
}
