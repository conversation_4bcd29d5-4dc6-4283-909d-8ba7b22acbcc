import { isBrowser } from './is'
import { toBoolean, toNumber } from './type'

export const GLOBAL_OBJ = isBrowser ? window : globalThis

// Custom browser global object, provided by the client WebView
export const BrowserTiming: Window['BrowserTiming'] = isBrowser ? getBrowserTiming() : ({} as Window['BrowserTiming'])

// The export here is for test case use only
export function getBrowserTiming() {
  const timing = window.BrowserTiming || {}

  return {
    deviceId: timing.deviceId || '',
    darkMode: timing.darkMode || '',
    pageType: timing.pageType || '',
    userIdEcpt: timing.userIdEcpt || '',
    networkType: timing.networkType || '',
    deviceModel: timing.deviceModel || '',
    deviceVendor: timing.deviceVendor || '',
    initTime: toNumber(timing.initTime || 0),
    startLoadTime: toNumber(timing.startLoadTime || 0),
    onStartedTime: toNumber(timing.onStartedTime || 0),
    isLowPowerMode: toBoolean(timing.isLowPowerMode || false)
  }
}
