{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "<PERSON> Vitest Browser", "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs", "console": "integratedTerminal", "args": ["--inspect-brk=127.0.0.1:3000", "--browser", "chrome", "--no-file-parallelism", "test/integrations/browser/performance/onSPA_LOAD.spec.ts"]}, {"type": "chrome", "request": "attach", "name": "Attach to Vitest Browser", "url": "http://127.0.0.1:3000", "port": 3000}], "compounds": [{"name": "Debug Vitest Browser", "configurations": ["<PERSON> Vitest Browser", "Attach to Vitest Browser"], "stopAll": true}]}