import { rollup } from 'rollup'
import { outputFile } from 'fs-extra'
import { readFileSync } from 'node:fs'
import { fileURLToPath } from 'node:url'
import { dirname, join } from 'node:path'
import terser from '@rollup/plugin-terser'
import virtual from '@rollup/plugin-virtual'
import rollupConfig from '../rollup.config.iife.mjs'

declare module 'rollup' {
  export interface RollupOptions {
    _entryPoint?: boolean
  }
}

const globalName = 'aegis'
const SDK_NAME = getSDKName()
const scriptUrl = 'your script url'
const _dirname = dirname(fileURLToPath(import.meta.url))
const snippetCode = formatSnippet(readFileSync(join(_dirname, '../scripts/snippet.ts')))

/**
 *
 * 该 rollup 打包流程主要是为了 SDK 在 CDN 使用的情况下，创建 loader.js 文件，用于加载 SDK
 *
 * 主要是把 loader.ts 和 snippet.ts 打包成一个 IIFE 格式的代码片段
 *
 */
async function build() {
  const bundle = await rollup({
    input: 'loader',
    plugins: [
      // 使用 virtual 插件加载 loader.ts 的内容
      virtual({
        loader: readFileSync(join(_dirname, '../src/loader.ts')).toString()
      }),
      // 清理源码中的 setTimeout 调用
      removeSetTimeoutPlugin(),
      // 压缩代码
      terser({
        output: {
          comments: false // 删除所有注释
        },
        mangle: {
          reserved: ['scriptUrl', 'globalName', 'callback'] // 保留这三个变量名
        }
      }),
      // 包裹 IIFE 调用代码
      rollupCustomIIFEPlugin(scriptUrl, globalName, snippetCode)
    ]
  })

  // 生成代码
  const [chunk] = (await bundle.generate({ format: 'esm', file: 'dist/iife/loader.js' })).output
  // 写入文件
  await outputFile(join(_dirname, '../dist/iife/loader.js'), chunk.code, 'utf-8')
}

build()

/**
 * 清理源码中的 setTimeout 调用
 *
 * 该插件主要进行以下操作：
 * 1. 使用正则表达式匹配 setTimeout 调用
 * 2. 提取 setTimeout 中的函数体
 * 3. 用函数体替换整个 setTimeout 调用
 *
 */
function removeSetTimeoutPlugin() {
  return {
    name: 'remove-settimeout',
    renderChunk(code: string) {
      const setTimeoutRegex = /setTimeout\s*\(\s*(function\s*\(.*?\)\s*\{([\s\S]*?)\})\s*,\s*\d+\s*\)/gm

      let match
      let newCode = code

      while ((match = setTimeoutRegex.exec(code)) !== null) {
        newCode = newCode.replace(match[0], match[2])
      }

      return { code: newCode, map: null }
    }
  }
}

/**
 * 自定义 IIFE 输出的插件
 *
 * 该插件主要进行以下操作：
 * 1. 移除 export 语句
 * 2. 将 const 转换为 var
 * 3. 将代码包装在 IIFE 中，并注入参数
 *
 */
function rollupCustomIIFEPlugin(scriptUrl: string, globalName: string, snippet: string) {
  return {
    name: 'custom-iife',
    renderChunk(code: string) {
      // remove export statement
      code = code.replace(/export\s*{[^}]*}\s*;?/g, '')
      // transform const to var
      code = code.replace(/const/g, 'var')
      // call function
      code = `(${code})('${scriptUrl}', '${globalName}', function () {
${snippet}
})`

      return { code, map: null }
    }
  }
}

/**
 * 格式化 snippet 代码
 *
 * 该函数主要进行以下操作：
 * 1. 移除注释
 * 2. 替换 GLOBAL_NAME 标记
 * 3. 替换 SDK_NAME 标记
 * 4. 清理空行
 * 5. 添加两个缩进
 *
 */
function formatSnippet(code: Buffer) {
  return (
    code
      .toString()
      // remove comments
      .replace(/\/\/.*|\/\*[\s\S]*?\*\//g, '')
      // replace GLOBAL_NAME flag
      .replace(/GLOBAL_NAME/g, globalName)
      // replace SDK_NAME flag
      .replace(/SDK_NAME/g, SDK_NAME!)
      // clear empty lines
      .trim()
      // add two indent
      .split('\n')
      .map((line) => `  ${line}`)
      .join('\n')
  )
}

function getSDKName() {
  const entryConfig = rollupConfig.filter((config) => config._entryPoint)[0]

  if (entryConfig && entryConfig.output) {
    return Array.isArray(entryConfig.output) ? entryConfig.output[0].name : entryConfig.output.name
  } else {
    return 'Aegis'
  }
}
