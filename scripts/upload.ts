import OSS from 'ali-oss'
import colors from 'colors'
import crypto from 'crypto'
import Table from 'cli-table3'
import { fileURLToPath } from 'node:url'
import { password } from '@inquirer/prompts'
import { join, dirname, basename } from 'node:path'
import { readdirSync, statSync, readFileSync } from 'node:fs'

const __dirname = dirname(fileURLToPath(import.meta.url))
const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'))

;(async function () {
  const urls: string[] = []
  const table = createOutputTable()
  let files = getAllFilePaths(join(__dirname, '../dist/iife'))

  const pass = await password({
    mask: true,
    message: 'Please enter the upload password:'
  })

  const cdnUrl = decrypt(process.env.CDN_URL as string, pass)

  const client = new OSS({
    bucket: decrypt(process.env.OSS_BUCKET as string, pass),
    region: decrypt(process.env.OSS_REGION as string, pass),
    accessKeyId: decrypt(process.env.OSS_KEY as string, pass),
    accessKeySecret: decrypt(process.env.OSS_SECRET as string, pass)
  })

  files = files.filter((file) => !file.includes('loader.js'))

  for (const file of files) {
    const url = await uploadFile(`aegis/${pkg.version}/js/${basename(file)}`, file, client)

    urls.push(url.replace(/^(http|https):\/\/.+?\//, cdnUrl))
  }

  table.push(['File', 'Url'])

  urls.forEach((url) => {
    table.push([basename(url), url])
  })

  console.log(table.toString() + '\n')
  console.log(colors.green.bold('Combo URL:'), colors.blue.underline(`${cdnUrl}aegis/${pkg.version}/js/??${urls.map((url) => basename(url)).join(',')}`), '\n')
})()

async function uploadFile(fileName: string, filePath: string, client: OSS) {
  const result = await client.put(fileName, filePath)

  return result.url
}

function getAllFilePaths(dirPath) {
  const results: string[] = []
  const files = readdirSync(dirPath)

  files.forEach(function (file) {
    file = join(dirPath, file)

    if (statSync(file).isDirectory()) {
      results.push(...getAllFilePaths(file))
    } else {
      results.push(file)
    }
  })

  return results
}

function createOutputTable() {
  return new Table({
    chars: {
      top: '═',
      'top-mid': '╤',
      'top-left': '╔',
      'top-right': '╗',
      bottom: '═',
      'bottom-mid': '╧',
      'bottom-left': '╚',
      'bottom-right': '╝',
      left: '║',
      'left-mid': '╟',
      mid: '─',
      'mid-mid': '┼',
      right: '║',
      'right-mid': '╢',
      middle: '│'
    }
  })
}

function decrypt(data: string, password: string) {
  const algorithm = 'aes-256-cbc'
  const iv = crypto.createHash('md5').update(password).digest()
  const key = crypto.createHash('sha256').update(password).digest()
  const decipher = crypto.createDecipheriv(algorithm, key, iv)

  return `${decipher.update(data, 'hex', 'utf8')}${decipher.final('utf8')}`
}
