import Table from 'cli-table3'
import { readFileSync, readdirSync, statSync } from 'node:fs'
import { basename, dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = dirname(fileURLToPath(import.meta.url))
const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'))

;(async function () {
  const urls: string[] = []
  const table = createOutputTable()
  let files = getAllFilePaths(join(__dirname, '../dist/iife'))

  files = files.filter((file) => !file.includes('loader.js'))

  for (const file of files) {
    const url = await uploadFile(file)

    urls.push(url)
  }

  table.push(['File', 'Url'])

  urls.forEach((url) => {
    table.push([basename(url), url])
  })

  console.log(table.toString() + '\n')
})()

async function uploadFile(filePath: string): Promise<string> {
  const version = pkg.version
  const fileName = basename(filePath)
  const formData = new FormData()
  formData.append('file', new Blob([readFileSync(filePath)], { type: 'application/javascript' }), fileName)
  const res = await (
    await fetch(`${process.env.SDK_UPLOAD_API}/file/js/web/${version}`, {
      method: 'POST',
      body: formData
    })
  ).json()
  return res.data.fileUrl
}

function getAllFilePaths(dirPath) {
  const results: string[] = []
  const files = readdirSync(dirPath)

  files.forEach(function (file) {
    file = join(dirPath, file)

    if (statSync(file).isDirectory()) {
      results.push(...getAllFilePaths(file))
    } else {
      results.push(file)
    }
  })

  return results
}

function createOutputTable() {
  return new Table({
    chars: {
      top: '═',
      'top-mid': '╤',
      'top-left': '╔',
      'top-right': '╗',
      bottom: '═',
      'bottom-mid': '╧',
      'bottom-left': '╚',
      'bottom-right': '╝',
      left: '║',
      'left-mid': '╟',
      mid: '─',
      'mid-mid': '┼',
      right: '║',
      'right-mid': '╢',
      middle: '│'
    }
  })
}
