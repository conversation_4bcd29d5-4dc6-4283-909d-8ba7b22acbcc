import ora from 'ora'
import { $ } from 'zx'
import OSS from 'ali-oss'
import axios from 'axios'
import semver from 'semver'
import colors from 'colors'
import crypto from 'crypto'
import { hash } from 'hasha'
import { fileURLToPath } from 'node:url'
import { join, dirname, basename } from 'node:path'
import { readFile, writeFile } from 'node:fs/promises'
import { readFileSync, readdirSync, statSync } from 'node:fs'
import { checkbox, select, Separator, password } from '@inquirer/prompts'

const __dirname = dirname(fileURLToPath(import.meta.url))
const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'))

;(async function () {
  const source = await select({
    loop: false,
    message: 'Select a data source',
    choices: [
      { name: 'Local (Based on the latest version)', value: 'local' },
      { name: 'Remote (Multiple versions available)', value: 'remote' }
    ]
  })

  if (source === 'local') {
    const dir = join(__dirname, '../dist/iife')

    try {
      statSync(dir)
    } catch (error) {
      const spinner = ora('Building Project...').start()
      await $`npm run build 2>&1`
      spinner.stop()
    }

    const files = getAllFilePaths(dir)
      .filter((file) => !file.includes('loader.js'))
      .reduce((acc, file) => ((acc[basename(file)] = file), acc), [])
    const choices = Object.keys(files)

    const sourceCodes = await checkbox({
      loop: false,
      pageSize: choices.length + 3,
      message: 'Select a source code',
      choices: [
        new Separator('Main =>'),
        ...choices.filter((item) => ['aegis'].some((prefix) => item.startsWith(prefix))).map(createChoice),
        new Separator('Integrations =>'),
        ...choices
          .filter((item) =>
            ['action', 'api', 'blankScreen', 'bridge', 'jsError', 'pageview', 'precollect', 'resource', 'resourceError', 'device', 'perf', 'largePictureInspect', 'feature'].some((prefix) =>
              item.startsWith(prefix)
            )
          )
          .map(createChoice),
        new Separator('Transports =>'),
        ...choices.filter((item) => ['console', 'http', 'pixel'].some((prefix) => item.startsWith(prefix))).map(createChoice)
      ]
    })

    if (!sourceCodes.length) {
      return
    }

    const names = elevateAegisJsFile(sourceCodes, /aegis.*\.js$/).map((item) => item.match(/([a-zA-Z]+)\.\w+\.js/)?.[1]) as string[]

    Promise.all(sourceCodes.map((item) => files[item]).map((item) => readFile(item, 'utf-8'))).then(async (sourceCodes) => {
      const code = [addComments(pkg.version, names), ...sourceCodes].join('\n')
      const filename = `aegis.${(await hash(code, { algorithm: 'sha1' })).slice(0, 8)}.js`

      await writeFile(join(dir, '..', filename), code, 'utf-8')

      console.log(colors.green.bold('File:'), colors.blue.underline(`file:/${join(dir, '..', filename)}`), '\n')
    })
  }

  if (source === 'remote') {
    const { cdnUrl, client } = await getClient()
    const version = await getVersion(client)

    if (!version) {
      return
    }

    const objectList = await client.listV2({ prefix: `aegis/${version}/` })
    const choices = dedupe(objectList.objects).map((object) => object.name) as string[]

    const sourceCodes = await checkbox({
      loop: false,
      pageSize: choices.length + 3,
      message: 'Select a source code',
      choices: [
        new Separator('Main =>'),
        ...filterChoices(choices, version, ['aegis']).map(createChoice),
        new Separator('Integrations =>'),
        ...filterChoices(choices, version, [
          'action',
          'api',
          'blankScreen',
          'bridge',
          'jsError',
          'pageview',
          'precollect',
          'resource',
          'resourceError',
          'device',
          'perf',
          'largePictureInspect',
          'feature'
        ]).map(createChoice),
        new Separator('Transports =>'),
        ...filterChoices(choices, version, ['console', 'http', 'pixel']).map(createChoice)
      ]
    })

    if (!sourceCodes.length) {
      return
    }

    const names = elevateAegisJsFile(sourceCodes, /\/aegis.*\.js$/)
      .map((item) => item.replace(`aegis/${version}/js/`, ''))
      .map((item) => item.match(/([a-zA-Z]+)\.\w+\.js/)?.[1]) as string[]

    Promise.all(sourceCodes.map((sourceCode) => `${cdnUrl}${sourceCode}`).map((item) => axios.get(item).then((response) => response.data)))
      .then(async (sourceCodes) => {
        const code = [addComments(version, names), ...sourceCodes].join('\n')
        const filename = `aegis.${(await hash(code, { algorithm: 'sha1' })).slice(0, 8)}.js`

        client.put(`js/${filename}`, Buffer.from(code)).then(() => {
          console.log(colors.green.bold('Combo URL:'), colors.blue.underline(`${cdnUrl}js/${filename}`), '\n')
        })
      })
      .catch((error) => {
        console.error(error)
      })
  }
})()

async function getClient() {
  const pass = await password({
    mask: true,
    message: 'Please enter the remote(CDN) password:'
  })

  const cdnUrl = decrypt(process.env.CDN_URL as string, pass)

  const client = new OSS({
    bucket: decrypt(process.env.OSS_BUCKET as string, pass),
    region: decrypt(process.env.OSS_REGION as string, pass),
    accessKeyId: decrypt(process.env.OSS_KEY as string, pass),
    accessKeySecret: decrypt(process.env.OSS_SECRET as string, pass)
  })

  return { cdnUrl, client }
}

async function getVersion(client: any) {
  const versionRegex = /(\d+\.\d+\.\d+)/g
  const objectList = await client.listV2({ prefix: `aegis/`, delimiter: '/' })

  const versions = [
    ...new Set(
      objectList.prefixes
        .map((item) => {
          const match = item.match(versionRegex)

          return match ? match[0] : null
        })
        .filter((item) => item)
    )
  ].sort((a, b) => semver.rcompare(a as string, b as string)) as string[]

  const version = await select({
    loop: false,
    pageSize: versions.length,
    message: 'Select a version',
    choices: versions.map((version) => ({ name: version, value: version }))
  })

  return version
}

function decrypt(data: string, password: string) {
  const algorithm = 'aes-256-cbc'
  const iv = crypto.createHash('md5').update(password).digest()
  const key = crypto.createHash('sha256').update(password).digest()
  const decipher = crypto.createDecipheriv(algorithm, key, iv)

  return `${decipher.update(data, 'hex', 'utf8')}${decipher.final('utf8')}`
}

function filterChoices(choices: string[], version: string, prefixs: string[]) {
  return choices.filter((item) => prefixs.some((prefix) => item.startsWith(`aegis/${version}/js/${prefix}`)))
}

function createChoice(choices: string) {
  return { name: choices, value: choices }
}

function addComments(version: string, names: string[]) {
  const date = new Date().toISOString().split('T')[0]

  return `/*!
 * Aegis v${version}
 * (c) ${date} author: ${pkg.author}
 * content: [${names.join(', ')}]
 */`
}

function elevateAegisJsFile(arr: string[], regex: RegExp): string[] {
  const index = arr.findIndex((item) => regex.test(item))

  if (index > 0) {
    const [file] = arr.splice(index, 1)

    arr.unshift(file)
  }

  return arr
}

function getAllFilePaths(dirPath) {
  const results: string[] = []
  const files = readdirSync(dirPath)

  files.forEach(function (file) {
    file = join(dirPath, file)

    if (statSync(file).isDirectory()) {
      results.push(...getAllFilePaths(file))
    } else {
      results.push(file)
    }
  })

  return results
}

function dedupe(data: any[]): any[] {
  return Object.values(
    data.reduce((acc, item) => {
      const name = basename(item.name).split('.')[0]

      if (acc[name]) {
        if (new Date(item.lastModified) > new Date(acc[name].lastModified)) {
          acc[name] = item
        }
      } else {
        acc[name] = item
      }

      return acc
    }, {})
  )
}
