# Logs
logs
*.log
npm-debug.log*

# Dependency directory
/node_modules

# Output directory
/dist

# Optional npm cache directory
.npm

# -- VSCode --
jsconfig.json

# -- IDEA --
/.idea

## File-based project format:
*.ipr
*.iws
*.iml

# -- OS X --
.DS_Store

# -- ESLint --
.eslintcache

# -- Vitest --
/coverage
/.nyc_output

# -- Docs --
docs/.vitepress/cache
docs/.vitepress/dist

# -- types --
/types

# -- lockfile --
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb
