module.exports = {
  root: true,
  plugins: ['prettier', '@typescript-eslint'],
  extends: [
    'eslint:recommended',
    'plugin:prettier/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/recommended-requiring-type-checking'
  ],
  rules: {
    'no-case-declarations': 'off',
    'prefer-rest-params': 'off',
    'prefer-spread': 'off',
    'prettier/prettier': 'error',
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/unbound-method': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/no-floating-promises': [
      'error',
      {
        ignoreIIFE: true
      }
    ]
  },
  env: {
    node: true
  },
  ignorePatterns: [
    '/*.js',
    '/*.json',
    '**/dist/**',
    '**/test/**',
    '**/docs/**',
    '**/types/**',
    '**/example/**',
    '**/scripts/**',
    '.eslintrc.cjs',
    'loader.ts',
    'vitest.config.*.mjs',
    'rollup.config.*.mjs',
    '/*.md'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: './tsconfig.json'
  }
}
