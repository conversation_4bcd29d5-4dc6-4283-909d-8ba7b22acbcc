<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Example</title>
    <!-- combo assets -->
    <!-- <script src="??<%= filePaths  %>"></script>
    <script>
      const aegis = new Aegis({
        id: 'test',
        integrations: [Aegis.pageview()],
        transports: [new Aegis.transports.Console(), new Aegis.transports.Pixel('/r.gif'), new Aegis.transports.Http('/log')]
      })
    </script> -->

    <!-- preload assets -->
    <script><%- loaderCode %></script>

    <!-- normal assets -->
    <!-- <% filePaths.forEach(function(path){ %><script src="<%= path %>"></script><% }); %>
    <script>
      const aegis = new Aegis({
        id: 'test',
        integrations: [Aegis.pageview()],
        transports: [new Aegis.transports.Console(), new Aegis.transports.Pixel('/r.gif'), new Aegis.transports.Http('/log')]
      })
    </script> -->

    <script>
      setTimeout(function () {
        aegis.sendPV('hi!')
      }, 2000)

      aegis.sendPV('hello')
    </script>
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
