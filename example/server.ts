import express from 'express'
import compression from 'compression'
import comboHandler from 'combo-handler'
import { fileURLToPath } from 'node:url'
import { dirname, join } from 'node:path'
import { OUTPUT_DIR } from '../rollup.config.iife.mjs'
import { readdirSync, statSync, readFileSync } from 'node:fs'

const pageStats = {}

const app = express()
const _dirname = dirname(fileURLToPath(import.meta.url))
const outputDirectory = join(_dirname, '..', `${OUTPUT_DIR}/`)
const filePaths = getAllFilePaths(outputDirectory).map((item) => item.replace(outputDirectory, ''))

app.set('views', _dirname)
app.set('view engine', 'ejs')

app.get('/r.gif', (req, res) => {
  const log = JSON.parse(<string>req.query.d)
  const gif = Buffer.from('R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==', 'base64')

  res.setHeader('Content-Type', 'image/gif')
  res.setHeader('Content-Length', gif.length)
  res.end(gif)

  console.log(`======== Pixel Transport Start ========`)
  console.log('IP:', normalizeIP(req.ip))
  console.log('Referer:', req.headers.referer)
  console.log('User-Agent:', req.headers['user-agent'])
  console.log('Log:', log)
  console.log(`======== Pixel Transport End ========`)

  const pid = log.payload.pid

  if (!pageStats[pid]) {
    pageStats[pid] = {
      pv: 0,
      uids: new Set()
    }
  }

  pageStats[pid].pv += 1

  if (!pageStats[pid].uids.has(log.common.uid)) {
    pageStats[pid].uids.add(log.common.uid)
  }
})

app.post('/log', express.json(), (req, res) => {
  const log = req.body.d[0]

  res.end()

  console.log(`======== Http Transport Start ========`)
  console.log('IP:', normalizeIP(req.ip))
  console.log('Referer:', req.headers.referer)
  console.log('User-Agent:', req.headers['user-agent'])
  console.log('Log:', log)
  console.log(`======== Http Transport End ========`)

  const pid = log.payload.pid

  if (!pageStats[pid]) {
    pageStats[pid] = {
      pv: 0,
      uids: new Set()
    }
  }

  pageStats[pid].pv += 1

  if (!pageStats[pid].uids.has(log.common.uid)) {
    pageStats[pid].uids.add(log.common.uid)
  }
})

app.get('/stats', (_, res) => {
  const stats = {}

  for (const [pid, data] of Object.entries(pageStats) as any) {
    stats[pid] = {
      pv: data.pv,
      uv: data.uids.size
    }
  }

  res.status(200).json(stats)
})

app.use(compression() as express.RequestHandler)
app.use(comboHandler({ base: outputDirectory }))

app.get('/', function (_, res) {
  const assets = filePaths.filter((item) => item.indexOf('loader.js') === -1)

  res.render('index.ejs', {
    filePaths: assets,
    loaderCode: readFileSync(join(outputDirectory, filePaths.filter((item) => item.indexOf('loader.js') !== -1)[0]))
      .toString()
      .replace('your script url', `??${assets.join(',')}`)
  })
})

app.use(express.static('example'))
app.use(express.static(OUTPUT_DIR))

app.listen(5678, () => console.log('Server running on port 5678'))

function getAllFilePaths(dirPath: string) {
  const results: string[] = []
  const files = readdirSync(dirPath)

  files.forEach(function (file) {
    file = join(dirPath, file)

    if (statSync(file).isDirectory()) {
      results.push(...getAllFilePaths(file))
    } else {
      results.push(file)
    }
  })

  return results
}

function normalizeIP(ip?: string) {
  return ip ? ip.replace('::ffff:', '') : 'Unknown IP'
}
