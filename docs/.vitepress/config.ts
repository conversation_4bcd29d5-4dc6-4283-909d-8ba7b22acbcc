import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'Aegis',
  description: 'Aegis 前端监控系统，提供前端错误监控、性能监控、资源监控等功能。',
  head: [['link', { rel: 'icon', href: '/favicon.ico' }]],
  themeConfig: {
    aside: false,
    siteTitle: false,
    logoLink: '/',
    logo: {
      light: '/logo/dark.svg',
      dark: '/logo/light.svg'
    },
    nav: [
      { text: '指南', link: '/guide/' },
      { text: '参考', link: '/config/' }
    ],
    sidebar: {
      '/guide/': [
        {
          text: '简介',
          items: [
            { text: '什么是 Aegis?', link: '/guide/' },
            { text: '接入方式', link: '/guide/usage' },
            { text: '设计理念', link: '/guide/design' }
          ]
        },
        {
          text: '插件',
          items: [
            {
              text: '采集项',
              items: [
                { text: 'API', link: '/guide/plugins/api' },
                { text: 'Action', link: '/guide/plugins/action' },
                { text: 'Device', link: '/guide/plugins/device' },
                { text: 'Bridge', link: '/guide/plugins/bridge' },
                { text: 'JS Error', link: '/guide/plugins/jsError' },
                { text: 'PageView', link: '/guide/plugins/pageview' },
                { text: 'Resource', link: '/guide/plugins/resource' },
                { text: 'Precollect', link: '/guide/plugins/precollect' },
                { text: 'Performance', link: '/guide/plugins/perf' },
                { text: 'Blank Screen', link: '/guide/plugins/blank' },
                { text: 'Resource Error', link: '/guide/plugins/resourceError' },
                { text: 'Large Picture Inspect', link: '/guide/plugins/largePictureInspect' },
                { text: 'Feature', link: '/guide/plugins/feature' }
              ]
            },
            {
              text: '上报方式',
              items: [
                { text: 'Pixel', link: '/guide/plugins/pixel' },
                { text: 'HTTP', link: '/guide/plugins/http' },
                { text: 'Console', link: '/guide/plugins/console' }
              ]
            }
          ]
        },
        {
          text: '高级',
          items: [
            { text: '开发约定', link: '/guide/advanced/convention' },
            { text: '插件开发', link: '/guide/advanced/plugin' },
            { text: 'Builder 开发', link: '/guide/advanced/builder' },
            { text: 'SourceMap 上传', link: '/guide/advanced/sourcemap' }
          ]
        },
        {
          text: '后端',
          items: [
            { text: '系统概述', link: '/guide/backend/overview' },
            { text: '日志采集服务', link: '/guide/backend/log' },
            { text: '业务服务', link: '/guide/backend/business' }
          ]
        }
      ],
      '/config/': [
        {
          text: '参考',
          items: [
            {
              text: 'SDK 配置',
              link: '/config/'
            },
            {
              text: '数据结构',
              link: '/config/data'
            }
          ]
        }
      ]
    }
  }
})
