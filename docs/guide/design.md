# 设计理念

Aegis SDK 的核心主要围绕插件化和模块化设计，它们俩共同构成了 SDK 的基础架构，使其具有高度的灵活性、可扩展性和可维护性。

## 架构

<div style="display: none;">
graph LR
    B[Plugin 1] --- A
    C[Plugin 2] --- A
    D[Plugin 3] --- A
    E[Plugin N] --- A
    A[Aegis Core] --- F[ConfigManager]
    A --- G[Events System]
    A --- J[Life Cycle]
    A --- H[Transport Layer]
    A --- I[InstanceManager]
</div>

<style scoped>
html:not(.dark) .architecture.dark {
  display: none;
}
.dark .architecture.light {
  display: none;
}
</style>

<p align="center" class="architecture dark">
  <img src="/aegis-sdk-architecture-dark.svg" width="50%" />
</p>

<p align="center" class="architecture light">
  <img src="/aegis-sdk-architecture.svg" width="50%" />
</p>

### 核心模块

`Aegis Core` 作为中心枢纽，连接了各个插件和内部模块。它负责协调整个 SDK 的运作，包括：

- 负责插件的注册和销毁
- 提供生命周期 API 供插件和外部调用
- 协调内部模块的工作

### 插件系统

插件系统允许开发者通过编写插件来扩展 SDK 的功能：

- 每个插件（Plugin 1, 2, 3, ..., N）都是独立的功能单元
- 插件可以通过标准接口与 Aegis Core 交互

核心代码：

```typescript
function pluginInstallHandler(aegis: Aegis, plugins: ConfigOptions['integrations']) {
  plugins.forEach((plugin) => {
    plugin.setup(aegis)
    plugin.tearDown && aegis.on('beforeDestroy', plugin.tearDown)
  })
}
```

### 内部模块

- `ConfigManager`：管理 SDK 的配置，提供统一的配置接口
- `Events System`：实现事件系统，促进插件和模块间的通信
- `Life Cycle`：管理 SDK 的生命周期，包括初始化和销毁过程
- `Transport Layer`：负责数据的传输，比如发送数据到服务端
- `InstanceManager`：管理多个 Aegis 实例，确保它们能够共存

### 采集数据上报流程

数据上报遵循 "采集-上报-构建-发送" 的流程，在每个关键节点（`beforeReport`、`beforeBuild`、`beforeSend`）提供干预机会，确保数据处理的灵活性和可控性。

<div style="display: none;">
sequenceDiagram
    participant Cap as Client
    participant Rep as Report
    participant Bui as Build
    participant Sen as Send
    participant Ser as Server

    Cap->>Rep: 调用 report 方法
    activate Rep
    Rep->>Rep: 触发 beforeReport 事件
    Rep-->>Bui: beforeReport 返回 truthy
    deactivate Rep
    activate Bui
    Bui->>Bui: 触发 beforeBuild 事件
    Bui-->>Sen: beforeBuild 返回 truthy
    deactivate Bui
    activate Sen
    Sen->>Sen: 触发 beforeSend 事件
    Sen-->>Ser: beforeSend 返回 truthy
    deactivate Sen

</div>

<style scoped>
html:not(.dark) .flow.dark {
  display: none;
}
.dark .flow.light {
  display: none;
}
</style>

<p align="center" class="flow dark">
  <img src="/aegis-sdk-data-flow-dark.svg" width="75%" />
</p>

<p align="center" class="flow light">
  <img src="/aegis-sdk-data-flow.svg" width="75%" />
</p>

### 错误处理和容错

`Aegis Core` 内置了错误处理机制以及`reportSDKErrors`方法，这确保了 SDK 本身的错误不会影响到宿主应用，同时也可以将 SDK 的内部错误上报。

## 生命周期事件

### `init`

监听实例初始化。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('init', () => {
  console.log('Aegis SDk 初始化完成')
})
```

### `beforeConfig`

监听实例配置变更之前，可拿到新的配置。

通过 `setConfig` 方法更新配置时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('beforeConfig', (configOptions) => {
  console.log('通过 setConfig 方法更新的新配置 ====>', configOptions)
})
```

### `beforeReport`

监听数据被上报之前的瞬间，返回 `Falsy` 则不上报。

通过 `report` 方法上报数据时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('beforeReport', (data) => {
  console.log('插件上报的数据 ====>', data)
})
```

### `beforeBuild`

监听数据被构建之前的瞬间，返回 `Falsy` 则不上报。

通过 `build` 方法构建数据时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('beforeBuild', (data) => {
  console.log('插件上报的数据 ====>', data)
})
```

### beforeSend

监听数据被发送之前的瞬间，返回 `Falsy` 则不上报。

通过 `send` 方法发送数据时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('beforeSend', (data) => {
  console.log('插件上报的数据 ====>', data)
})
```

### beforeDestroy

监听实例销毁完成。

通过 `destroy` 方法销毁实例时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('beforeDestroy', () => {
  console.log('实例已销毁')
})
```

### provide

监听实例被挂载属性的瞬间，可拿到属性名。

通过 `provide` 方法注入属性时触发。

```typescript
const aegis = new Aegis({ id: 'test' })

aegis.on('provide', (name) => {
  console.log('通过 provide 方法注入的方法名称 ====>', name)
})
```
