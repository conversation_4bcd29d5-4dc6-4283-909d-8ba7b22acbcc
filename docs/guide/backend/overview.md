<!-- 日志采集服务：主要技术栈使用到了 rust + rocket + Clickhouse。
日志采集服务项目部署在Kubernetes，接受日志上报sdk 上报的 log，
当日志采集服务接收到数据后，第一时间将数据序列化并过滤掉不符合上报规范的数据。
接下来将数据push 到数据清洗队列中。我们还有一个进程用于消费数据清洗队列中的数据。
由日志清洗进程处理后的数据并不会直接存储在Clickhouse同样的这些数据也是进入到缓存池里，会有2个控制阈值1是时间每间隔几秒触发，1当缓存池中的数据到达一点量的时候符合任意一个阈值会触发将数据写入Clickhouse。

业务服务：主要技术栈使用到了 node + nestjs + mysql + redis。
业务服务部署在Kubernetes，主要的功能模块有： 数据分析服务， 告警服务，项目管理等服务
数据分析服务：提供了数据查询，数据分析，数据可视化等服务
告警服务：提供了告警规则配置，告警通知，告警处理等服务
项目管理服务：提供了用户管理，项目管理，项目配置，项目监控等服务 -->

# 系统概述

Aegis 后端是 Aegis 项目的核心组成部分之一。主要有日志采集服务和业务服务两大模块构成。 系统均采用微服务架构，具有高可用、高性能、可扩展等特点。

## 系统架构

Aegis 后端由2个服务组成：

- 日志采集服务

日志采集服务主要负责将上报的日志数据进行序列化，过滤，清洗并存储到数据库中。是 Aegis 项目中最重要的服务之一。项目采用 Rust + Clickhouse 技术栈，Rust 语言以其高性能、高并发、高可靠性的特点，为系统提供了强大的性能保障。Clickhouse 作为一款高性能的列式数据库，为系统提供了高效的数据存储和查询能力。

- 业务服务

业务服务主要负责将日志数据进行分析，告警，存储到数据库中。项目采用 node + nestjs + mysql + redis 技术栈，为前端研发人员提供了一个熟悉便于上手的项目。并且nestjs 是一个社区活跃，文档齐全，生态丰富的框架。

<!-- TODO: 系统架构图 -->

## 系统架构图

<p align="center" >
  <img src="/backend/back-arch.png" width="100%" />
</p>
