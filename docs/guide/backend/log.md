<!--
日志采集服务：主要技术栈使用到了 rust + rocket + Clickhouse。
日志采集服务项目部署在Kubernetes，接受日志上报sdk 上报的 log，
当日志采集服务接收到数据后，第一时间将数据序列化并过滤掉不符合上报规范的数据。
接下来将数据push 到数据清洗队列中。我们还有一个进程用于消费数据清洗队列中的数据。
由日志清洗进程处理后的数据并不会直接存储在Clickhouse同样的这些数据也是进入到缓存池里，会有2个控制阈值1是时间每间隔几秒触发，1当缓存池中的数据到达一点量的时候符合任意一个阈值会触发将数据写入Clickhouse。
 -->

# 日志采集服务

日志采集服务主要负责将上报的日志数据进行序列化，过滤，清洗并存储到数据库中。日志采集服务是 Aegis 后端服务与前端上报数据的第一道关卡。
承接来自端侧上报的海量数据，是保证服务质量，数据质量，数据安全的重要一环。

## 技术栈

日志采集服务主要技术栈使用到了 rust + Clickhouse。

### Rust

- 先进的内存管理机制

日志收集服务需短时间内容接受并处理大量的数据，对内存管理要求非常高。
Rust 是无 GC 的语言，通过所有权和生命周期机制，确保内存安全，避免了内存泄漏和悬挂指针等问题。
Rust 的内存管理机制能够提供和 C 一样的性能、并且不需要像 C 那样需要手动管理内存造成开发中的疏忽造成内存泄漏。

- 高性能、高并发、高可用

Rust 编译器非常优秀，在编译期能够发现很多潜在的错误，编辑产物基本和 C/C++ 一样，在运行时性能非常高。

Rust 开发的程序最终的产物是二进制文件，运行效率高，在运行时不需要依赖其他库，可以快速启动。
并且 Rust 的并发模型非常优秀，能够提供和 node.js 一样的并发模型。

- 各语言性能对比图 (基准: C/C++ = 100%)

```
性能指标 (越高越好)
100% ┌──────────────────────────────────────────┐
     │C/C++  ████████████████████████████████   │ 100%
     │Rust   ████████████████████████████████   │ 100%
     │Go     ██████████████████████             │ 75%
     │Java   ████████████████████               │ 70%
     │NodeJS ████████                           │ 30%
     └──────────────────────────────────────────┘
     0      20     40     60     80     100

内存使用 (越低越好)
100% ┌──────────────────────────────────────────┐
     │C/C++  ████████████                       │ 100%
     │Rust   █████████████                      │ 105%
     │Go     ████████████████████               │ 175%
     │Java   ██████████████████████████████     │ 275%
     │NodeJS ████████████████████████████████   │ 350%
     └──────────────────────────────────────────┘
     0      100    200    300    400    500
```

<!--
对比C/C++
内存使用的微小差距主要来之
元数据开销、安全检查开销。

还有高效的序列化/反序列化
serde生态系统
零拷贝解析
高性能JSON处理
 -->

<!--
首先，ClickHouse具有卓越的性能表现，单节点每秒可处理数十万次写入，查询性能比传统数据库快10-1000倍；其次，在成本方面，存储成本仅为Elasticsearch的50-60%，且开源免费无需支付授权费用；第三，技术特性上，其列式存储引擎提供高达10:1的压缩率，支持SQL标准和强大的聚合分析能力；最后，运维成本相对较低，部署简单，社区活跃。预计总体TCO可降低35-45%，能够很好地满足我们每日约5TB的日志数据存储需求。
 -->

### Clickhouse

- clickhouse 是俄罗斯的 Yandex 公司开发的列式存储数据库，主要用于在线分析处理查询（OLAP），能够处理海量数据。

- 开发友好，支持SQL标准和强大的聚合分析能力。

- 整体成本角度。

<!-- 存储成本仅为Elasticsearch的50-60%，且开源免费无需支付授权费用 -->

### 核心流程

#### 日志接收

- 接收上报日志
- 序列化日志数据
- 过滤不合规的日志
- 将日志数据推送到数据清洗队列

#### 数据清洗

- 消费数据清洗队列中的数据
- 清洗日志数据
- 将清洗后的数据推送到缓存池

#### 数据写入

- 从 Clickhouse 连接池中获取连接（均衡负载）
- 从缓存池中获取数据
- 将数据写入 Clickhouse

<p align="center" >
  <img src="/backend/log-process.png" width="100%" />
</p>

## 目录结构

```tree

├── Cargo.toml              # 项目配置和依赖管理
├── Cargo.lock              # 依赖版本锁定文件
├── Dockerfile              # 容器构建配置
├── docker-compose.yml      # 容器编排配置
├── Rocket.toml            # Web框架配置
├── README.md              # 项目说明文档
├── ck-script/             # ClickHouse数据库脚本
└── src/                   # 源代码目录
    ├── main.rs            # 程序入口
    ├── models/            # 数据模型
    │   ├── mod.rs        # 模型模块入口
    │   ├── common.rs     # 通用模型定义
    │   ├── monitor_event.rs  # 监控事件模型
    │   └── task.rs       # 任务模型
    ├── monitors/          # 监控模块具体业务实现
    │   ├── mod.rs        # 监控模块入口
    │   ├── traits.rs     # 监控特征定义
    │   ├── action/       # 用户行为监控
    │   ├── api/          # API请求监控
    │   ├── blank_screen/ # 白屏监控
    │   ├── device/       # 设备信息采集
    │   ├── js_error/     # JS异常监控
    │   ├── log/          # 日志监控
    │   ├── page_view/    # PV统计
    │   ├── resource_error/     # 资源加载异常
    │   ├── resource_performance/  # 资源性能
    │   ├── web_performance/   # 页面性能
    │   └── large_picture_inspect/  # 大图检测
    ├── queue/            # 消息队列
    │   ├── mod.rs       # 队列模块入口
    ├── routes/          # HTTP路由
    └── task/           # 任务处理
        ├── mod.rs      # 任务模块入口
        ├── handler.rs  # 任务处理器
        ├── consumer.rs # 消息消费者
        └── clickhouse_client.rs  # CH客户端
```

## 如何开发

我们如果需要新增一个监控类型，我们需要怎么做。我们以 page_view 监控为例。

1. 定义监控数据的 数据库表结构。

```sql

-- 定义 page_view 监控数据表结构
CREATE TABLE
    aegis.page_view_events_v1 ON CLUSTER cluster_emr (
        `app_id` LowCardinality (String) COMMENT 'app ID，每个应用的唯一标识',
        `env` LowCardinality (String) COMMENT '环境，如生产环境或测试环境',
        `log_timestamp` DateTime COMMENT '记录日志的时间',
        `event_timestamp` DateTime COMMENT '客户端上报事件发生的时间戳，表示事件实际发生的时间',
        `session_id` String COMMENT '会话的唯一标识符，用于追踪用户的会话',
        `did` LowCardinality (String) COMMENT '设备ID，标识上报日志的设备',
        `user_id` String COMMENT '用户的唯一标识符，用于追踪特定用户的行为',
        `event_url` LowCardinality (String) COMMENT '事件关联的URL，可用于追踪事件发生的具体页面',
        `sdk_name` LowCardinality (String) COMMENT '追踪使用的SDK的名称，用于识别数据收集工具',
        `sdk_version` LowCardinality (String) COMMENT '追踪使用的SDK的版本，帮助识别可能的版本差异问题',
        `client_ip` LowCardinality (String) COMMENT '客户端IP，用于地理位置分析或安全审计',
        `cid` LowCardinality (String) COMMENT 'cid',
        `page_id` LowCardinality (String) COMMENT '页面访问事件的唯一标识符（页面ID）',
        `source` Enum8 (
            'auto' = 1,
            'manually' = 2,
            'pushState' = 3,
            'replaceState' = 4,
            'hash' = 5,
            'history' = 6
        ) DEFAULT 'auto' COMMENT 'pv上报数据来源，默认为自动（auto）'
    ) ENGINE = ReplicatedMergeTree (
        '/clickhouse/tables/aegis/page_view_events_v1/{shard}/replica_{replica}',
        '{replica}'
    )
PARTITION BY
    (app_id, toYYYYMMDD (log_timestamp))
ORDER BY
    (
        log_timestamp,
        app_id,
        user_id,
        page_id,
        cid,
        source
    ) TTL log_timestamp + toIntervalDay (90) SETTINGS index_granularity = 8192;


-- 定义 page_view 监控数据分布表映射
CREATE TABLE
    aegis.page_view_events_distributed ON CLUSTER cluster_emr AS aegis.page_view_events_v1 ENGINE = Distributed (
        'cluster_emr',
        'aegis',
        'page_view_events_v1',
        cityHash64 (app_id)
    );
```

2. 在 `src/monitors/` 目录下新增一个文件夹, 文件夹名称为监控类型，例如 `api`。
3. 在 `src/monitors/page_view/` 目录下新增 `mod.rs` 用于导出监控类型，`models.rs` 定义监控类型数据结构，`monitor.rs` 实现监控类型具体业务逻辑。

```rust
 //  src/monitors/page_view/models.rs
 // 用于定义事件数据结构

/**
 * 定义上报payload 数据结构
 */
 #[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Payload {
    pub pid: String,
    pub source: String,
}

/**
 * 数据库数据结构
 */
#[derive(Row, Serialize, Deserialize, Debug)]
pub struct DBRow {
    pub app_id: String,
    pub cid: String,
    pub env: String,
    #[serde(with = "clickhouse::serde::time::datetime")]
    pub log_timestamp: OffsetDateTime,
    #[serde(with = "clickhouse::serde::time::datetime")]
    pub event_timestamp: OffsetDateTime,
    pub session_id: String,
    pub did: String,
    pub user_id: String,
    pub event_url: String,
    pub sdk_name: String,
    pub sdk_version: String,
    pub client_ip: String,
    pub page_id: String,
    pub source: PvSource,
}


```

```rust
// src/monitors/page_view/monitor.rs
// 用于实现 PV 监控具体业务逻辑, 实现 Monitor 特征

/**
 * PV 监控实现
 */
impl Monitor for PageViewMonitor {
    type Payload = Payload;

    type DBRow = DBRow;

     /**
      * 获取 Clickhouse 表名
      */
    fn get_table_name(&self) -> String {
        "page_view_events_v1".to_string()
    }

    /**
     * 序列化上报数据
     */
    fn serialize_payload(&self, data: &JsonValue) -> Result<Self::Payload, serde_json::error::Error> {
        Ok(Payload::deserialize(data)?)
    }

    /**
     * 将上报事件数据转换为数据库数据
     */
    fn task_to_db_row(&self, task_data: TaskData) -> Result<Self::DBRow, serde_json::error::Error> {
        let common = match &task_data.data {
            MonitorEvent::PageView { common, .. } => common,
            _ => return Err(serde::de::Error::custom("Expected PageView common")),
        };

        let payload = match &task_data.data {
            MonitorEvent::PageView { payload, .. } => payload,
            _ => {
                return Err(serde::de::Error::custom("Expected PageView payload"));
            }
        };

        let client_ip = format!("{}", task_data.client_ip); // 转换 IpAddr 为 String

        let timestamp = task_data
            .timestamp
            .duration_since(std::time::SystemTime::UNIX_EPOCH)
            .expect("Time went backwards");

        //  将 UNIX 时间戳转换为 DateTime<Utc>
        let log_timestamp = OffsetDateTime::from_unix_timestamp(timestamp.as_secs() as i64)
            .expect("Invalid timestamp");

        let event_time = match OffsetDateTime::from_unix_timestamp(common.timestamp / 1000) {
            Ok(time) => time,
            Err(e) => {
                return Err(serde::de::Error::custom(format!(
                    "Invalid event timestamp: {}",
                    e
                )));
            }
        };

        let row = DBRow {
            app_id: common.id.clone(),
            cid: common.cid.clone(),
            env: common.env.clone(),
            log_timestamp: log_timestamp,
            event_timestamp: event_time,
            session_id: common.sid.clone(),
            did: common.did.clone(),
            user_id: common.uid.clone(),
            event_url: common.url.clone(),
            sdk_name: common.sdk_name.clone(),
            sdk_version: common.sdk_version.clone(),
            client_ip: client_ip,
            page_id: payload.pid.clone(),
            source: PvSource::from(payload.source.clone()),
        };

        Ok(row)
    }
}

```

```rust
// src/monitors/page_view/mod.rs
// 用于导出 PV 监控

pub mod models;
pub mod monitor;

pub use models::*;
pub use monitor::*;

```

项目仓库地址：https://code.soulapp-inc.cn/h5/aegis/log-service
