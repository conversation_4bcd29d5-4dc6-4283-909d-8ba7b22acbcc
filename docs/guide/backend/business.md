<!--
业务服务：主要技术栈使用到了 node + nestjs + mysql + redis。
业务服务部署在Kubernetes，主要的功能模块有： 数据分析服务， 告警服务，项目管理等服务
数据分析服务：提供了数据查询，数据分析，数据可视化等服务
告警服务：提供了告警规则配置，告警通知，告警处理等服务
项目管理服务：提供了用户管理，项目管理，项目配置，项目监控等服务
 -->

# 业务服务

业务服务目前主要负责数据分析，告警，项目管理等除了日志采集服务外的大多数业务都集中在这个服务中。
是 Aegis 中的数据和业务中枢，负责数据分析，告警，项目管理等。

## 技术栈

- node + nestjs

使用 node 作为开发语言，主要考虑到对前端开发人员友好，上手容易，社区活跃，文档齐全，生态丰富。nestjs 作为框架，应该是目前nodejs 生态中最好的服务端开发框架。经过多年的发展，生态已经非常完善，文档齐全，社区活跃，生态丰富。

- mysql
- redis
- clickhouse

mysql + redis + clickhouse 也为系统提供了完备的存储能力，和pod 之间的数据交互能力

## 项目模块

- 任务调度模块

  任务调度模块作为基础模块主要负责为其他模块提供任务调度能力，包括定时任务，周期任务，一次性任务等。

- 文件模块

  文件管理模块也是基础模块，主要负责文件的上传，下载，删除，查询等服务。

- 用户/团队管理模块：

  用户/团队管理模块主要负责用户管理，用户权限管理，用户角色管理等服务。

- 应用管理模块:

  项目管理模块主要负责项目管理，项目配置，项目监控等服务。

- 数据分析模块

  数据分析模块主要负责数据查询，数据分析，数据可视化等服务。

- 告警模块

  告警模块主要负责告警规则的配置，告警通知，告警处理等服务。 保证所监控的指标在异常时能够及时通知到相关人员。

- 更多模块待建设

<p align="center" >
  <img src="/backend/business.png" width="100%" />
</p>
