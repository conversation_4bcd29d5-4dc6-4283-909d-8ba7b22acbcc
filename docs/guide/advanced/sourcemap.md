# SourceMap 上传

通过 Aegis SourceMap 上传工具，在打包时自动上传 SourceMap 文件，可以实现错误堆栈的还原，从而提高错误排查的效率：

- Vite 插件：[@clover/vite-plugin-aegis-sourcemap](https://npm.soulapp-inc.cn/package/@clover/vite-plugin-aegis-sourcemap)
- Webpack 插件：[@clover/webpack-plugin-aegis-sourcemap](https://npm.soulapp-inc.cn/package/@clover/webpack-plugin-aegis-sourcemap)

## 注意事项

> [!IMPORTANT]
> 请确保在打包配置中开启生成 SourceMap 功能。

> [!TIP]
> 如需在上传 SourceMap 后删除 SourceMap 文件，请在 `package.json` 的 `scripts` 中添加删除命令：
>
> ```bash
> "postbuild": "find {outDir} -type f -name '*.map' -delete",
> ```

## 插件参数

| 参数     | 类型    | 默认值  | 是否必填 | 说明                   |
| -------- | ------- | ------- | -------- | ---------------------- |
| outDir   | string  | -       | 是       | js 文件输出目录        |
| appKey   | string  | -       | 是       | 应用标识，每个项目唯一 |
| token    | string  | -       | 是       | 认证密钥，每个项目唯一 |
| platform | string  | -       | 是       | 平台类型               |
| enabled  | boolean | `true`  | 否       | 是否启用               |
| debug    | boolean | `false` | 否       | 开启后会打印更多日志   |

## 使用方法

### 安装

::: code-group

```bash [Vite]
npm install @clover/vite-plugin-aegis-sourcemap@latest -D
```

```bash [Webpack]
npm install @clover/webpack-plugin-aegis-sourcemap@latest -D
```

:::

### 配置

::: code-group

```javascript [Vite]
import { AegisSourcemap } from '@clover/vite-plugin-aegis-sourcemap'

export default defineConfig({
  plugins: [
    AegisSourcemap({
      outDir: 'xxx',
      appKey: 'xxx',
      token: 'xxx',
      platform: 'xxx',
      enabled: true,
      debug: false
    })
  ]
})
```

```javascript [Webpack]
const { AegisSourcemap } = require('@clover/webpack-plugin-aegis-sourcemap')

module.exports = {
  plugins: [
    new AegisSourcemap({
      outDir: 'xxx',
      appKey: 'xxx',
      token: 'xxx',
      platform: 'xxx',
      enabled: true,
      debug: false
    })
  ]
}
```

:::
