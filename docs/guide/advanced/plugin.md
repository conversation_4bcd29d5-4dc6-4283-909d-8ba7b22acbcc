# 插件开发

Aegis SDK 提供了强大的插件机制和全面的生命周期 API，使开发者能够轻松扩展 SDK 功能，自定义数据的收集、处理和传输流程，从而满足各种复杂的监控需求。

### 插件结构

一个典型的 Aegis 插件应该包含以下结构：

```typescript
interface Integration {
  name: string // 插件的唯一标识符
  options?: Record<string, any> // 插件的配置选项
  tearDown?: () => void // 可选的插件销毁方法，用于清理资源
  setup: (aegis: Aegis) => void // 插件初始化方法，在 Aegis 实例创建时调用
}
```

### 开发步骤

1. 定义插件接口类型

```typescript
interface PluginReport {
  // 定义插件上报的数据结构
}

interface PluginOptions {
  // 定义插件的配置选项
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends PluginReport>(report: P): void
  }
}
```

2. 创建插件工厂函数

创建一个返回 `Integration` 对象的工厂函数：

```typescript
export default function (options?: PluginOptions): Integration {
  // 初始化插件逻辑
  return {
    name: 'yourPluginName',
    setup(aegis: Aegis) {
      // 设置插件逻辑
    },
    tearDown() {
      // 清理逻辑
    }
  }
}
```

3. 实现插件逻辑

在 `setup` 方法中实现插件的核心逻辑：

- 监听全局事件&初始化变量等
- 监听 aegis 实例的生命周期事件
- 收集数据并调用 `aegis.report` 方法上报数据

4. 提供清理机制

在 `tearDown` 方法中实现资源清理逻辑，确保插件可以被正确卸载。

### 示例

::: code-group

```typescript [interface]
interface NetworkReport {
  type: 'network'
  payload: {
    online: boolean
    effectiveType?: string
  }
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    network(online: boolean): void
    report<P extends NetworkReport>(report: P): void
  }
}
```

```typescript [plugin]
import type { Integration } from '@aegis/sdk'

export default function networkPlugin(): Integration {
  function reportNetwork(online: boolean) {
    const report: NetworkReport = {
      type: 'networ',
      payload: { online }
    }

    if ('connection' in navigator && navigator.connection) {
      report.payload.effectiveType = navigator.connection.effectiveType
    }

    aegis.report(report)
  }

  function handleOnline() {
    reportNetwork(true)
  }

  function handleOffline() {
    reportNetwork(false)
  }

  return {
    name: 'network',
    setup(aegis) {
      // 上报当前网络状态
      reportNetwork(navigator.onLine)

      // 监听网络状态变化
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)

      // 提供API，供用户手动上报网络状态
      aegis.provide('network', reportNetwork)
    },
    tearDown() {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }
}
```

```typescript [usage]
import Aegis from '@aegis/sdk'
import networkPlugin from './networkPlugin'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [networkPlugin()]
})
```

:::

### 注意事项

> [!IMPORTANT]
> 插件应该遵循单一职责原则，专注于特定的功能领域。

> [!TIP]
> 在开发过程中，充分考虑不同环境的兼容性问题。

> [!CAUTION]
> 插件的行为不应该对用户的应用造成明显的性能影响。必要时，考虑提供配置选项来控制插件的行为。
