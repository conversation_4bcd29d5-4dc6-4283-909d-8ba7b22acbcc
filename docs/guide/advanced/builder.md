# Builder 开发

Builder 是 Aegis SDK 数据上报流程中的一个重要环节。它负责处理和转换由插件收集的原始数据，并生成通用标准格式的上报数据结构。

## Builder 的作用

Builder 的主要职责包括：

1. 接收收集到的原始数据
2. 添加通用字段（如 `id`、`uid`、`timestamp`、`sdk_name` 等）
3. 根据项目类型添加特定字段（如浏览器环境中的 `url`、`network` 等）
4. 返回处理后的数据结构，如果返回 `Falsy` 值，则表示该数据将被忽略不上报

## 内置 Builder

Aegis SDK 为不同的项目类型提供了专门的 Builder 实现。

### `browserBuilder`

用于浏览器环境的 Builder。

```typescript
export function browserBuilder(data: CustomReport, configManager: ConfigManager) {
  const url = isBrowser ? location.href : ''
  const common: Record<string, string | number | undefined> = {
    url: url,
    timestamp: now(),
    sdk_name: SDK_NAME
    // ...etc
  }

  const result = {
    common,
    type: data.type,
    payload: data.payload
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}
```

### `nodejsBuilder`

待开发后补充

### `miniappBuilder`

待开发后补充

### `reactNativeBuilder`

待开发后补充

### 自定义 Builder

Aegis SDK 允许开发者通过配置自定义 Builder 函数来满足特定需求。

如果配置了自定义 Builder 函数，则 Aegis SDK 将会使用该函数来处理数据，而不会使用内置的 Builder 函数。

```typescript
const customBuilder = (data: CustomReport, configManager: ConfigManager) => {
  const common = {
    timestamp: Date.now(),
    id: configManager.getConfigValue('id'),
    uid: configManager.getConfigValue('uid'),
    custom_field: 'custom value'
  }

  const result = {
    common,
    type: data.type,
    payload: {
      ...data.payload
    }
  }

  if (data.immediate !== undefined) {
    result.immediate = data.immediate
  }

  return result
}

const aegis = new Aegis({
  id: 'your-project-id',
  builder: customBuilder
})
```

### 注意事项

> [!TIP]
> 确保包含所有必要的字段，特别是 `id`、`timestamp` 等关键信息。可以利用 `configManager` 获取当前的配置值。
