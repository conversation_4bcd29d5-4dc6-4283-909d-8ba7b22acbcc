# 开发约定

- 使用 `TypeScript` 语言，如无必要，不要使用 `any` 类型
- 每个功能点插件的实现方式和注意点必须有完整的文档说明
- 每个插件必须有相应的测试用例，且测试用例覆盖率 100%
- SDK 的私有属性全部都是以两个下划线开头命名(`__inited`)，在代码打包时，以这种风格命名的变量全部会被压缩换名，所以不要依赖这种变量
- SDK 应该尽量的小，所以不可以使用 ES5+ 的语法（2009年），比如`箭头函数`，`rest语法`，因为它们在打包时，`TypeScript` 会带来 `Polyfill` 代码的增加
- 为满足低端浏览器，在开发时要考虑容错处理，比如 PV 插件在监听 `pushState` 和 `replaceState` 事件时，会考虑是否有 `history` 对象可用，虽然 `history` 对象已经在绝大多数浏览器上被实现
- 如果插件使用了浏览器全局资源，比如监听某个浏览器事件，`hook`某个原生对象，一定要在插件的`tearDown`时机做回置处理
