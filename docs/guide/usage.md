# 接入方式

Aegis SDK 提供两种接入方式：CDN 接入和 NPM 包接入。两种方式各有优势，主要区别如下：

- 版本更新：
  - CDN 接入：只需在监控平台选择最新版本进行配置即可完成更新。
  - NPM 包接入：需要在项目中手动更新 SDK 版本并重新部署上线。
- 监控时机：
  - CDN 异步接入：通过内嵌的 JavaScript 脚本异步加载 SDK，同时预收集代码异常信息。
  - NPM 包接入：需等待业务代码加载 SDK 后才开始监控异常。

鉴于上述特点，建议在高要求的监控场景下优先采用 CDN 接入方式。

## CDN 接入

在 HTML 文档的 `<head>` 标签中添加以下代码即可完成接入。

CDN 接入支持同步加载和异步加载两种方式。异步加载的优势在于不会阻塞页面其他资源的加载，同时引入了错误预收集机制。

> [!TIP]
> 使用插件前，必须引入相应的插件代码。

::: code-group

```html [同步加载]
<script src="{CDN_URL}"></script>
<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [
      Aegis.api(), // HTTP 请求监控插件
      Aegis.pageview() // 页面访问(PV)监控插件
    ],
    transports: [
      new Aegis.transports.Console(), // 控制台输出(用于调试)
      new Aegis.transports.Pixel('{DATA_REPORT_URL}'), // 像素上报(URL 必须返回 1x1 gif 图片)
      new Aegis.transports.Http('{DATA_REPORT_URL}') // HTTP POST 方式上报
    ]
  })
</script>
```

```html [异步加载]
<script>
  ;(function e(scriptUrl, globalName, callback) {
    var e = [],
      n = window,
      t = 'precollect',
      o = 'addEventListener',
      r = document.createElement('script'),
      c = 'removeEventListener',
      i = [
        'on',
        'off',
        'emit',
        'contains',
        'setConfig',
        'provide',
        'report',
        'build',
        'send',
        'destroy',
        'sendPV',
        t,
        'reportBridgeRequest',
        'reportResourceError',
        'reportHttpRequest',
        'captureException'
      ]
    function s(e) {
      var o = (e = e || n.event).target || e.srcElement
      o instanceof Element ? n[globalName][t]('sr', o, a()) : n[globalName][t]('js', e, a())
    }
    function u(e) {
      ;(e = e || n.event), n[globalName][t]('js', e, a())
    }
    function a() {
      return Date.now ? Date.now() : +new Date()
    }
    ;(n[globalName] = n[globalName] || {}),
      i.forEach(function (t) {
        n[globalName][t] = function () {
          e.push([t, arguments])
        }
      }),
      (r.src = scriptUrl),
      (r.crossOrigin = 'anonymous'),
      (r.onload = function () {
        callback(),
          n[c]('error', s, !0),
          n[c]('unhandledrejection', u, !0),
          e.forEach(function (e) {
            var t = n[globalName][e[0]]
            t ? t.apply(n[globalName], e[1]) : console.error('Plugin not found:', e[0])
          }),
          (e.length = 0)
      }),
      n[o]('error', s, !0),
      n[o]('unhandledrejection', u, !0),
      document.getElementsByTagName('head')[0].appendChild(r)
  })('{CDN_URL}', 'aegis', function () {
    window['aegis'] = new Aegis({
      id: '{APP_ID}',
      integrations: [
        Aegis.precollect(), // 预收集插件
        Aegis.api(), // HTTP 请求监控插件
        Aegis.pageview() // 页面访问(PV)监控插件
      ],
      transports: [
        new Aegis.transports.Console(), // 控制台输出(用于调试)
        new Aegis.transports.Pixel('{DATA_REPORT_URL}'), // 像素上报(URL 必须返回 1x1 gif 图片)
        new Aegis.transports.Http('{DATA_REPORT_URL}') // HTTP POST 方式上报
      ]
    })
  })
</script>
```

:::

## NPM 包接入

### 安装

使用 npm 包管理器安装 Aegis SDK：

```js
npm install @aegis/sdk --save
```

### 使用

```js
import Aegis from '@aegis/sdk'

// 按需导入所需插件
import { api } from '@aegis/sdk/integrations/api'
import { pageview } from '@aegis/sdk/integrations/pageview'

// 按需导入数据传输方式
import { Http } from '@aegis/sdk/transports/http'
import { Pixel } from '@aegis/sdk/transports/pixel'
import { Console } from '@aegis/sdk/transports/console'

// 初始化 Aegis 实例
const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [
    api(), // HTTP 请求监控插件
    pageview() // 页面访问(PV)监控插件
  ],
  transports: [
    new Console(), // 控制台输出(用于调试)
    new Pixel('{DATA_REPORT_URL}'), // 像素上报(URL 必须返回 1x1 gif 图片)
    new Http('{DATA_REPORT_URL}') // HTTP POST 方式上报
  ]
})
```
