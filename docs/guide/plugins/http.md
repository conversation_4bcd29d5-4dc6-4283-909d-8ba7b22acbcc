# HTTP 插件

HTTP 插件用于把采集到的数据上传到服务端。因为是 `POST` 请求，所以可以发送大量数据。但需要服务端对接口进行 `CORS` 设置。

HTTP 插件支持数据批量发送和请求重试，同时在页面卸载时会使用 `navigator.sendBeacon` 发送数据，强烈建议使用该插件发送数据。

### 使用

使用 HTTP 插件，只需在初始化 Aegis 实例时将其添加到 `transports` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    transports: [new Aegis.transports.Http('{DATA_REPORT_URL}')]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { Http } from '@aegis/sdk/transports/http'

const aegis = new Aegis({
  id: '{APP_ID}',
  transports: [Http('{DATA_REPORT_URL}')]
})
```

:::

#### 配置选项

- `reportUrl`: string - 用于报告数据的服务器端点 URL
- `options`: object (可选) - 插件的配置选项，包括：
  - `batch`: boolean - 是否启用批量发送。默认为 `false`
  - `retries`: number - 请求失败时的重试次数。默认为 `3`
  - `batchCount`: number - 触发批量发送的数据条数，默认为 `10`
  - `retryDelay`: number - 重试之间的延迟时间（毫秒）。默认为 `2000`
  - `batchInterval`: number - 批量发送的时间间隔（毫秒），默认为 `5000`

### 数据格式

HTTP 插件发送的数据格式如下：

```json
{
  "d": [
    // 采集到的数据对象数组
  ]
}
```

### 实现原理

HTTP 插件的核心实现包括以下几个部分：

- 批量发送

当启用批量发送时，插件会将数据缓存起来，并在达到指定条数或时间间隔时发送。

```ts
this.__batchCache.push(data)

if (this.__batchCache.length === 1) {
  const ctx = this

  this.__batchTimeoutID = setTimeout(function () {
    ctx.__batchTimeoutID = -1
    ctx.__doBatchRequest()
  }, this.__options.batchInterval)
}

if (this.__batchCache.length >= this.__options.batchCount) {
  this.__doBatchRequest()
}
```

- 同构请求

插件会根据环境选择使用 `XMLHttpRequest` 或 `fetch` 发送请求。

```ts
function request<T>(url: string, data: T, callback: (err: Error | null, responseText?: string) => void, retries?: number, retryDelay?: number) {
  if (isBrowser) {
    xhrRequest(url, data, callback, retries, retryDelay)
  } else {
    fetchRequest(url, data, callback, retries, retryDelay)
  }
}
```

- 页面卸载时发送

插件会在页面卸载时尝试使用 `navigator.sendBeacon` 发送剩余的数据。

```ts
if (!this.__isUnloadHandlerAttached && isBrowser) {
  this.__isUnloadHandlerAttached = true

  const ctx = this
  let dataSent = false

  const sendBeaconData = function () {
    if (ctx.__batchCache.length > 0 && 'sendBeacon' in navigator && !dataSent) {
      dataSent = navigator.sendBeacon(ctx.__reportUrl, json.stringify({ d: ctx.__batchCache }))
      ctx.__batchCache = []
    }
  }

  this.__cleanupTasks.add(attachPageUnloadEvent(sendBeaconData))
}
```

### 注意事项

> [!IMPORTANT]
> 启用批量发送可以减少网络请求次数，但可能会导致数据发送的延迟。请根据实际需求权衡配置。

> [!TIP]
> 在浏览器环境中，HTTP 插件会尝试在页面卸载时使用 navigator.sendBeacon 发送剩余数据，这有助于减少数据丢失。sendBeacon 发送数据有大小限制，详情请参考 [The 64KiB Limitation of navigator.sendBeacon and its implementation](https://blog.huli.tw/2025/01/06/en/navigator-sendbeacon-64kib-and-source-code/) 。

> [!IMPORTANT]
> 在 `WKWebView` 等环境中启用 `batch` 模式时，页面卸载事件（`unload`、`beforeunload`、`pagehide`）可能无法正常触发，造成缓存数据无法及时上报而丢失。对于重要的数据，建议使用立即上报，在上报时，把`immediate` 字段设置为 `true` 即可。详情请参考 [插件数据结构](../../config/data.md#字段说明) 。
