# Console 插件

Console 插件用于把采集到的数据输出到控制台。主要用于开发和调试。

### 使用

使用 Console 插件，只需在初始化 Aegis 实例时将其添加到 `transports` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    transports: [new Aegis.transports.Console()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { Console } from '@aegis/sdk/transports/console'

const aegis = new Aegis({
  id: '{APP_ID}',
  transports: [new Console()]
})
```

:::

### 数据格式

Console 插件会将数据以以下格式输出到控制台：

```text
Aegis SDK ----> {data}
```

其中 `{data}` 是一个对象，包含了所有采集到的信息。
