# Device 插件

Device 插件用于收集用户设备的相关信息，包括操作系统、浏览器、屏幕分辨率等。

### 使用

使用 Device 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.device()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { device } from '@aegis/sdk/integrations/device'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [device()]
})
```

:::

### 配置选项

- `extra`：设备信息自定义提取函数，接收 `userAgent` 作为参数，返回一个信息对象，该对象会与内置信息合并上报，默认为 `noop`

### 数据格式

Device 插件上报的数据格式如下：

```json
{
  "type": "device",
  "payload": {
    "os": "Windows",
    "sr": "1920x1080",
    "dpr": 1,
    "lang": "zh-CN",
    "device": "pc",
    "engine": "WebKit",
    "browser": "Chrome",
    "engineVer": "537.36",
    "browserVer": "91.0.4472.124"
  }
}
```

#### 字段说明

- `os`：操作系统
- `sr`：屏幕分辨率
- `dpr`：设备像素比
- `lang`：浏览器语言
- `device`：设备类型
- `engine`：浏览器引擎
- `browser`：浏览器名称
- `engineVer`：浏览器引擎版本
- `browserVer`：浏览器版本

### 实现原理

Device 插件主要利用 `navigator.userAgent` 属性，结合正则表达式和其他浏览器 API 来获取设备相关信息。其核心实现包括以下几个部分：

- 设备信息识别

通过检测特定关键词来判断设备类型。

```ts
function getDeviceType(userAgent: string): string {
  if (/Tablet|iPad/.test(userAgent)) return 'tablet'
  if (/Mobile|Android|iP(hone|od|ad)|Windows Phone/.test(userAgent)) return 'mobile'
  return userAgent === Unknown || !userAgent ? Unknown : 'pc'
}
```

- 操作系统识别

使用正则表达式匹配 userAgent 和 platform 字符串来识别操作系统。

```ts
function getOS(userAgent: string, platform: string): string {
  if (/Android/.test(userAgent)) return 'Android'
  if (/iPhone|iPad|iPod/.test(userAgent)) return 'IOS'
  if (/Windows/.test(userAgent)) return 'Windows'
  // ... 其他操作系统判断
  return Unknown
}
```

- 浏览器识别

通过一系列正则表达式来识别不同的浏览器。

```ts
function getBrowser(userAgent: string): string {
  if (/Edge\//.test(userAgent)) return 'Edge'
  if (/Chrome\//.test(userAgent) && !/Edg/.test(userAgent)) return 'Chrome'
  if (/Firefox\//.test(userAgent)) return 'Firefox'
  // ... 其他浏览器判断
  return Unknown
}
```

- 其他信息识别
  - 浏览器和引擎版本：使用正则表达式提取版本号
  - 屏幕分辨率：通过 `window.screen.width` 和 `window.screen.height` 获取
  - 设备像素比：使用 `window.devicePixelRatio`
  - 浏览器语言：通过 `navigator.language` 获取

### 类型定义

```typescript
type DeviceReport = {
  type: 'device'
  payload: {
    os: string
    sr: string
    dpr: number
    lang: string
    device: string
    engine: string
    browser: string
    engineVer: string
    browserVer: string
  }
}
```
