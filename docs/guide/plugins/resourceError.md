# ResourceError 插件

ResourceError 插件主要用于捕获和报告页面中资源加载失败的错误。它不仅能自动捕获资源加载错误，同时也提供了手动报告资源错误的方法。

### 使用

使用 ResourceError 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.resourceError()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { resourceError } from '@aegis/sdk/integrations/resourceError'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [resourceError()]
})
```

:::

#### 手动报告资源 Error 请求

在某些情况下，业务逻辑可能需要手动报告 resource Error 请求。resourceError 插件提供了 `reportResourceError` 方法来实现这一功能：

该方法接收两个参数：

- `target`: 类型为 `EventTarget`，表示发生错误的目标元素
- `timestamp`: 表示错误发生的时间戳

```javascript
const element = document.querySelector('div')

aegis.reportResourceError(element, Date.now())
```

### 数据格式

ResourceError 插件上报的数据格式如下：

```json
{
  "type": "resource_error",
  "payload": {
    "url": "https://example.com/failed-resource.js",
    "xpath": "HTML > BODY > SCRIPT:nth-child(3)",
    "tagName": "script",
    "timestamp": 1628000000000,
    "integrity": "sha384-oqVuAfXRKap7fdgcCY5uykM6+R9GqQ8K/uxy9rx7HNQlGYl1kPzQho1wx4JwY8wC",
    "timing": {
      "duration": 1000,
      "startTime": 100,
      "fetchStart": 110
      // ... 其他timing字段
    }
  }
}
```

#### 字段说明

- `url`: 加载失败的资源URL
- `tagName`: 资源元素的标签名
- `timestamp`: 错误发生的时间戳
- `integrity`: 资源的完整性校验值
- `xpath`: 资源元素在 DOM 树中的 `XPath` 路径
- `timing`: 资源加载的详细性能数据, 详情见 [Resource 插件文档](/guide/plugins/resource#%E6%95%B0%E6%8D%AE%E6%A0%BC%E5%BC%8F)

### 实现原理

ResourceError 插件的核心实现包括以下几个部分：

- 错误监听

在页面初始化时，插件会添加全局的 `error` 事件监听器，捕获资源加载失败的错误。

```javascript
function resourceErrorListener(event: ErrorEvent) {
  event = event || window.event
  const target = event.target || event.srcElement

  validateParams(target, function (url, xpath) {
    reportResourceError(aegis, buildPayload(url, xpath, <Element>event.target))
  })
}
```

- 错误数据构建

插件会收集详细的错误信息，包括资源URL、DOM位置、加载性能等：

```javascript
function buildPayload(url: string, xpath: string, target: Element, timestamp?: number) {
  const timing = getResourceTiming(url)
  const payload: ResourceError = {
    url: url,
    xpath: xpath,
    timestamp: timestamp || now(),
    tagName: target.tagName.toLowerCase()
  }

  if (target.hasAttribute('integrity')) {
    payload.integrity = target.getAttribute('integrity') as string
  }

  if (timing instanceof PerformanceResourceTiming) {
    payload.timing = createTiming(timing)
  }

  return payload
}
```

### 注意事项

> [!IMPORTANT]
> 为了获取最准确的资源加载错误信息，请确保 ResourceError 插件在其他可能影响资源加载的插件之前初始化。

> [!TIP]
> 使用 `reportResourceError` 方法可以捕获一些自动监听可能漏掉的资源错误，特别是在动态加载资源时。

### 类型定义

```typescript
interface ResourceError {
  url: string
  xpath?: string
  tagName: string
  timestamp?: number
  integrity?: string
  timing?: PerformanceResourceTiming
}

type ResourceErrorReport = {
  type: 'resource_error'
  payload: ResourceError
}
```
