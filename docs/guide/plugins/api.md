# API 插件

API 插件主要用于统计应用发出的 HTTP 请求。它通过代理原生的 `fetch` 和 `XMLHttpRequest` 函数来拦截请求，获取请求的详细信息，并根据配置决定是否报告这些数据。

### 使用

使用 API 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.api()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { api } from '@aegis/sdk/integrations/api'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [api()]
})
```

:::

#### 配置选项

- `reportAll`：是否报告所有 API 请求，包括成功的请求，默认为 `false`，只报告失败的请求
- `ignoreUrls`：需要忽略的 API 请求列表，可以是字符串或正则表达式，默认为空数组
- `reportPolicy`：自定义报告策略函数，接收响应文本作为参数，返回布尔值决定是否报告该请求，默认为 `undefined`

#### 手动报告 API 请求

在某些情况下，业务逻辑可能需要手动报告 API 请求。API 插件提供了 `reportHttpRequest` 方法来实现这一功能：

该方法接收一个对象参数，表示要报告的请求信息。例如：

```javascript
aegis.reportHttpRequest({
  ok: false,
  query: '',
  status: 404,
  method: 'GET',
  duration: 200,
  requestType: 'fetch',
  url: 'https://api.example.com/data',
  base: 'https://api.example.com/data'
})
```

### 数据格式

API 插件上报的数据格式如下：

```json
{
  "type": "api",
  "payload": {
    "url": "https://api.example.com/data?id=123",
    "ok": true,
    "base": "https://api.example.com/data",
    "query": "id=123",
    "status": 200,
    "method": "GET",
    "duration": 150,
    "headers": "{\"Content-Type\":\"application/json\"}",
    "requestData": "{\"param\":\"value\"}",
    "responseData": "{\"result\":\"success\"}",
    "requestType": "fetch"
  }
}
```

#### 字段说明

- `url`：完整的请求 URL
- `status`：HTTP 状态码
- `query`：URL 的查询参数部分
- `duration`：请求持续时间（毫秒）
- `method`：HTTP 方法（`GET`, `POST` 等）
- `base`：URL 的基础部分（不包含查询参数）
- `requestType`：请求类型（`fetch` 或 `xhr`）
- `ok`：请求是否成功（状态码在 200-299 之间）
- `headers`：请求头（仅在请求失败时包含）
- `requestData`：请求体数据（仅在请求失败时包含）
- `responseData`：响应数据（仅在请求失败时包含）

### 实现原理

API 插件的核心实现包括以下几个部分：

- 拦截 `fetch` 请求

插件通过代理系统的 `fetch` 函数来拦截 `fetch` 请求。

```javascript
aop(window, 'fetch', function (original, args) {
  const cloneResponse = response.clone()
  // 拦截逻辑

  return original.apply(window, args).then(
    (resp) => {
      // 拦截逻辑
      return resp
    },
    (error) => {
      // 拦截逻辑
      throw error
    }
  )
})
```

- 拦截 `XMLHttpRequest`

插件通过代理浏览器的 `XMLHttpRequest.prototype` 的 `open` 和 `send` 方法来拦截 `XHR` 请求。

```javascript
const KEY = '__xhr_data__'

aop(XMLHttpRequest.prototype, 'open', function (original, args) {
  this[KEY] = {
    // 临时存储请求信息
  }
  // 拦截逻辑
})

aop(XMLHttpRequest.prototype, 'send', function (original, args) {
  // 拦截逻辑
})
```

### 注意事项

> [!IMPORTANT]
> 使用 `reportPolicy` 时，请确保函数的执行效率，因为它会在每个请求完成后被调用。

> [!TIP]
> API 插件会自动忽略 Aegis SDK 自身的数据上报请求。这包括通过 `SDKReportUrl` 参数指定的 URL，以及所有上报方式中配置的 `reportUrl` 参数对应的 URL。这种设计可以防止循环上报，确保 SDK 的正常运行。

### 类型定义

```typescript
type RequestReport = {
  type: 'api'
  payload: {
    url: string
    ok: boolean
    base: string
    query: string
    status: number
    method: string
    duration: number
    headers?: string
    requestData?: string
    responseData?: string
    requestType: 'fetch' | 'xhr'
  }
}

interface RequestOptions {
  reportAll?: boolean
  ignoreUrls?: (string | RegExp)[]
  reportPolicy?: (responseText: string) => boolean
}
```
