# Action 插件

Action 插件主要用于捕获用户在页面上的交互行为，如点击或触摸事件。它能够自动记录这些行为并上报相关数据，包括事件类型和目标元素的信息。

### 使用

使用 Action 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.action()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { action } from '@aegis/sdk/integrations/action'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [action()]
})
```

:::

### 数据格式

Action 插件上报的数据格式如下：

```json
{
  "type": "action",
  "payload": {
    "type": "pointerdown",
    "target": {
      "text": "按钮文本",
      "path": "html > body > div > button"
    }
  }
}
```

#### 字段说明

- `type`：事件类型，可能的值包括 `pointerdown` 或 `click`
- `target`：触发事件的目标元素信息
  - `text`：目标元素的文本内容
  - `path`：目标元素在 DOM 树中的路径

### 实现原理

Action 插件的核心实现包括以下几个部分：

- 事件监听

插件会根据浏览器的支持情况，选择监听 `pointerdown` 或 `click` 事件。

```typescript
const supportPointerEvent = !!window.PointerEvent
const eventName = supportPointerEvent ? 'pointerdown' : 'click'

attachEvent(eventName, clickOrPointerHandler, true)
```

- 事件处理

当捕获到用户交互事件时，插件会提取目标元素的文本内容和 DOM 路径，并通过 Aegis 实例上报数据。

```typescript
function clickOrPointerHandler(event: Event, aegis: Aegis) {
  if (event.target instanceof Element) {
    ...

    aegis.report({
      type: 'action',
      payload: {
        type: event.type,
        target: {
          text: text || '',
          path: htmlTreeStringify(event.target)
        }
      }
    })
  }
}
```

### 注意事项

> [!IMPORTANT]
> Action 插件会自动选择使用 `pointerdown` 或 `click` 事件，以确保在不同设备上的兼容性。

### 类型定义

```typescript
type ActionReport = {
  type: 'action'
  payload: {
    type: string
    target: {
      text: string
      path: string
    }
  }
}
```
