# Bridge 插件

Bridge 插件主要用于统计应用发出的 HTTP 请求，它主要用于 APP Webview 页面使用桥接方式发出请求的场景。

它通过代理 APP 注入的 `AEJSBridge.dispatch` 函数来拦截请求，获取请求的详细信息，并根据配置决定是否报告这些数据。

### 使用

使用 Bridge 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.bridge()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { bridge } from '@aegis/sdk/integrations/bridge'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [bridge()]
})
```

:::

#### 配置选项

- `ignoreUrls`：忽略的 URL 列表，列表中的 URL 不会上报。
- `reportAll`：是否报告所有桥接请求，包括成功的请求。默认为 `false`，只报告失败的请求。

#### 手动报告桥接请求

在某些情况下，业务逻辑可能需要手动报告桥接请求。Bridge 插件提供了 `reportBridgeRequest` 方法来实现这一功能：

该方法接收一个对象参数，表示要报告的请求信息。例如：

```javascript
aegis.reportBridgeRequest({
  ok: false,
  query: '',
  status: 500,
  method: 'GET',
  duration: 200,
  url: 'https://api.example.com/data',
  base: 'https://api.example.com/data'
})
```

### 数据格式

Bridge 插件上报的数据格式如下：

```json
{
  "type": "api",
  "payload": {
    "url": "https://api.example.com/data?id=123",
    "ok": true,
    "base": "https://api.example.com/data",
    "query": "id=123",
    "status": 200,
    "method": "GET",
    "duration": 150,
    "requestData": "{\"param\":\"value\"}",
    "responseData": "{\"result\":\"success\"}",
    "requestType": "bridge"
  }
}
```

#### 字段说明

- `ok`：请求是否成功
- `url`：完整的请求 URL
- `query`：URL 的查询参数部分
- `requestType`：固定为 `bridge`
- `duration`：请求持续时间（毫秒）
- `method`：HTTP 方法（`GET`, `POST` 等）
- `base`：URL 的基础部分（不包含查询参数）
- `status`：状态码（200 表示成功，500 表示失败）
- `responseData`：响应数据（仅在请求失败时包含）
- `requestData`：请求体数据（仅在请求失败时包含）

### 实现原理

Bridge 插件的核心实现包括以下几个部分：

- 拦截桥接请求

插件通过代理 `AEJSBridge.dispatch` 方法来拦截桥接请求。

```javascript
aop(window.AEJSBridge, 'dispatch', function (original, options) {
  if (options[0] && isRequestOptions(options[0])) {
    const originalCallback = options[0].callback

    options[0].callback = function (result) {
      try {
        originalCallback(result)
      } finally {
        // 拦截逻辑
      }
    }
  }

  original.apply(window.AEJSBridge, options)
})
```

- 桥接就绪检测

插件通过检查注入的 `AEJSBridge` 和 `WebViewJavascriptBridge` 对象是否存在来判断桥接是否就绪。

```javascript
bridgeReady(function () {
  // 初始化拦截逻辑
})

function bridgeReady(callback: () => void) {
  if (isBridgeReady()) {
    callback()
  } else {
    document.addEventListener('AEJSBridgeReady', function () {
      callback()
    })
  }
}

function isBridgeReady() {
  const isIOS = /iPad|iPod|iPhone|iOS/i.test(navigator.userAgent)
  const isBridgeAvailable = !!(window.AEJSBridge && window.AEJSBridge.dispatch)

  return isIOS ? !!(isBridgeAvailable && window.WebViewJavascriptBridge) : isBridgeAvailable
}
```

### 注意事项

> [!IMPORTANT]
> Bridge 插件依赖于 `AEJSBridge` 和 `WebViewJavascriptBridge` 的存在。请确保在使用此插件之前，`AEJSBridge` 和 `WebViewJavascriptBridge` 已正确初始化。

> [!TIP]
> Bridge 插件只适用于特殊的业务场景，如遇到无法满足业务场景，请参考源码自行扩展。

### 类型定义

```typescript
type RequestReport = {
  type: 'api'
  payload: {
    url: string
    ok: boolean
    base: string
    query: string
    status: number
    method: string
    duration: number
    requestType: 'bridge'
    requestData?: string
    responseData?: string
  }
}

interface RequestOptions {
  reportAll?: boolean
}
```
