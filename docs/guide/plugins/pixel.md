# Pixel 插件

Pixel 插件用于把采集到的数据上传到服务端。它通过 1x1 像素的透明 GIF 图像来发送数据，因为是 `GET` 请求，所以可以跨域发送数据。

对于大量数据，Pixel 插件会对数据进行分块发送，以避免 URL 长度限制。

### 使用

使用 Pixel 插件，只需在初始化 Aegis 实例时将其添加到 `transports` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    transports: [new Aegis.transports.Pixel('{DATA_REPORT_URL}')]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { Pixel } from '@aegis/sdk/transports/pixel'

const aegis = new Aegis({
  id: '{APP_ID}',
  transports: [Pixel('{DATA_REPORT_URL}')]
})
```

:::

#### 配置选项

- `reportUrl`: string - 用于报告数据的 1x1 透明 GIF 图像 URL

### 数据格式

Pixel 插件发送的数据格式如下：

```text
{DATA_REPORT_URL}?d={encodedData}&cid={chunkId}&n={chunkNumber}&done={isDone}
```

#### 字段说明

- `d`：string - 经过 URL 编码的数据
- `cid`：string - 当数据被分块时的唯一标识符
- `n`：number - 当前数据块的序号
- `done`number - 标识是否为最后一个分块（'1' 表示是，'0' 表示否）

### 实现原理

Pixel 插件的核心实现包括以下几个部分：

- 插件通过创建 Image 对象来发送数据

```ts
const ctx = this
const img = new Image()

img.src = url
```

- 对大量数据进行分块处理

```ts
const chunks = []
const chunkSize = 2000

for (let i = 0; i < data.length; i += chunkSize) {
  chunks.push(data.slice(i, i + chunkSize))
}

for (let i = 0; i < chunks.length; i++) {
  const isLastChunk = i === chunks.length - 1
  const url = buildUrl(this.__reportUrl, chunks[i]) + '&cid=' + cid + '&n=' + i + '&done=' + (isLastChunk ? '1' : '0')

  this.__send(url)
}
```
