# Resource 插件

Resource 插件主要用于收集页面加载的资源文件信息。它提供了两种模式来收集资源数据：页面加载时收集和持续观察收集。

### 使用

使用 Resource 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.resource()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { resource } from '@aegis/sdk/integrations/resource'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [resource()]
})
```

:::

#### 配置选项

- `batchInterval`：批量上报的时间间隔，默认为 10000 毫秒
- `ignoreTypes`：要忽略的资源类型数组，默认会忽略 `xmlhttprequest`、`fetch` 和 `beacon`
- `ignoreUrls`：要忽略的 URL 数组，可以是字符串或正则表达式

### 数据格式

Resource 插件上报的数据格式如下：

```json
{
  "type": "resource",
  "payload": {
    "data": [
      {
        "url": "https://example.com/script.js",
        "encodedBodySize": 1024,
        "duration": 100,
        "startTime": 50,
        "connectEnd": 70,
        "fetchStart": 60,
        "redirectEnd": 0,
        "responseEnd": 150,
        "connectStart": 65,
        "requestStart": 75,
        "redirectStart": 0,
        "responseStart": 140,
        "domainLookupEnd": 63,
        "domainLookupStart": 61,
        "tagName": "script",
        "secureConnectionStart": 0,
        "timeOrigin": 1628000000000
      }
    ]
  }
}
```

#### 字段说明

- `url`：资源的 URL
- `encodedBodySize`：资源的编码大小（字节）
- `duration`：资源加载持续时间
- `startTime`：资源加载开始时间
- `connectEnd`：建立连接结束时间
- `fetchStart`：开始获取资源的时间
- `redirectEnd`：重定向结束时间
- `responseEnd`：接收响应结束时间
- `connectStart`：开始建立连接的时间
- `requestStart`：开始请求资源的时间
- `redirectStart`：重定向开始时间
- `responseStart`：开始接收响应的时间
- `domainLookupEnd`：域名查找结束时间
- `domainLookupStart`：域名查找开始时间
- `tagName`：资源的初始化类型
- `secureConnectionStart`：安全连接开始时间
- `timeOrigin`：性能测量的起始时间

### 实现原理

Resource 插件的核心实现包括以下几个部分：

- 页面加载时收集

插件在页面加载完成后，使用 `performance.getEntriesByType('resource')` 获取已加载的资源信息。

```javascript
function reportPageLoadedResources(aegis: Aegis, ignoreTypes: string[], ignoreUrls: ignoreUrlTypes) {
  const resources = filterResource(performance.getEntriesByType('resource'), ignoreTypes, ignoreUrls)

  reportResourceMetrics(
    aegis,
    resources.map(function (resource) {
      return createTiming(resource)
    })
  )
}
```

- 持续观察收集

如果浏览器支持 `PerformanceObserver`，插件会创建一个观察器来监听新的资源加载事件。

```javascript
const observer = new PerformanceObserver(function (list: PerformanceObserverEntryList) {
  let resources = list.getEntries().filter(function (entry) {
    return entry.entryType === 'resource'
  }) as PerformanceResourceTiming[]

  resources = filterResource(resources, ignoreTypes, ignoreUrls)

  for (let i = 0; i < resources.length; i++) {
    batchCache.push(createTiming(resources[i]))
  }
})

observer.observe({ entryTypes: ['resource'] })
```

- 批量上报

插件会按照配置的 `batchInterval` 定期上报收集到的资源信息。

```javascript
const batchTimeoutID = setInterval(function () {
  reportResourceMetrics(aegis, batchCache.slice())
  batchCache = []
}, batchInterval)
```

### 注意事项

> [!IMPORTANT]
> 请确保正确配置 `ignoreTypes` 和 `ignoreUrls`，以避免收集不必要的资源信息，这可能会影响性能和数据量。

> [!CAUTION]
> 当浏览器不支持 `PerformanceObserver` 时，插件将无法持续观察新加载的资源，只能收集页面加载时的资源信息。

### 类型定义

```typescript
type ResourceReport = {
  type: 'resource'
  payload: {
    data: PerformanceResourceTiming[]
  }
}

type ignoreUrlTypes = (string | RegExp)[]

type resourceOption = {
  batchInterval?: number
  ignoreTypes?: string[]
  ignoreUrls?: ignoreUrlTypes
}
```
