# BlankScreen 插件

BlankScreen 插件主要用于检测页面是否出现白屏或空白状态。它能够在页面加载完成后以及URL变化时自动进行检测，并在检测到白屏时上报相关数据。

### 使用

使用 BlankScreen 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例:

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.blankscreen()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { blankscreen } from '@aegis/sdk/integrations/blankscreen'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [blankscreen()]
})
```

:::

#### 配置选项

- `skeleton`: 页面是否存在骨架屏。默认为 `false`
- `ignoreUrls`: 忽略的 URL 列表，列表中的 URL 不会上报
- `mode`: 页面路由模式，可选 `history` 或 `hash`，默认为 `history`
- `threshold`: 空元素占比阈值，超过该阈值则判定为白屏。取值范围为 0 到 1 之间的小数。默认为 `1`
- `rootSelector`: 空元素选择器，这些元素都被判定为空，一般值为 `['html', 'body', '#app']`，**必传项**

### 数据格式

BlankScreen 插件上报的数据格式如下:

```json
{
  "type": "blank",
  "payload": {
    "url": "https://example.com/page"
  }
}
```

#### 字段说明

- `url`: 发生白屏的页面 URL

#### 检测时机

- 初始检测：

  - 当 `document.readyState` 变为 `complete` 或 `load` 事件触发时，插件会将检测任务添加到 `requestIdleCallback` 队列中，以确保不影响页面的主要渲染过程。

- 检测流程：
  - 非骨架屏模式：
    1.  执行首次白屏检测
    2.  如检测到白屏，则在 2 秒后进行第二次检测
    3.  仅当两次检测结果均为白屏时，则判定为白屏
  - 骨架屏模式：
    1.  生成当前 DOM 结构的快照
    2.  2 秒后生成新的 DOM 快照
    3.  比对两次快照，如结构一致，则判定为白屏

### 实现原理

BlankScreen 插件的核心实现包括以下几个部分:

- 检测时机，插件在以下情况触发时会进行白屏检测：

  - 页面加载完成后
  - URL 发生变化时(根据配置的 `mode`)
  - 使用 `history.pushState` 或 `history.replaceState` 改变 URL 时

- 插件使用 `document.elementFromPoint` 方法在页面上选取多个点，检查这些点上的元素是否为空。

```javascript
// points 点位示意图
// +-------------------+
// | o       o       o | <- pointX & (height - pointY)
// |   o     o     o   |
// |     o   o   o     |
// |       o o o       |
// | o o o o o o o o o | <- pointX & (height / 2)
// |       o o o       |
// |     o   o   o     |
// |   o     o     o   |
// | o       o       o | <- pointX & pointY
// +-------------------+
//           ^------------- (width / 2) & pointY
//
function getElements(points: Array<Point>) {
  const elements: IElement[] = []

  for (let i = 0; i < points.length; i++) {
    const element = document.elementFromPoint(points[i].x, points[i].y)
    const selector = getElementSelector(element)

    elements.push({
      empty: false,
      point: points[i],
      selector: selector
    })
  }

  return elements
}
```

- 根据配置的 `skeleton` 和 `threshold` 参数来判断是否发生白屏:
  - 非骨架屏模式: 比较空白元素占比是否超过阈值
  - 骨架屏模式: 比较初始快照和最新快照是否相同

```javascript
function measure(elementsArchive: IElement[][], isSkeleton: boolean, threshold: number) {
  if (!isSkeleton) {
    const elements = elementsArchive[elementsArchive.length - 1]
    const emptyElements = elements.filter(function (element) {
      return element.empty
    })

    return emptyElements.length / elements.length < threshold
  } else {
    const initialElementSnapshot = getElementSnapshot(elementsArchive[0])
    const actualElementSnapshot = getElementSnapshot(elementsArchive[elementsArchive.length - 1])

    return !(initialElementSnapshot === actualElementSnapshot)
  }
}
```

### 注意事项

> [!TIP]
> 白屏检测任务会在 `requestIdleCallback` 中执行，以避免阻塞主线程。该 `requestIdleCallback` 已默认进行过 Polyfill 处理。

> [!IMPORTANT]
> 请确保在使用 BlankScreen 插件时，正确配置 `rootSelector` 以排除可能被误判为空白的合法元素(如加载中的占位符)。

> [!CAUTION]
> 在某些复杂的单页应用中，可能需要调整 `threshold` 值以获得更准确的白屏检测结果。

### 类型定义

```typescript
type BlankScreenReport = {
  type: 'blank'
  payload: {
    url: string
  }
}

interface BlankScreenOptions {
  skeleton?: boolean
  threshold?: number
  mode?: 'history' | 'hash'
  rootSelector?: string[] | string
}

interface Point {
  x: number
  y: number
}

interface IElement {
  empty: boolean
  point: Point
  selector: string
}
```
