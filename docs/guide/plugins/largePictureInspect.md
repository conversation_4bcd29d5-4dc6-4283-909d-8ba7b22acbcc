# LargePictureInspect 插件

LargePictureInspect 插件主要用于收集页面里的大图信息，同时也提供了手动收集大图信息的方法。

### 大图计算规则

- 图片的原始文件大小超过图片 DOM 元素渲染大小的 3 倍，即为大图
- DOM 元素渲染大小计算规则：
  - `el.width * el.height * 3` （JPG 图片， 使用 `rgb` 色彩计算）
  - `el.width * el.height * 4` （非 JPG 图片， 使用 `rgba` 色彩计算）
  - 基于上面计算结果，再乘以 `dpr` 系数，即为图片 DOM 元素渲染大小

### 使用

使用 LargePictureInspect 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.largePictureInspect()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { largePictureInspect } from '@aegis/sdk/integrations/largePictureInspect'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [largePictureInspect()]
})
```

:::

### 配置选项

- `dpr`：设备像素比，默认为 3
- `ignoreUrls`：忽略的 URL 列表，可以是字符串或正则表达式
- `reportMode`：上报模式，默认为 `auto`，当设置为 `manual` 时，需要手动收集上报大图信息

### 手动报告大图信息

在某些情况下，开发者可能需要手动收集不合理图片使用信息。LargePictureInspect 插件提供了 `largePictureInspect` 方法来实现这一功能。

`largePictureInspect` 方法支持传入一个布尔值参数，当参数为 `true` 时，会自动收集上报大图信息。

```javascript
let largePictures = aegis.largePictureInspect() // 只收集大图信息，但不会自动上报

console.log(largePictures) // large pictures

largePictures = aegis.largePictureInspect(true) // 收集大图信息并上报

console.log(largePictures) // void
```

### 数据格式

LargePictureInspect 插件上报的数据格式如下：

```json
{
  "type": "LPI",
  "payload": {
    "data": [
      {
        "bg": true,
        "url": "https://example.com/image.jpg",
        "path": "body > div > img",
        "size": "100x100",
        "fileSize": 360000,
        "memorySize": 90000
      }
    ]
  }
}
```

#### 字段说明

- `bg`：是否为背景图
- `url`：图片的 URL
- `path`：图片在 DOM 树中的路径
- `size`：图片在 DOM 中的尺寸
- `fileSize`：图片的原始文件大小
- `memorySize`：根据图片 DOM 的尺寸计算出的内存大小

### 实现原理

LargePictureInspect 插件的核心实现包括以下几个部分:

- 插件在页面加载完成后，使用 `performance.getEntriesByType('resource')` 获取已加载的资源信息。

```typescript
function reportPageLoadedLargePictures() {
  const resources = filterResource(performance.getEntriesByType('resource'), ignoreUrls)
  const largePictures = getLargePictures(resources)

  if (largePictures.length) {
    reportLargePictures(aegis, largePictures)
  }
}
```

- 持续观察收集

如果浏览器支持 `PerformanceObserver`，插件会创建一个观察器来监听新的资源加载事件。

```typescript
const observer = new PerformanceObserver(function (list: PerformanceObserverEntryList) {
  let resources = list.getEntries().filter(function (entry) {
    return entry.entryType === 'resource'
  }) as PerformanceResourceTiming[]

  resources = filterResource(resources, ignoreUrls)
  const largePictures = getLargePictures(resources)

  if (largePictures.length) {
    reportLargePictures(aegis, largePictures)
  }
})

observer.observe({ entryTypes: ['resource'] })
```

- 通过图片URL 获取 DOM 树

```typescript
function findImageElements(url: string) {
  const elements = document.querySelectorAll('img[src="' + url + '"]') // 查找 img 标签

  if (elements.length) {
    return elements
  }

  const inlineRules = findInlineRules(url) // 查找 inline 样式的 background-image

  if (inlineRules.length) {
    return inlineRules
  }

  const cssRules = findCssRules(url) // 查找 css 样式的 background-image

  if (cssRules.length) {
    return cssRules
      .map(function (rule) {
        const element = document.querySelector(rule.selector)

        return element ? element : null
      })
      .filter(Boolean)
  }

  return []
}
```

- 获取图片的 DOM 尺寸

```typescript
function getElementSize(element: HTMLElement) {
  return {
    width: parseInt(window.getComputedStyle(element).width, 10),
    height: parseInt(window.getComputedStyle(element).height, 10)
  }
}
```

- 计算图片的内存大小

```typescript
function getImageMemorySize(element: HTMLElement, url: string) {
  const isJPEG = url.indexOf('.jpg') > -1 || url.indexOf('.jpeg') > -1
  // jpg 图片使用 rgb 色彩计算，非 jpg 图片使用 rgba 色彩计算
  const bytesPerPixel = isJPEG ? 3 : 4
  const size = getElementSize(element)

  return size.width * size.height * bytesPerPixel * 3 // 全部当做 3 倍图处理
}
```

### 注意事项

> [!TIP]
> 图片检测任务会在 `requestIdleCallback` 中执行，以避免阻塞主线程。该 `requestIdleCallback` 已默认进行过 Polyfill 处理。

> [!CAUTION]
> 当浏览器不支持 `PerformanceObserver` 时，插件将无法持续观察新加载的资源，只能收集页面加载时的资源信息。

> [!CAUTION]
> 该插件使用了 `PerformanceResourceTiming` 获取资源的 [`decodedBodySize`](https://developer.mozilla.org/en-US/docs/Web/API/PerformanceResourceTiming/decodedBodySize) 信息，该信息需要对资源进行的请求头配置 `Timing-Allow-Origin: *` ，否则会获取不到。

### 类型定义

```typescript
type LargePictureInspectReport = {
  type: 'LPI'
  payload: {
    data: {
      bg: boolean
      url: string
      path: string
      size: string
      fileSize: number
      memorySize: number
    }[]
  }
}

type largePictureInspectOption = {
  dpr?: number
  reportMode?: 'auto' | 'manual'
  ignoreUrls?: (string | RegExp)[]
}
```
