# Performance 插件

Performance 插件主要用于监控和报告页面的性能指标，包括 `LCP`、`FCP`、`CLS`、`TTI`、`TTFB`、`FID` 和 `SPA_LOAD`。该插件自动捕获性能数据，并在页面加载完成后上报相关信息。

## 使用

使用 Performance 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.perf({ reportType: ['LCP', 'FID'] })]
  })
</script>
```

```js [NPM]
import { Aegis, perf } from '@aegis/sdk'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [perf({ reportType: ['LCP', 'FID'] })]
})
```

:::

## 配置选项

- `reportType`：需要上报的性能指标类型，可以是 `LCP`、`FCP`、`CLS`、`TTI`、`TTFB`、`FID` 和 `SPA_LOAD` 的组合，默认为全部类型, `['LCP', 'FCP', 'CLS', 'TTI', 'TTFB', 'FID', 'SPA_LOAD']`
- `spaLoad`：SPA_LOAD 指标的配置选项，详见 [SPA_LOAD 配置](#spa_load-配置)

## 数据格式

Performance 插件上报的数据格式如下：

### LCP（Largest Contentful Paint）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "LCP",
        "value": 2500,
        "navigationType": "navigate",
        "element": "img#hero",
        "url": "https://example.com/hero.jpg",
        "timeToFirstByte": 500,
        "resourceLoadDelay": 100,
        "resourceLoadDuration": 200,
        "elementRenderDelay": 1700
      }
    ]
  }
}
```

### FCP（First Contentful Paint）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "FCP",
        "value": 1500,
        "timeToFirstByte": 500,
        "firstByteToFCP": 1000,
        "loadState": "loading"
      }
    ]
  }
}
```

### CLS（Cumulative Layout Shift）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "CLS",
        "value": 0.1,
        "largestShiftTarget": ".example",
        "largestShiftTime": 1234.56,
        "largestShiftValue": 0.05,
        "largestShiftSource": {
          "node": ".example",
          "previousRect": {
            "x": 0,
            "y": 0,
            "top": 0,
            "left": 0,
            "width": 100,
            "height": 100
          },
          "currentRect": {
            "x": 0,
            "y": 0,
            "top": 0,
            "left": 0,
            "width": 200,
            "height": 200
          }
        },
        "loadState": "dom-content-loaded"
      }
    ]
  }
}
```

### TTFB（Time to First Byte）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "TTFB",
        "value": 500,
        "waitingDuration": 100,
        "cacheDuration": 50,
        "dnsDuration": 20,
        "connectionDuration": 30,
        "requestDuration": 300
      }
    ]
  }
}
```

### FID（First Input Delay）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "FID",
        "value": 100,
        "eventTarget": "#button",
        "eventTime": 1234.56,
        "eventType": "click",
        "loadState": "dom-interactive"
      }
    ]
  }
}
```

### TTI（Time to Interactive）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "TTI",
        "longtasks": [
          {
            "start": 1000,
            "end": 2000,
            "attribution": [
              {
                "containerId": "iframe1",
                "containerName": "iframe",
                "containerSrc": "https://example.com",
                "containerType": "iframe"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### SPA_LOAD（Single Page Application Load）

```json
{
  "type": "performance",
  "payload": {
    "metrics": [
      {
        "name": "SPA_LOAD",
        "value": 3200,
        "source": "stable",
        "startTime": 1234567890123,
        "records": [
          {
            "url": "https://api.example.com/data",
            "type": "api",
            "duration": 250
          },
          {
            "url": "https://example.com/image.jpg",
            "type": "image",
            "duration": 180
          },
          {
            "url": "https://example.com/script.js",
            "type": "resource",
            "duration": 120
          }
        ],
        "startLoadTime": 1234567890000,
        "initTime": 1234567889950,
        "onStartedTime": 1234567889980,
        "isLowPowerMode": false
      }
    ]
  }
}
```

## SPA_LOAD 配置

SPA_LOAD 指标专门用于监控单页应用的加载性能，提供了丰富的配置选项：

```js
const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [
    perf({
      reportType: ['SPA_LOAD'],
      spaLoad: {
        loadTimeout: 30000, // 最大等待时间（毫秒）
        stableTimeout: 300, // 稳定检查间隔（毫秒）
        mode: 'history', // 路由模式：'history' 或 'hash'
        ignoreUrls: [
          // 忽略的 URL 列表
          /\/api\/ignore/,
          'https://example.com/ignore'
        ]
      }
    })
  ]
})
```

### 配置选项说明

- **`loadTimeout`**（默认：30000）：页面加载的最大等待时间（毫秒）。超过此时间将强制上报结果，source 为 'timeout'
- **`stableTimeout`**（默认：300）：稳定性检查的间隔时间（毫秒）。页面在此时间内无新资源加载时被认为稳定
- **`mode`**（默认：'history'）：路由模式，支持 'history' 和 'hash' 两种模式
- **`ignoreUrls`**（默认：[]）：需要忽略监控的 URL 列表，支持字符串和正则表达式

### 使用示例

#### 基础使用

```js
// 使用默认配置
const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [perf({ reportType: ['SPA_LOAD'] })]
})
```

#### 自定义配置

```js
// 针对移动端优化的配置
const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [
    perf({
      reportType: ['SPA_LOAD'],
      spaLoad: {
        loadTimeout: 20000, // 移动端网络较慢，适当延长超时时间
        stableTimeout: 500, // 增加稳定检查间隔
        mode: 'hash', // 使用 hash 路由
        ignoreUrls: [
          /\/analytics/, // 忽略统计相关请求
          /\/tracking/, // 忽略埋点请求
          'https://cdn.example.com/ads' // 忽略广告资源
        ]
      }
    })
  ]
})
```

### 最佳实践

1. **合理设置超时时间**：根据应用的实际加载情况调整 `loadTimeout`，避免过短导致误报或过长影响用户体验监控
2. **优化稳定检查间隔**：`stableTimeout` 过短可能导致频繁检查影响性能，过长可能延迟结果上报
3. **忽略不必要的资源**：通过 `ignoreUrls` 忽略统计、广告等对用户体验影响较小的资源
4. **选择合适的路由模式**：确保 `mode` 配置与应用的实际路由模式一致

### 性能考虑

- SPA_LOAD 监控会对页面性能产生一定影响，建议在生产环境中谨慎使用
- 监控过程中会创建多个观察器和事件监听器，在资源受限的环境中可能需要调整配置
- 大量的 DOM 操作和样式计算可能影响页面响应性，特别是在低端设备上

## 实现原理

### Largest Contentful Paint (LCP)

LCP 代表页面加载性能的关键指标，指示页面内容的主要部分何时完全呈现在用户屏幕上。为了捕获 LCP，使用 `PerformanceObserver` 监听 `largest-contentful-paint` 类型的性能条目。

- **关键步骤**:
  1. 使用 `PerformanceObserver` 监听 `largest-contentful-paint` 条目。
  2. 获取最新的 LCP 条目，并计算从导航开始到该条目的时间差。
  3. 如果有相关资源条目，计算资源加载延迟和持续时间。
  4. 将计算结果和相关信息（如元素、URL）上报。

### First Contentful Paint (FCP)

FCP 指示页面的第一个内容元素何时呈现在用户屏幕上。使用 `PerformanceObserver` 监听 `paint` 类型的性能条目。

- **关键步骤**:
  1. 使用 `PerformanceObserver` 监听 `paint` 条目。
  2. 获取 `first-contentful-paint` 条目，并计算从导航开始到该条目的时间差。
  3. 如果有导航条目，计算从请求开始到 FCP 的时间差。
  4. 将计算结果和相关信息（如加载状态）上报。

### Cumulative Layout Shift (CLS)

CLS 衡量页面布局的稳定性，表示页面内容在加载期间的视觉稳定性。使用 `PerformanceObserver` 监听 `layout-shift` 类型的性能条目。

- **关键步骤**:
  1. 使用 `PerformanceObserver` 监听 `layout-shift` 条目。
  2. 计算会话内的所有布局偏移值，并识别最大的偏移条目。
  3. 获取最大的布局偏移条目的详细信息（如目标元素、偏移矩形）。
  4. 将计算结果和相关信息（如加载状态）上报。

### Time to First Byte (TTFB)

TTFB 衡量从用户发出请求到接收到第一个字节响应的时间。通过 `PerformanceNavigationTiming` 获取相关数据。

- **关键步骤**:
  1. 获取导航条目。
  2. 计算从请求发出到接收到第一个字节响应的时间差。
  3. 计算等待时间、DNS 查找时间、连接时间等详细信息。
  4. 将计算结果和相关信息上报。

### First Input Delay (FID)

FID 衡量用户首次与页面交互（如点击按钮）到浏览器响应该交互的时间。使用 `PerformanceObserver` 监听 `first-input` 类型的性能条目。

- **关键步骤**:
  1. 使用 `PerformanceObserver` 监听 `first-input` 条目。
  2. 计算从用户交互到浏览器处理该交互的时间差。
  3. 获取交互事件的详细信息（如目标元素、事件类型）。
  4. 将计算结果和相关信息上报。

### Time to Interactive (TTI)

TTI 衡量页面变得完全可交互的时间。通过监听 `paint`、`longtask` 和 `resource` 类型的性能条目来确定页面的静默窗口期。

- **关键步骤**:
  1. 监听 `first-contentful-paint` 条目以获取 FCP 时间。
  2. 监听 `DOMContentLoaded` 事件结束时间。
  3. 监听 `longtask` 条目以捕获长任务。
  4. 监听 `resource` 条目以捕获网络请求。
  5. 计算从 FCP 之后的长任务和网络请求的静默窗口期，确定 TTI 候选时间。
  6. 如果 TTI 候选时间小于 `DOMContentLoaded` 事件结束时间，则以 `DOMContentLoaded` 事件结束时间为准。
  7. 将计算结果和相关信息上报。

### Single Page Application Load (SPA_LOAD)

SPA_LOAD 专门用于监控单页应用的加载性能，通过综合监控多种资源类型来判断页面的完整加载状态。

- **关键步骤**:
  1. **初始化监控**：创建 API、DOM 和路由观察器，设置页面开始时间
  2. **资源监控**：
     - **API 请求**：拦截 fetch/XMLHttpRequest，记录请求的开始和结束时间
     - **图片资源**：监控 `<img>` 元素的 load/error 事件，只监控可见区域内的图片
     - **脚本资源**：监控 `<script>` 元素加载，特殊处理 ES 模块兼容性
     - **背景图片**：解析 CSS 背景图片 URL，检查样式表规则
     - **Bridge 通信**：监听原生应用与 WebView 之间的通信事件
  3. **稳定性检查**：定期检查所有待处理资源是否完成加载
  4. **结果上报**：根据三种触发条件上报结果：
     - **stable**：所有监控的资源都已完成加载
     - **timeout**：达到最大等待时间（loadTimeout）
     - **router**：检测到路由变化，立即上报当前结果

#### 监控的资源类型

1. **API 请求**（type: 'api'）：通过拦截器监控 fetch 和 XMLHttpRequest
2. **图片资源**（type: 'image'）：监控 `<img>` 元素，仅限可见区域
3. **脚本资源**（type: 'resource'）：监控 `<script>` 元素的加载状态
4. **背景图片**（type: 'background'）：解析和监控 CSS 背景图片
5. **Bridge 请求**（type: 'bridge'）：原生应用与 WebView 的通信

#### 三种结束条件

- **stable（稳定）**：所有待处理资源完成加载，页面达到稳定状态
- **timeout（超时）**：超过 `loadTimeout` 设置的最大等待时间
- **router（路由）**：检测到 SPA 路由变化，强制结束当前监控周期

## 技术实现分析

### 核心实现机制

SPA_LOAD 的实现基于多个观察器的协同工作：

1. **API 观察器**：使用 AOP（面向切面编程）技术拦截原生 fetch 和 XMLHttpRequest API
2. **DOM 观察器**：使用 MutationObserver 监控 DOM 树的变化，捕获新增的资源元素
3. **路由观察器**：监听 history API 和 hashchange 事件，处理 SPA 路由变化
4. **性能 API 集成**：利用 Performance API 获取资源加载的详细信息

### 资源状态管理

系统维护四个核心的待处理资源列表：

- `pendingRequests`：待完成的 API 请求
- `pendingImages`：待加载的图片元素
- `pendingScriptDomList`：待加载的脚本元素
- `pendingBgDomList`：待加载的背景图片

### 稳定性判断算法

页面稳定性通过以下逻辑判断：

1. 检查是否超过最大等待时间（loadTimeout）
2. 验证所有待处理资源列表是否为空
3. 特殊处理背景图片的样式表检查
4. 在稳定间隔（stableTimeout）内重复检查

### 已知问题和限制

#### 性能影响

- **频繁的 DOM 操作**：每次 DOM 变化都会触发复杂的处理逻辑
- **样式计算开销**：背景图片监控需要频繁调用 `getComputedStyle`
- **内存占用**：大量的事件监听器和 DOM 引用可能导致内存泄漏

#### 技术限制

- **浏览器兼容性**：依赖现代浏览器 API（MutationObserver、PerformanceObserver）
- **竞态条件**：多个异步操作可能导致状态不一致
- **边界情况**：快速路由切换、网络错误等场景的处理不够完善

#### 监控准确性

- **可见性检查**：只监控可见区域的图片，可能遗漏懒加载资源
- **背景图片检测**：CSS 规则解析可能存在误判
- **动态资源**：运行时动态加载的资源可能无法准确捕获

### 使用建议

1. **生产环境谨慎使用**：监控本身会对页面性能产生影响
2. **合理配置参数**：根据应用特点调整超时时间和检查间隔
3. **忽略无关资源**：通过 ignoreUrls 过滤对用户体验影响较小的资源
4. **监控资源消耗**：定期检查内存使用情况，防止内存泄漏

## 注意事项

> [!IMPORTANT]
> 在不支持 `PerformanceObserver` API或者部分支持的浏览器环境中，可能不会上报数据或者上报默认值（`value` 为 `-1`）。

## 类型定义

```typescript
interface Metric {
  name: 'FCP' | 'CLS' | 'FID' | 'LCP' | 'TTFB' | 'TTI' | 'SPA_LOAD'
  value: number
  navigationType?: 'navigate' | 'reload' | 'prerender' | 'restore'
}

type LoadState = 'loading' | 'dom-interactive' | 'dom-content-loaded' | 'complete'

type DOMHighResTimeStamp = number

interface PerformanceEntry {
  duration: DOMHighResTimeStamp
  entryType: 'paint' | 'resource' | 'navigation'
  name: 'paint' | 'resource' | 'navigation'
  startTime: DOMHighResTimeStamp
}

interface DOMRectReadOnly {
  x: number
  y: number
  top: number
  left: number
  width: number
  height: number
}

interface LayoutShiftAttribution {
  node?: string
  previousRect: DOMRectReadOnly
  currentRect: DOMRectReadOnly
}

interface PerformanceEventTiming extends PerformanceEntry {
  duration: DOMHighResTimeStamp
  interactionId: number
}

interface PerformanceResourceTiming extends PerformanceEntry {
  connectEnd: DOMHighResTimeStamp
  connectStart: DOMHighResTimeStamp
  decodedBodySize: number
  domainLookupEnd: DOMHighResTimeStamp
  domainLookupStart: DOMHighResTimeStamp
  encodedBodySize: number
  fetchStart: DOMHighResTimeStamp
  initiatorType: string
  nextHopProtocol: string
  redirectEnd: DOMHighResTimeStamp
  redirectStart: DOMHighResTimeStamp
  requestStart: DOMHighResTimeStamp
  responseEnd: DOMHighResTimeStamp
  responseStart: DOMHighResTimeStamp
  secureConnectionStart: DOMHighResTimeStamp
  serverTiming: ReadonlyArray<PerformanceServerTiming>
  transferSize: number
  workerStart: DOMHighResTimeStamp
}

interface CLSMetric extends Metric {
  name: 'CLS'
  largestShiftTarget?: string
  largestShiftTime?: DOMHighResTimeStamp
  largestShiftValue?: number
  largestShiftSource?: LayoutShiftAttribution
  loadState?: LoadState
}

interface LayoutShift extends PerformanceEntry {
  value: number
  sources: LayoutShiftAttribution[]
  hadRecentInput: boolean
}

interface PerformanceNavigationTiming {
  activationStart?: number
}

interface FCPMetric extends Metric {
  name: 'FCP'
  timeToFirstByte: number
  firstByteToFCP: number
  loadState: LoadState
}

interface LCPMetric extends Metric {
  name: 'LCP'
  element?: string
  url?: string
  timeToFirstByte: number
  resourceLoadDelay: number
  resourceLoadDuration: number
  elementRenderDelay: number
}

interface FIDMetric extends Metric {
  name: 'FID'
  eventTarget: string
  eventTime: number
  eventType: string
  loadState: LoadState
}

interface TTFBMetric extends Metric {
  name: 'TTFB'
  waitingDuration: number
  cacheDuration: number
  dnsDuration: number
  connectionDuration: number
  requestDuration: number
}

interface TTIMetric {
  name: 'TTI'
  longtasks?: {
    start: number
    end: number
    attribution: TaskAttributionTiming[]
  }[]
}

interface TaskAttributionTiming extends PerformanceEntry {
  containerId: string
  containerName: string
  containerSrc: string
  containerType: 'iframe' | 'embed' | 'object'
}

interface SPALoadMetric extends Metric {
  name: 'SPA_LOAD'
  startTime: number
  records: Record[]
  source: 'timeout' | 'stable' | 'router'
  startLoadTime?: number
  initTime?: number
  onStartedTime?: number
  isLowPowerMode?: boolean
}

interface Record {
  url: string
  duration: number
  startTime?: number
  type: 'api' | 'image' | 'background' | 'bridge' | 'resource'
  requestId?: string
  endTime?: number
}
```
