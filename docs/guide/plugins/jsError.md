# JSError 插件

JSError 插件主要用于收集页面中的 JavaScript 错误信息，同时也提供了手动报告 JavaScript 错误的方法。

### 使用

使用 JSError 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.jsError()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { jsError } from '@aegis/sdk/integrations/jsError'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [jsError()]
})
```

:::

#### 配置选项

- `vueCheck`：是否检查 Vue 框架，默认为 `true`
- `dedupe`：是否对错误信息进行去重，默认为 `true`
- `logErrorsInConsole`：是否在控制台输出错误信息，默认为 `true`
- `ignoreErrors`：忽略的错误信息列表，可以是字符串或正则表达式

### 手动报告错误

在某些情况下，业务逻辑可能需要手动报告 JavaScript 错误。JSError 插件提供了 `captureException` 方法来实现这一功能。

该方法接收两个参数：

- `exception`: 类型为 `Exception`，表示要报告的错误信息
- `timestamp`: 表示错误发生的时间戳

```javascript
aegis.captureException(new Error('Custom error message'), Date.now())
```

### 数据格式

JSError 插件上报的数据格式如下：

```json
{
  "type": "js_error",
  "payload": {
    "type": "Error",
    "value": "Custom error message",
    "timestamp": 1626780000000,
    "stacktrace": [
      {
        "function": "function name",
        "filename": "file path",
        "lineno": 1,
        "colno": 1
      }
    ],
    "cate": "error"
  }
}
```

#### 字段说明

- `type`：[错误类型](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#error_types)
- `value`：错误信息
- `timestamp`：错误发生的时间戳
- `stacktrace`：错误堆栈信息
  - `function`：函数名
  - `filename`：文件路径
  - `lineno`：行号
  - `colno`：列号
- `cate`：错误分类，值包括 `error` 和 `promise`

### 实现原理

JSError 插件的实现原理主要包括以下几个部分：

- 在页面初始化时，插件会添加全局的 `error` 事件监听器，捕获 JavaScript 错误信息。

```typescript
function __attachJSErrorListener() {
  const handler = function (this: JSErrorMonitor, event: ErrorEvent) {
    if (isJsError(event)) {
      this.__sendError(normalizeException(event))
    }
  }.bind(this)

  attachEvent('error', handler, true)
}
```

- 在页面初始化时，插件会添加全局的 `unhandledrejection` 事件监听器，捕获 Promise 错误信息。

```typescript
function __attachPromiseRejectionErrorListener() {
  const handler = function (this: JSErrorMonitor, event: PromiseRejectionEvent) {
    this.__sendError(normalizeException(event))
  }.bind(this)

  attachEvent('unhandledrejection', handler, true)
}
```

- 错误数据构建

插件会将捕获到的错误根据错误信息进行格式化，抹平不同浏览器内核的差异，解析出错误类型 `type` 、错误信息 `value` 、错误堆栈树 `stacktrace` 等关键信息，最终构建出统一的错误数据。

```typescript
function normalizeException(exception: Exception, timestamp?: number): JSErrorReport['payload'] {
  timestamp = timestamp || now()

  if (isErrorEvent(exception)) {
    return normalizeErrorEvent(exception, timestamp)
  } else if (isPromiseRejectionEvent(exception)) {
    return normalizeRejectionError(exception, timestamp)
  } else if (isError(exception)) {
    return normalizeError(exception, timestamp)
  } else {
    return {
      cate: 'error',
      type: 'Error',
      timestamp: timestamp,
      value: isString(exception) ? exception : json.stringify(exception)
    }
  }
}
```

### 注意事项

> [!IMPORTANT]
> 由于浏览器的安全策略，跨域的 JavaScript 错误无法被捕获。对应获取到的错误信息是 `Script error`，无法获取到详细信息。

> [!CAUTION]
> 如果应用使用了 Vue 框架，但是没有配置 [`errorHandler`](https://vuejs.org/api/application.html#app-config-errorhandler) 方法，那么插件将无法捕获 Vue 错误。插件会在控制台输出相应的警告信息。当把 `vueCheck` 设置为 `false` 时，不会执行 `vue` 框架相关的检测，那么就不会输出该警告信息。

获取跨域脚本报错有两种方案：

1. 开启 CORS（Cross Origin Resource Sharing）

- 为 script 标签添加 `crossorigin` 属性

```html
<script src="https://foo.com/bar.js" crossorigin="anonymous"></script>
```

此步骤的作用是告知浏览器以匿名方式获取目标脚本。这意味着请求脚本时不会向服务端发送潜在的用户身份信息（例如Cookies、HTTP证书等）。

- 同时服务端设置 `Access-Control-Allow-Origin` 头

```shell
Access-Control-Allow-Origin: *

# 或者指定域名

Access-Control-Allow-Origin: https://app.com

```

2. 使用 `try-catch` 包裹报错脚本后手动抛出错误

```html
<script src="https://foo.com/bar.js"></script>
<script>
  try {
    bar()
  } catch (e) {
    throw e // 手动抛出错误
  }
</script>
```

### 类型定义

```typescript
interface JsErrorOptions {
  dedupe?: boolean
  vueCheck?: boolean
  logErrorsInConsole?: boolean
  ignoreErrors?: (string | RegExp)[]
}

type Exception = Error | ErrorEvent | PromiseRejectionEvent | CustomEvent<{ reason: unknown }>

interface JSErrorReport {
  type: 'js_error'
  payload: {
    type: string
    value: string
    stack?: string
    timestamp: number
    stacktrace?: StackFrame[]
    cate: 'error' | 'promise'
  }
}

interface StackFrame {
  colno?: number
  lineno?: number
  filename?: string
  function?: string
}
```
