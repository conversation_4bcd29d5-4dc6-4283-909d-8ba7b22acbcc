# Precollect 插件

Precollect 插件主要用于配合 CDN 异步加载脚本的场景。所以非 CDN 异步加载脚本的情况下，不建议使用该插件。

目前预收集信息只包括**代码错误信息**和**资源加载错误信息**，所以使用该插件请确保也同样使用了 [JS Error](./jsError.md) 和 [Resource Error](./resourceError.md) 插件。

### 使用

使用 Precollect 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

```html
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.precollect()]
  })
</script>
```

### 实现原理

Precollect 插件的实现原理主要包括两个部分：

- 监听全局错误事件，当发生错误时，调用临时对象的 `precollect` 方法进行预收集
- 当 Aegis SDK 加载完成后并初始化完成后，调用真实对象的 `precollect` 方法进行数据上报，并取消对全局错误事件的监听

::: code-group

```ts [loader.ts]
// 预先收集错误信息，此时 win[globalName] 是一个临时对象

function handleError(event) {
  event = event || win.event
  const target = event.target || event.srcElement

  if (target instanceof Element) {
    win[globalName][precollect]('sr', target, now())
  } else {
    win[globalName][precollect]('js', event, now())
  }
}

function handleRejectError(event) {
  event = event || win.event
  win[globalName][precollect]('js', event, now())
}
```

```ts [precollect.ts]
// 注册 precollect 方法，并调用相应的错误收集方法进行数据上报

aegis.provide('precollect', function (type: 'sr' | 'js', data: EventTarget | null | Exception, timestamp?: number) {
  const methodName = typeMap[type]

  if (registrys.indexOf(methodName) !== -1 || aegis[methodName]) {
    if (methodName === 'reportResourceError') {
      aegis[methodName](<EventTarget | null>data, timestamp || now())
    } else {
      aegis[methodName](<Exception>data, timestamp || now())
    }
  }
})

// 这行代码确保使用了 JS Error 和 Resource Error 插件且已经被加载
aegis.on('provide', function (name: string) {
  registrys.push(name)
})
```

:::
