# Feature 插件

Feature 插件主要用于浏览器特性检测功能，开发者通过自定义配置检测项来评估浏览器对不同功能的支持程度。该插件当前支持检测 API、CSS 属性以及各种图片格式等多种浏览器特性。

### 使用

使用 Feature 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.feature()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { feature } from '@aegis/sdk/integrations/feature'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [feature()]
})
```

:::

### 配置选项

- `remoteUrl`：远程配置文件，支持动态的加载检测项进行特性检测。默认为空
- `features`：本地检测项配置列表，用于设置需要检测的浏览器特性，可与远程配置共同生效。默认为空数组
- `immediate`：检测时机，为 `true` 时立即执行检测；为 `false` 时会在浏览器空闲时执行检测。默认为 `false`

### 配置检测特性

开发者可以通过 `features` 配置项添加自定义特性检测：

> [!TIP]
> 远程配置文件（`remoteUrl`）的数据结构与 `features` 相同。

```js
const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [
    feature({
      features: [
        {
          type: 'API',
          name: 'URLSearchParams',
          strategy: 'ctor',
          params: {
            class: 'URLSearchParams',
            args: ['https://example.com']
          }
        },
        {
          type: 'CSS',
          name: 'flexbox',
          strategy: 'property',
          params: { property: 'display', value: 'flex' }
        },
        {
          type: 'IMAGE',
          name: 'webp',
          async: true,
          strategy: 'format',
          params: { base64: 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==' }
        }
      ]
    })
  ]
})
```

### 数据格式

Feature 插件上报的数据格式如下：

```json
{
  "type": "feature",
  "payload": {
    "features": [
      {
        "name": "promise",
        "type": "API",
        "supported": true
      },
      {
        "name": "fetch",
        "type": "API",
        "supported": true
      },
      {
        "name": "grid",
        "type": "CSS",
        "supported": true
      },
      {
        "name": "webp",
        "type": "IMAGE",
        "supported": true
      }
    ]
  }
}
```

#### 字段说明

- `features`：特性检测结果列表
  - `name`：特性名称
  - `type`：特性类型，包括 `API`、`CSS` 和 `IMAGE`
  - `supported`：是否支持该特性

### 内置特性检测

Feature 插件内置了以下特性检测：

- #### API 特性

  - `promise`：异步编程支持
  - `fetch`：网络请求
  - `performance`：性能监控
  - `requestAnimationFrame`：动画帧请求
  - `MutationObserver`：DOM 变动监听
  - `Blob`：二进制数据操作
  - `IntersectionObserver`：元素可见性监听
  - `ResizeObserver`：元素尺寸变化监听
  - `serviceWorker`：离线缓存服务
  - `cryptoSubtleDigest`：加密哈希计算
  - `WebAssembly`：WebAssembly 支持
  - `URL`：URL 解析与构造
  - `URLSearchParams`：URL 参数处理
  - `arrowFunction`：箭头函数语法支持

- #### CSS 特性

  - `grid`：CSS Grid 布局

- #### 图片格式特性
  - `webp`：WebP 图片格式

### 实现原理

Feature 插件的核心实现包括以下几个部分：

- 检测策略

插件实现了四种特性检测策略，分别用于检测不同类型的浏览器特性：

::: code-group

```typescript [API Property]
/**
 * 属性检测
 *
 * 用于检测全局对象（如 window）上是否存在特定属性
 * 支持通过 `context` 参数检测深层对象的属性（如 `navigator.serviceWorker`）
 *
 * 示例：
 * {
 *   type: 'API',
 *   name: 'serviceWorker',
 *   strategy: 'property',
 *   params: {
 *     context: 'navigator',
 *     property: 'serviceWorker'
 *   }
 * }
 */
function property(params: IFeature['params']): boolean {
  if (!params.property) {
    return false
  }

  try {
    let context = window

    if (params.context) {
      let currentContext: unknown = window
      const parts = params.context.split('.')

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i]

        if (currentContext && typeof currentContext === 'object') {
          currentContext = (currentContext as Record<string, unknown>)[part]
        } else {
          currentContext = undefined
        }
      }

      context = currentContext as typeof window
    }

    return params.property in context && context[params.property as keyof typeof context] !== undefined
  } catch (error) {
    return false
  }
}
```

```typescript [API Class]
/**
 * 构造函数检测
 *
 * 检测是否可以正确实例化某个构造函数
 * 支持传入构造函数参数
 *
 * 示例：
 * {
 *   type: 'API',
 *   name: 'URLSearchParams',
 *   strategy: 'ctor',
 *   params: { class: 'URLSearchParams', args: ['https://example.com'] }
 * }
 */
function ctor(params: IFeature['params']): boolean {
  if (!params.class) {
    return false
  }

  try {
    const args = [null].concat(params.args || []) as [anyType, ...anyType[]]
    const Ctor = window[params.class as keyof Window] as new (...args: anyType[]) => object
    const BoundCtor = Function.prototype.bind.apply(Ctor, args) as unknown as new () => object

    const instance = new BoundCtor()
    return instance !== undefined && (typeof instance === 'object' || typeof instance === 'function')
  } catch (error) {
    return false
  }
}
```

```js [CSS]
/**
 * CSS 属性检测
 *
 * 用于检测 CSS 属性是否支持特定值
 *
 * 示例：
 * {
 *   type: 'CSS',
 *   name: 'flexbox',
 *   strategy: 'property',
 *   params: { property: 'display', value: 'flex' }
 * }
 */
function property(params: IFeature['params']): boolean {
  if (!params.property || !params.value) {
    return false
  }

  return CSS.supports(params.property, params.value)
}
```

```js [IMAGE]
/**
 * 格式检测
 *
 * 用于检测图片格式支持
 *
 * 示例：
 * {
 *   type: 'IMAGE',
 *   name: 'webp',
 *   strategy: 'format',
 *   params: { base64: 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==' }
 * }
 */
function format(params: IFeature['params'], callback: (supported: boolean) => void) {
  if (!params.base64) {
    callback(false)
    return
  }

  const img = new Image()

  img.onload = () => callback(true)
  img.onerror = () => callback(false)
  img.src = params.base64
}
```

:::

- 检测流程

插件的检测流程分为以下步骤：

- 合并检测项

  - 将内置检测项（`builtinFeatures`）和自定义检测项（`options.features`）合并
  - 如果配置了 `remoteUrl`，还会获取远程检测项并合并

- 执行检测

  - 同步检测：直接执行并获取结果
  - 异步检测：通过回调方式获取结果
  - 使用 `requestIdleCallback` 在浏览器空闲时执行检测，避免影响页面性能

  ```typescript
  if (immediate) {
    runDetection(featureList, reportFeatures)
  } else {
    idleCallbackId = requestIdleCallback(() => {
      runDetection(featureList, reportFeatures)
    })
  }
  ```

### 注意事项

> [!TIP]
> Feature 插件检测任务会在 `requestIdleCallback` 中执行，以避免阻塞主线程。该 `requestIdleCallback` 已默认进行过 Polyfill 处理。如果需要立即执行，可以将 `immediate` 选项设置为 `true`。

### 类型定义

```typescript
interface IFeature {
  name: string
  skip?: boolean
  async?: boolean
  type: 'API' | 'CSS' | 'IMAGE'
  strategy: 'property' | 'ctor' | 'format'
  params: {
    class?: string
    value?: string
    args?: any[]
    context?: string
    property?: string
    base64?: string
  }
}

interface IFeatureResult {
  name: string
  type: 'API' | 'CSS' | 'IMAGE'
  supported: boolean
}

type FeatureReport = {
  type: 'feature'
  payload: {
    features: IFeatureResult[]
  }
}

interface FeatureOptions extends Record<string, unknown> {
  remoteUrl?: string
  immediate?: boolean
  features?: IFeature[]
}
```
