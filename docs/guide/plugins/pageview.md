# PageView 插件

PageView 插件主要用于统计页面的访问量（PV）和独立访客数（UV）。它能够自动捕获页面浏览事件，并在 URL 发生变化时上报相关数据。

### 使用

使用 PageView 插件，只需在初始化 Aegis 实例时将其添加到 `integrations` 数组即可。以下是配置示例：

::: code-group

```html [CDN]
<script src="{CDN_URL}"></script>

<script>
  const aegis = new Aegis({
    id: '{APP_ID}',
    integrations: [Aegis.pageview()]
  })
</script>
```

```js [NPM]
import Aegis from '@aegis/sdk'
import { pageview } from '@aegis/sdk/integrations/pageview'

const aegis = new Aegis({
  id: '{APP_ID}',
  integrations: [pageview()]
})
```

:::

#### 配置选项

- `vueCheck`：是否检查 Vue 框架，默认为 `true`
- `syncCid`：是否将 `cid` 与 `pid` 同步，默认为 `false`
- `initSend`：是否在初始化时自动发送 PV 数据，默认为 `true`
- `mode`：页面路由模式，可选 `history` 或 `hash`，默认为 `history`
- `extractPid`：自定义 `pid` 提取函数，接收完整 URL 作为参数，返回提取的 `pid`，默认为内置的 `pid` 提取逻辑

#### 手动发送 PV 数据

在某些情况下，业务逻辑可能需要手动发送 PV 数据。PageView 插件提供了 `sendPV` 方法来实现这一功能。

该方法接收一个字符串参数，表示要发送的页面路径。例如：

```javascript
aegis.sendPV('/custom-page-path')
```

### 数据格式

PageView 插件上报的数据格式如下：

```json
{
  "type": "pv",
  "payload": {
    "pid": "/page-path",
    "source": "auto"
  }
}
```

#### 字段说明

- `pid`：页面的唯一标识符
  - history 模式：URL 的 `pathname`
  - hash 模式：URL 的 `hash` 井号(#)到问号(?)之间的内容
- `source`：页面变化的来源，可能的值包括：
  - auto：初始页面加载
  - hash：URL 的 `hash` 部分发生变化
  - manually：通过 `sendPV` 方法手动触发
  - history：通过浏览器的前进/后退按钮改变 URL
  - pushState：通过 `history.pushState` 方法改变 URL
  - replaceState：通过 `history.replaceState` 方法改变 URL

### 实现原理

PageView 插件的核心实现包括以下几个部分：

- 路由模式适配

插件根据配置的模式（`history` 或 `hash`）采用不同的方式监听 URL 变化。

```ts
if (mode === 'hash') {
  attachEvent('hashchange', boundHashChangeListener, true)
} else {
  attachEvent('popstate', boundHistoryChangeListener, true)
}
```

- 劫持 `history` 方法

为了捕获通过编程方式改变 URL 的情况，插件劫持了 `history.pushState` 和 `history.replaceState` 方法。

```ts
aop(history, 'pushState', function (original, args) {
  original.apply(history, args)
  historyChangeListener(aegis, 'pushState')
})

aop(history, 'replaceState', function (original, args) {
  original.apply(history, args)
  historyChangeListener(aegis, 'replaceState')
})
```

### 注意事项

> [!IMPORTANT]
> 自定义 `extractPid` 函数时，请确保处理了所有可能的 URL 格式。如果函数返回 `falsy` 值，插件将回退到默认的提取逻辑。

> [!CAUTION]
> 如果应用使用了 Vue + Vue-Router 并设置了 `scrollBehavior` 选项，在 `initSend: true` 模式下可能会导致重复发送 PV 数据。插件会在控制台输出相应的警告信息。当把 `vueCheck` 设置为 `false` 时，不会执行 `vue` 框架相关的检测，那么就不会输出该警告信息。

> [!TIP]
> 当 `syncCid` 设置为 `true` 时，`cid` 将随每次页面变化而更新。为了保证其他插件能够获取到最新的 `cid`，请确保在所有插件之前初始化 PageView 插件。

### 类型定义

```typescript
type Source = 'auto' | 'manually' | 'pushState' | 'replaceState' | 'hash' | 'history'

type PageviewReport = {
  type: 'pv'
  payload: {
    pid: string
    source: Source
  }
}

interface PageViewOptions {
  syncCid?: boolean
  vueCheck?: boolean
  initSend?: boolean
  mode?: 'history' | 'hash'
  extractPid?: (url: string) => string
}
```
