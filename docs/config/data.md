# 数据结构

Aegis SDK 使用统一的数据格式进行日志采集和上报。下面将详细说明 SDK 使用的数据结构及其字段含义。

## 插件数据结构

插件在采集数据时使用以下格式：

```typescript
interface PluginReportData {
  type: string
  immediate?: boolean
  payload: Record<string, any>
}
```

#### 字段说明

- `type`: 字符串，表示数据类型（如 `error, `performance, `pv` 等）
- `immediate`: 布尔值，表示是否立即上报
- `payload`: 包含具体数据的对象，其结构根据 `type` 而变化

## 通用数据结构

无论在何种环境下（浏览器、Node.js、小程序或 React Native），Aegis SDK 都使用以下通用数据结构：

```typescript
interface SendType {
  common: {
    id: string
    uid: string
    timestamp: number
    sdk_name: string
    sdk_version: string
    [key: string]: string | number | undefined
  }
  type: string
  immediate?: boolean
  payload: any
}
```

#### 字段说明

- `common`: 通用信息的对象
  - `timestamp`: 时间戳
  - `sdk_name`: SDK 的名称
  - `sdk_version`: SDK 的版本号
  - `id`: 项目 ID
  - `uid`: 用户 ID，由 SDK 配置或自动生成
- `type`: 与插件上报的 `type` 相同，表示数据类型
- `immediate`: 与插件上报的 `immediate` 相同，表示是否立即上报
- `payload`: 与插件上报的 `payload` 相同，包含具体的数据内容

## 环境特定数据结构

#### 浏览器环境

在浏览器环境中，`common` 对象还包含以下额外字段：

```typescript
interface BrowserCommon extends Common {
  url: string // 页面 URL
  sid: string // 会话 ID
  network: string // 网络类型
  cid: string // 页面 ID
  env?: string // 环境标识
  sample?: number // 采样率
  did?: string // 设备 ID
  release?: string // 应用版本号
}
```

#### Node.js 环境

待开发后补充

#### 小程序环境

待开发后补充

#### React Native 环境

待开发后补充

## 示例

```json
{
  "common": {
    "sample": 1,
    "env": "prod",
    "network": "4g",
    "uid": "user-123",
    "release": "v2.1.0",
    "sdk_name": "Aegis",
    "sdk_version": "1.0.0",
    "id": "your-project-id",
    "did": "browser-device-id",
    "timestamp": 1623456789000,
    "cid": "https://example.com/page#main",
    "url": "https://example.com/page#main",
    "sid": "550e8400-e29b-41d4-a716-************"
  },
  "type": "pv",
  "payload": {
    "pid": "page-123",
    "source": "auto"
  }
}
```

### 注意事项

> [!TIP] > `common` 对象中的字段可能因配置和运行环境而异，不是所有字段都会在每次上报中出现。

> [!TIP]
> 开发者可以通过自定义 `builder` 函数来修改或扩展数据结构，但应保持基本格式不变。
