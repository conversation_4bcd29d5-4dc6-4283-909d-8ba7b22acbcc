# SDK 配置

以下是 Aegis SDK 所有可用的配置选项及其详细说明。

## 参数项

### `id` <sup>`string`</sup>

监控应用的唯一标识符，也称之为**项目ID**，由后端生成，**必填项**。

> [!TIP] > `id` 是由后端生成的一串特殊格式的字符串，该字符串的前两位标识了项目类型。

| 标识 | 项目类型 | 标识 |   项目类型   |
| ---- | :------: | ---- | :----------: |
| `00` |   Web    | `01` |   Node.js    |
| `02` | Mini App | `03` | React Native |

### `uid` <sup>`string`</sup>

用户 ID，用于标识访问用户，如不设置，SDK 会自动使用 `uuid` v4算法生成并存储在本地。

### `did` <sup>`string`</sup>

设备 ID，用于标识具体设备。

### `cid` <sup>`string`</sup>

通用 ID，用于关联各类日志数据。在浏览器环境中，默认为页面 URL。当使用 PageView 插件并启用 `syncCid` 选项时，`cid` 会随页面 URL 变化而更新。

### `release` <sup>`string`</sup>

应用版本号，用于区分不同版本的应用。

### `sample` <sup>`number`</sup>

日志采样率配置，用于控制上报数据的比例。可选值为 0 到 1 之间的小数。

### `env` <sup>`string`</sup>

环境标识，用于区分不同的运行环境。可选值有 `'prod' | 'gray' | 'pre' | 'test'`。

- `prod`: 线上环境
- `gray`: 灰度环境
- `pre`: 预发环境
- `test`: 测试环境

### `integrations` <sup>`Integration[]`</sup>

采集项插件配置数组。

### `transports` <sup>`AbstractTransport[]`</sup>

数据上报方式，可定义多个传输通道。

### `SDKReportUrl` <sup>`string`</sup>

SDK 自身错误上报地址，用于收集 SDK 内部发生的错误。接口地址必须支持 `POST` 请求。

### `builder`

上报数据处理函数，用于在上报前对采集到的数据进行处理。接收两个参数：

- `data`: 采集到的数据
- `configManager`: 配置管理器，用于获取当前配置

如果返回值为 `Falsy`，则不上报数据。

> [!TIP]
> 如果没有自定义 `builder` 函数，那么将根据项目类型使用内置的 `builder` 函数。

## 示例

```javascript
import Aegis from '@aegis/sdk'

const aegis = new Aegis({
  id: 'your-project-id',
  uid: 'user-123',
  release: 'v1.0.0',
  env: 'prod',
  sample: 0.5,
  integrations: [
    /* 自定义插件 */
  ],
  builder: (data, configManager) => {
    // 自定义数据处理逻辑
    return data
  },
  SDKReportUrl: 'https://your-sdk-error-report-url.com'
})
```

## 配置更新

SDK 支持动态更新配置，通过 `setConfig` 方法传入新的配置对象即可更新配置。

```javascript
aegis.setConfig({
  uid: 'new-user-456',
  env: 'test'
})
```

> [!TIP] > `id` `integrations` `SDKReportUrl` `transports` 字段不能通过 `setConfig` 方法更新。
