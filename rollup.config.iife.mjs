import { hash } from 'hasha'
import Table from 'cli-table3'
import { minify } from 'terser'
import { filesize } from 'filesize'
import { defineConfig } from 'rollup'
import json from '@rollup/plugin-json'
import clean from 'rollup-plugin-delete'
import babel from '@rollup/plugin-babel'
import { fileURLToPath } from 'node:url'
import { gzipSizeSync } from 'gzip-size'
import replace from '@rollup/plugin-replace'
import commonjs from '@rollup/plugin-commonjs'
import { DEFAULT_EXTENSIONS } from '@babel/core'
import typescript from 'rollup-plugin-typescript2'
import externals from 'rollup-plugin-node-externals'
import { join, dirname, basename, extname, sep } from 'node:path'
import { nodeResolve, DEFAULTS } from '@rollup/plugin-node-resolve'
import { readdirSync, statSync, unlinkSync, readFileSync, renameSync, writeFileSync } from 'node:fs'

export const OUTPUT_DIR = 'dist/iife'

const shouldCompress = process.env.COMPRESS === 'true'
const _dirname = dirname(fileURLToPath(import.meta.url))
const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'))
const toAbsolutePath = (relativePath) => join(_dirname, relativePath)

/**
 * 该Rollup配置主要进行以下操作：
 *
 * 1: 将 `src/index.ts` 文件作为入口文件，把各模块进行 CommonJS 格式的打包
 * 1.1: 在 `manualChunks` 钩子中进行配置，主要是按模块的功能进行区分，对不同的模块进行合并打包
 *
 * 2: 把所有的 CommonJS 模块转换为 IIFE 格式
 * 2.1: 移除 ES 模块的 `__esModule` 标记
 * 2.2: 简化 Aegis 命名空间
 * 2.3: 移除 CommonJS 模块中的 `exports` 相关代码
 * 2.4: 为模块添加注释，方便后续代码进行拆分
 *
 * 3: 对代码进行合并压缩，详情见 `comboCompress` 函数注释
 */
export default defineConfig([
  {
    input: 'src/index.ts',
    output: {
      format: 'cjs',
      dir: OUTPUT_DIR,
      entryFileNames: 'aegis.js',
      chunkFileNames: '[name].js',
      manualChunks(chunk) {
        // 获取源码路径 Eg: {dirname}/src/
        const sourceCodeDirectory = join(_dirname, 'src', sep)
        // 获取源码文件相对路径并剔除文件后缀名 Eg: integrations/browser/pageview
        const sourceCodeFilePath = chunk
          .replace(sourceCodeDirectory, '')
          .replace(extname(chunk), '')
          // Remove tslib.js null bytes
          .replace(/\u0000/g, '')

        // ['pageview', 'precollect', 'blankScreen', 'action', 'bridge', 'resource', 'resourceError', 'device', 'largePictureInspect', 'feature'] 单独打包
        if (
          [
            'integrations/browser/pageview',
            'integrations/browser/precollect',
            'integrations/browser/blankScreen',
            'integrations/browser/action',
            'integrations/browser/bridge',
            'integrations/browser/resource',
            'integrations/browser/resourceError',
            'integrations/browser/device',
            'integrations/browser/largePictureInspect',
            'integrations/browser/feature'
          ].includes(sourceCodeFilePath)
        ) {
          return `integrations/browser/${basename(chunk, '.ts')}`
        }

        // api 目录下的文件合并打包为一个文件
        if (['integrations/browser/api/index'].includes(sourceCodeFilePath)) {
          return `integrations/browser/api`
        }

        // jsError 目录下的文件合并打包为一个文件
        if (
          ['integrations/browser/jsError/index', 'integrations/browser/jsError/JSErrorMonitor', 'integrations/browser/jsError/utils', 'integrations/browser/jsError/stack-parsers'].includes(
            sourceCodeFilePath
          )
        ) {
          return `integrations/browser/jsError`
        }

        // pixel transport 打包成一个文件
        if (['transports/browser/pixel'].includes(sourceCodeFilePath)) {
          return `transports/browser/${basename(chunk, '.ts')}`
        }

        // console transport 和 http transport 打包成一个文件
        if (['transports/console', 'transports/http'].includes(sourceCodeFilePath)) {
          return `transports/${basename(chunk, '.ts')}`
        }

        // performance 目录下的文件合并打包为一个文件
        if (sourceCodeFilePath.includes('integrations/browser/performance')) {
          return `integrations/browser/perf`
        }

        // utils 目录下的文件和其他文件合并打包为一个文件
        if (
          [
            'tslib',
            'utils/fetch',
            'utils/index',
            'constants',
            'utils/uuid',
            'utils/store',
            'utils/polyfills',
            'utils/resource',
            'integrations/browser/api/utils',
            'utils/browser/htmlTreeStringify',
            'utils/browser/routeObserver',
            'utils/browser/domObserver',
            'utils/browser/apiObserver',
            'transports/transport',
            'utils/is',
            'utils/json',
            'utils/time',
            'utils/browser/xhr',
            'utils/browser/cookie',
            'utils/browser/storage',
            'utils/browser/utils',
            'utils/worldwide'
          ].includes(sourceCodeFilePath)
        ) {
          return `utils`
        }
      }
    },
    plugins: [
      clean({ targets: [OUTPUT_DIR, 'types/*'] }),
      externals({ deps: true }),
      nodeResolve({
        extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
      }),
      commonjs(),
      json(),
      replace({
        preventAssignment: true,
        __SDK_VERSION__: pkg.version,
        'process.env.NODE_ENV': JSON.stringify('production'),
        __SDK_NAME__: `${pkg.name.charAt(0).toUpperCase()}${pkg.name.slice(1)}`
      }),
      typescript({ useTsconfigDeclarationDir: true }),
      babel({
        babelHelpers: 'runtime',
        exclude: '**/node_modules/**',
        extensions: [...DEFAULT_EXTENSIONS, '.ts', '.tsx']
      })
    ]
  },
  ...convertCommonJsToIIFEformat()
])

function convertCommonJsToIIFEformat() {
  const utils = toAbsolutePath(`${OUTPUT_DIR}/utils.js`)
  const aegis = toAbsolutePath(`${OUTPUT_DIR}/aegis.js`)
  const api = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/api.js`)
  const httpTransport = toAbsolutePath(`${OUTPUT_DIR}/transports/http.js`)
  const perf = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/perf.js`)
  const bridge = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/bridge.js`)
  const action = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/action.js`)
  const device = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/device.js`)
  const consoleTransport = toAbsolutePath(`${OUTPUT_DIR}/transports/console.js`)
  const jsError = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/jsError.js`)
  const feature = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/feature.js`)
  const resource = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/resource.js`)
  const pageview = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/pageview.js`)
  const pixelTransport = toAbsolutePath(`${OUTPUT_DIR}/transports/browser/pixel.js`)
  const precollect = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/precollect.js`)
  const blankScreen = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/blankScreen.js`)
  const resourceError = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/resourceError.js`)
  const largePictureInspect = toAbsolutePath(`${OUTPUT_DIR}/integrations/browser/largePictureInspect.js`)

  return [
    /**
     * 把 utils.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis_utils 文件名：utils.js
     */
    {
      input: utils,
      output: {
        format: 'iife',
        exports: 'named',
        dir: OUTPUT_DIR,
        name: 'Aegis_utils',
        entryFileNames: 'utils.js'
      },
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeComments(),
        addCommentsToModules()
      ]
    },
    /**
     * 把 pageview.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.pageview 文件名：pageview.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: pageview,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.pageview',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'pageview.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('pageview'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 precollect.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.precollect 文件名：precollect.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: precollect,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.precollect',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'precollect.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('precollect'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 blankScreen.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.blankScreen 文件名：blankScreen.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: blankScreen,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.blankScreen',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'blankScreen.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('blankScreen'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 action.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.action 文件名：action.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: action,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.action',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'action.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('action'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 device.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.device 文件名：device.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: device,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.device',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'device.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('device'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 api.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.api 文件名：api.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: api,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.api',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'api.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('index'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 bridge.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.bridge 文件名：bridge.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: bridge,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.bridge',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'bridge.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('bridge'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 resource.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.resource 文件名：resource.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: resource,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.resource',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'resource.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('resource'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 resourceError.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.resourceError 文件名：resourceError.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: resourceError,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.resourceError',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'resourceError.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('resourceError'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 jsError.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.jsError 文件名：jsError.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: jsError,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.jsError',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'jsError.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('jsError'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 perf.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.perf 文件名：perf.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: perf,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.perf',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'perf.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('index'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 largePictureInspect.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.largePictureInspect 文件名：largePictureInspect.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: largePictureInspect,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.largePictureInspect',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'largePictureInspect.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('largePictureInspect'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 feature.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.feature 文件名：feature.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: feature,
      output: {
        format: 'iife',
        exports: 'named',
        name: 'Aegis.feature',
        dir: `${OUTPUT_DIR}/integrations/browser`,
        entryFileNames: 'feature.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('feature'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 console.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.transports.Console 文件名：console.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: consoleTransport,
      output: {
        format: 'iife',
        exports: 'named',
        dir: `${OUTPUT_DIR}/transports`,
        name: 'Aegis.transports.Console',
        entryFileNames: 'console.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('ConsoleTransport'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 http.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.transports.Http 文件名：http.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: httpTransport,
      output: {
        format: 'iife',
        exports: 'named',
        dir: `${OUTPUT_DIR}/transports`,
        name: 'Aegis.transports.Http',
        entryFileNames: 'http.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('HttpTransport'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 pixel.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis.transports.Pixel 文件名：pixel.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     */
    {
      input: pixelTransport,
      output: {
        format: 'iife',
        exports: 'named',
        dir: `${OUTPUT_DIR}/transports/browser`,
        name: 'Aegis.transports.Pixel',
        entryFileNames: 'pixel.js',
        globals: {
          [utils]: 'Aegis_utils'
        }
      },
      external: [utils],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeAegisNamespace(),
        removeExports('PixelTransport'),
        addCommentsToModules()
      ]
    },
    /**
     * 把 aegis.js 从 CommonJS 格式转换为 IIFE 格式
     *
     * 命名空间：Aegis 文件名：aegis.js
     *
     * 对 utils 的引用使用全局变量 Aegis_utils
     *
     * 对 pageview, precollect, httpTransport, pixelTransport, consoleTransport, blankScreen, action, bridge, api, resource, resourceError, jsError, device, perf, largePictureInspect, feature 的引用使用空对象也就是不引用
     *
     */
    {
      input: aegis,
      output: {
        name: 'Aegis',
        format: 'iife',
        exports: 'named',
        dir: OUTPUT_DIR,
        entryFileNames: 'aegis.js',
        globals: {
          [utils]: 'Aegis_utils',
          [pageview]: '{}',
          [blankScreen]: '{}',
          [precollect]: '{}',
          [httpTransport]: '{}',
          [pixelTransport]: '{}',
          [consoleTransport]: '{}',
          [action]: '{}',
          [bridge]: '{}',
          [api]: '{}',
          [resource]: '{}',
          [resourceError]: '{}',
          [jsError]: '{}',
          [device]: '{}',
          [perf]: '{}',
          [largePictureInspect]: '{}',
          [feature]: '{}'
        }
      },
      external: [utils, httpTransport, pageview, precollect, pixelTransport, consoleTransport, blankScreen, action, bridge, api, resource, resourceError, jsError, device, perf, largePictureInspect, feature],
      plugins: [
        nodeResolve({
          extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
        }),
        commonjs(),
        removeEsModuleProperty(),
        removeExports('Aegis'),
        addCommentsToModules(),
        removeUselessCode(),
        ...(shouldCompress
          ? [
              comboCompress({
                minify: true, // 启用代码压缩
                compress: {
                  unused: true, // 移除未使用的函数和变量
                  dead_code: true // 移除不可达代码
                },
                mangle: {
                  // 代码混淆
                  properties: {
                    // 属性名混淆设置
                    regex: /^__(?!vue__|vue_app__)/ // 只混淆以双下划线 "__" 开头的属性名，但不混淆 __vue__ 和 __vue_app__ 属性名
                  }
                }
              })
            ]
          : [])
      ],
      onwarn: (warning, warn) => {
        if (warning.code === 'UNKNOWN_OPTION') {
          return
        }

        warn(warning)
      },
      _entryPoint: true
    }
  ]
}

/**
 * 对代码进行合并压缩
 *
 * 该函数主要进行以下操作：
 * 1. 根据注释提取单个模块
 * 2. 获取 aegis.js 和 utils.js 模块，并将其代码合并为一个字符串写入 aegis.js 文件中，并删除 utils.js 文件
 * 3. 获取其他模块，如：pageview.js, precollect.js, http.js 等，并将其代码写入对应的文件中
 * 4. 重新获取所有文件路径，显示文件大小，并为文件添加哈希值
 *
 * 注意：
 * 为了保证压缩和未压缩处理方式一致，这里使用了注释的方式来标记模块的开始和结束
 *
 */
function comboCompress(minifyOptions = {}) {
  return {
    name: 'combo-compress',
    async writeBundle(outputOptions) {
      // 获取所有IIFE模块的文件路径
      let filePaths = getAllFilePaths(join(_dirname, outputOptions.dir))
      // 将所有IIFE模块的代码合并为一个字符串
      const comboCode = filePaths.map((filePath) => readFileSync(filePath, 'utf-8')).join('')

      function minifyOutputHandler(result) {
        // 提取单个模块 [ { name: 'dist/iife/integrations/browser/pageview.js', code: '..........................................' }, ... ]
        const modules = extractSingleModules(result.code)
        // 获取 aegis.js 模块
        const aegisModule = modules.filter((module) => module.name === join(outputOptions.dir, 'aegis.js'))[0]
        // 获取 utils.js 模块
        const utilsModule = modules.filter((module) => module.name === join(outputOptions.dir, 'utils.js'))[0]
        // 获取其他模块，如：pageview.js, precollect.js, http.js 等
        const otherModule = modules.filter((module) => module.name !== join(outputOptions.dir, 'aegis.js') && module.name !== join(outputOptions.dir, 'utils.js'))
        // 对其他模块进行遍历，将每个模块的代码写入对应的文件中
        otherModule.forEach((module) => {
          const filePath = filePaths.filter((filePath) => filePath.includes(module.name))[0]

          writeFileSync(filePath, module.code, 'utf-8')
        })

        // 获取 aegis.js 和 utils.js 的文件路径
        const aegisFilePath = filePaths.filter((filePath) => filePath.includes(aegisModule.name))[0]
        const utilsFilePath = filePaths.filter((filePath) => filePath.includes(utilsModule.name))[0]

        // 将 utils.js 和 aegis.js 的代码合并为一个字符串，并写入 aegis.js 文件中
        // 这里要注意 utils.js 的代码要放在 aegis.js 的代码之前，因为 aegis.js 中会引用 utils.js 中的代码
        writeFileSync(aegisFilePath, `${utilsModule.code}\n${aegisModule.code}`, 'utf-8')

        // 删除 utils.js 文件
        unlinkSync(utilsFilePath)
        // 重新获取所有文件路径
        filePaths = getAllFilePaths(join(_dirname, outputOptions.dir))
        // 显示文件大小
        showFileSizes(filePaths, outputOptions.dir)
        // 为文件添加哈希值
        renameFilesWithHash(filePaths)
      }

      // 如果 minify 为 true 则进行代码压缩
      if (minifyOptions.minify) {
        delete minifyOptions.minify
        await minify(comboCode, minifyOptions).then(minifyOutputHandler)
      } else {
        minifyOutputHandler({ code: comboCode })
      }
    }
  }
}

/**
 * 使用 ascii 表格显示文件大小
 *
 * 主要显示以下内容：
 * 1. 文件相对路径 (File)
 * 2. 未压缩大小 (Bundle Size)
 * 3. Gzip 压缩大小 (Gzip Size)
 */
function showFileSizes(filePaths, dirPath) {
  let totalSize = 0
  let totalGzipSize = 0
  const table = createOutputTable()

  table.push(['File', 'Bundle Size', 'Gzip Size'])

  for (const filePath of filePaths) {
    const content = readFileSync(filePath, 'utf-8')

    totalSize += content.length
    totalGzipSize += gzipSizeSync(content)

    if (basename(filePath) === 'aegis.js') {
      table.push([basename(filePath), filesize(content.length), filesize(gzipSizeSync(content))])
    } else {
      table.push([filePath.replace(join(_dirname, dirPath, '/'), ''), filesize(content.length), filesize(gzipSizeSync(content))])
    }
  }

  table.push(['Total', filesize(totalSize), filesize(totalGzipSize)])

  console.log(table.toString())
}

function createOutputTable() {
  return new Table({
    chars: {
      top: '═',
      'top-mid': '╤',
      'top-left': '╔',
      'top-right': '╗',
      bottom: '═',
      'bottom-mid': '╧',
      'bottom-left': '╚',
      'bottom-right': '╝',
      left: '║',
      'left-mid': '╟',
      mid: '─',
      'mid-mid': '┼',
      right: '║',
      'right-mid': '╢',
      middle: '│'
    }
  })
}

/**
 * 对文件进行哈希值重命名
 *
 * 主要进行以下操作：
 * 1. 对文件进行哈希值计算
 * 2. 将文件重命名为 `文件名.哈希值.js` 的格式
 */
async function renameFilesWithHash(filePaths) {
  for (const filePath of filePaths) {
    const result = await hash(readFileSync(filePath, 'utf-8'), { algorithm: 'sha1' })

    renameSync(filePath, filePath.replace('.js', `.${result.slice(0, 8)}.js`))
  }
}

/**
 * 从压缩后的代码中提取单个模块
 *
 * 主要进行以下操作：
 * 1. 使用正则表达式匹配被特殊注释包围的模块代码
 * 2. 提取每个模块的名称和代码内容
 * 3. 处理代码末尾可能存在的逗号
 *
 * Eg:
 *
 * Before:
 *
 * ```javascript
 * /* Module dist/iife/integrations/browser/pageview.js: Start @preserve*\/
 * Aegis.pageview = (function (_, require$$0) {
 * ........
 * })({}, Aegis_utils);
 * /* Module dist/iife/integrations/browser/pageview.js: End @preserve*\/
 * /* Module dist/iife/integrations/browser/precollect.js: Start @preserve*\/
 * Aegis.precollect = (function (_, require$$0) {
 * .....
 * })({}, Aegis_utils);
 * /* Module dist/iife/integrations/browser/precollect.js: End @preserve*\/
 * ```
 *
 * After:
 *
 * ```javascript
 *[
 *  {
 *    name: 'dist/iife/integrations/browser/pageview.js',
 *    code: '..........................................'
 *  }
 *  {
 *    name: 'dist/iife/integrations/browser/precollect.js',
 *    code: '..........................................'
 *  }
 * ]
 * ```
 *
 */
function extractSingleModules(minifyCode) {
  const extractRegex = /\/\*\s*Module\s+(.+?)\s*:\s*Start\s+@preserve\s*\*\/([\s\S]*?)\n\/\*\s*Module\s+\1\s*:\s*End\s+@preserve\s*\*\//g

  let modules = []
  let matchCode = []

  while ((matchCode = extractRegex.exec(minifyCode)) !== null) {
    const code = matchCode[2].trim()
    const tailIsComma = code[code.length - 1] === ','

    modules = [...modules, { name: matchCode[1], code: tailIsComma ? `${code.slice(0, -1)};` : code }]
  }

  return modules
}

function getAllFilePaths(dirPath) {
  const results = []
  const files = readdirSync(dirPath)

  files.forEach(function (file) {
    file = join(dirPath, file)

    if (statSync(file).isDirectory()) {
      results.push(...getAllFilePaths(file))
    } else {
      results.push(file)
    }
  })

  return results
}

/**
 * 移除代码中的注释
 *
 * 移除以下类型的注释：
 * 1. 多行注释 (/* ... *\/)
 * 2. 单行注释 (// ...)
 *
 */
function removeComments() {
  return {
    name: 'remove-comments',
    transform(code) {
      const newCode = code.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '')

      return {
        code: newCode,
        map: null
      }
    }
  }
}

/**
 * 为模块添加注释
 * 在构建过程中为每个模块添加开始和结束的注释标记
 * 这些注释可以帮助后面在最终做文件构建时识别不同模块的边界，方便调试，代码分析，以及代码拆分
 * 因为 terser 在压缩代码时会移除所有注释，所以这里使用了 `@preserve` 来标记这些注释
 *
 * Eg:
 *
 * Before:
 *
 * ```javascript
 * Aegis.pageview = (function (_, require$$0) {
 *  'use strict';
 *
 * var pageview$1 = {};
 * var pageview_2 = pageview$1.pageview = pageview;
 *
 * return pageview;
 *
 * })({}, Aegis_utils);
 * ```
 *
 * After:
 *
 * ```javascript
 * \/* Module dist/iife/integrations/browser/pageview.js: Start @preserve*\/
 * Aegis.pageview = (function (_, require$$0) {
 * 'use strict';
 *
 * var pageview$1 = {};
 * var pageview_2 = pageview$1.pageview = pageview;
 *
 * return pageview;
 *
 * })({}, Aegis_utils);
 * \/* Module dist/iife/integrations/browser/pageview.js: End @preserve*\/
 * ```
 */
function addCommentsToModules() {
  return {
    name: 'add-comments-to-modules',
    renderChunk(code, chunk, options) {
      const commentStart = `/* Module ${join(options.dir, chunk.fileName)}: Start @preserve*/\n`
      const commentEnd = `\n/* Module ${join(options.dir, chunk.fileName)}: End @preserve*/\n`

      return `${commentStart}${code}${commentEnd}`
    }
  }
}

/**
 * 移除 CommonJS 模块中的 exports 相关代码
 * 用于在将 CommonJS 模块转换为 IIFE 格式时清理 exports 相关的代码
 *
 * 主要进行以下操作：
 * 1. 将 `(exports,` 替换为 `(_,`，移除 exports 参数
 * 2. 移除所有 `exports.xxx = ...;` 形式的赋值
 * 3. 移除 `exports.default = ...;` 形式的默认导出
 * 4. 将 `return exports;` 替换为 `return ${exportsName}`
 *
 * Eg:
 *
 * Before:
 *
 * ```javascript
 * Aegis.pageview = (function (exports, require$$0) {
 *  'use strict';
 *
 * var pageview$1 = {};
 * var pageview_2 = pageview$1.pageview = pageview;
 *
 * exports.default = pageview$1;
 * exports.pageview = pageview_2;
 *
 * return exports;
 *
 * })({}, Aegis_utils);
 * ```
 *
 * After:
 *
 * ```javascript
 * Aegis.pageview = (function (_, require$$0) {
 *  'use strict';
 *
 * var pageview$1 = {};
 * var pageview_2 = pageview$1.pageview = pageview;
 *
 * return pageview;
 *
 * })({}, Aegis_utils);
 * ```
 *
 * @param {string} exportsName - 用于替换 `return exports` 的名称
 */
function removeExports(exportsName) {
  return {
    name: 'remove-exports',
    renderChunk(code) {
      const exportsReturnRegex = /return exports;/g
      const exportsParamRegex = /\(\s*exports\s*,/g
      const exportAssignmentRegex = /exports\.\w+\s*=\s*.+?;\s*/g
      const defaultExportRegex = /exports\.default\s*=\s*.+?;\s*/g

      let newCode = code.replace(exportsParamRegex, '(_,').replace(exportAssignmentRegex, '').replace(defaultExportRegex, '').replace(exportsReturnRegex, `return ${exportsName}`)

      return { code: newCode, map: null }
    }
  }
}

/**
 * 移除 ES 模块的 `__esModule` 标记
 * 在转译 ES 模块到 CommonJS 模块时，通常会添加 `__esModule` 标记来标识模块原本是 ES 模块
 * 在转换为 IIFE 模块时，需要移除这个标记，因为在浏览器环境中直接使用时不需要这个标识
 * 移除这个标记可以减小代码体积，并避免不必要的属性定义
 *
 * Eg:
 *
 * Before:
 *
 * ```javascript
 * exports.default = pageview$1;
 * exports.pageview = pageview_2;
 *
 * Object.defineProperty(exports, '__esModule', { value: true });
 *
 * return exports;
 * ```
 *
 * After:
 *
 * ```javascript
 * exports.default = pageview$1;
 * exports.pageview = pageview_2;
 *
 * return exports;
 * ```
 *
 */
function removeEsModuleProperty() {
  return {
    name: 'remove-es-module-property',
    renderChunk(code) {
      const newCode = code.replace(/Object\.defineProperty\(exports,\s*'__esModule',\s*\{\s*value:\s*true\s*\}\);\s*/g, '')

      return { code: newCode, map: null }
    }
  }
}

/**
 * 简化 Aegis 命名空间
 *
 * 主要进行以下操作：
 * 1. 移除 Aegis 命名空间的初始化代码，因为 Aegis 已经在全局对象上定义了
 * 2. 将 `this.Aegis.` 替换为 `Aegis.`，因为在 IIFE 中 Aegis 会被定义为全局变量，不需要通过 `this` 访问
 *
 * Eg:
 *
 * Before:
 *
 * ```javascript
 * this.Aegis = this.Aegis || {};
 * this.Aegis.pageview = (function (exports, require$$0) {
 *  'use strict';
 *
 * var pageview$1 = {};
 * })({}, Aegis_utils);
 * ```
 *
 * After:
 *
 * ```javascript
 * Aegis.pageview = (function (exports, require$$0) {
 * 'use strict';
 *
 * var pageview$1 = {};
 * })({}, Aegis_utils);
 * ```
 *
 */
function removeAegisNamespace() {
  return {
    name: 'remove-aegis-namespace',
    renderChunk(code) {
      const newCode = code.replace(/this\.Aegis\s*=\s*this\.Aegis\s*\|\|\s*{};\n?/g, '').replace(/this\.Aegis\./g, 'Aegis.')

      return { code: newCode, map: null }
    }
  }
}

/**
 * 移除无用代码
 *
 * aegis.js 专用插件，主要是移除 aegis.js 中的无用代码
 */
function removeUselessCode() {
  return {
    name: 'remove-useless-code',
    renderChunk(code) {
      const newCode = code
        .replace('var HttpTransport = aegis.HttpTransport = transports_http.HttpTransport;', '')
        .replace('var api = aegis.api = integrations_browser_api.index;', '')
        .replace('var bridge = aegis.bridge = integrations_browser_bridge.bridge;', '')
        .replace('var feature = aegis.feature = integrations_browser_feature.feature;', '')
        .replace('var action = aegis.action = integrations_browser_action.action;', '')
        .replace('var device = aegis.device = integrations_browser_device.device;', '')
        .replace('var jsError = aegis.jsError = integrations_browser_jsError.jsError;', '')
        .replace('var perf = aegis.perf = integrations_browser_perf.index;', '')
        .replace('var pageview = aegis.pageview = integrations_browser_pageview.pageview;', '')
        .replace('var resource = aegis.resource = integrations_browser_resource.resource;', '')
        .replace('var PixelTransport = aegis.PixelTransport = transports_browser_pixel.PixelTransport;', '')
        .replace('var precollect = aegis.precollect = integrations_browser_precollect.precollect;', '')
        .replace('var ConsoleTransport = aegis.ConsoleTransport = transports_console.ConsoleTransport;', '')
        .replace('var blankScreen = aegis.blankScreen = integrations_browser_blankScreen.blankScreen;', '')
        .replace('var resourceError = aegis.resourceError = integrations_browser_resourceError.resourceError;', '')
        .replace('var largePictureInspect = aegis.largePictureInspect = integrations_browser_largePictureInspect.largePictureInspect;', '')
        .replace('var Aegis_1 = aegis.Aegis = Aegis;', '')

      return { code: newCode, map: null }
    }
  }
}
