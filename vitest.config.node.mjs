import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    include: ['test/core/**/*.spec.ts', 'test/transports/**/*.spec.ts', 'test/utils/**/*.spec.ts'],
    exclude: ['**/browser/**/*.{test,spec}.?(c|m)[jt]s?(x)', 'test/utils/loader.spec.ts'],
    coverage: {
      enabled: true,
      provider: 'istanbul',
      reporter: ['json'],
      reportsDirectory: './coverage/node',
      exclude: ['**/example/**', '*.cjs', '**/test/mocks/**', '**/scripts/**', '**/coverage/**']
    }
  }
})
