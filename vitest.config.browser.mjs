import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    browser: {
      enabled: true,
      name: 'chromium',
      headless: true,
      provider: 'playwright',
      isolate: true
    },
    coverage: {
      enabled: true,
      provider: 'istanbul',
      reporter: ['json'],
      reportsDirectory: './coverage/browser',
      exclude: ['**/example/**', '*.cjs', '**/test/mocks/**', '**/scripts/**', '**/coverage/**']
    }
  }
})
