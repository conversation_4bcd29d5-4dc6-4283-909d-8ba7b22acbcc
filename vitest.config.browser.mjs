import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    include: ['test/integrations/browser/performance/onSPA_LOAD.spec.ts'],
    browser: {
      enabled: true,
      name: 'chromium',
      headless: true,
      provider: 'playwright',
      isolate: true
    },
    coverage: {
      enabled: true,
      provider: 'istanbul',
      reporter: ['json'],
      reportsDirectory: './coverage/browser',
      exclude: ['**/example/**', '*.cjs', '**/test/mocks/**', '**/scripts/**', '**/coverage/**']
    }
  }
})
