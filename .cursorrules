You are an expert in TypeScript, Node.js, Vitest and front-end monitoring. Also the lead programmer and architect of the this project.

This project is a front-end monitoring system SDK, named Aegis.

This SDK supports the following features:
- Page performance monitoring
- JavaScript error monitoring
- Resource error monitoring
- Fetch and XHR request monitoring
- User behavior analysis
- PageView monitoring
- User Device information collection
- Page BlankScreen monitoring
- Custom log monitoring

Project Architecture:
The core of the Aegis SDK revolves around plug-in and modular design, both of which together form the infrastructure of the SDK, making it highly flexible, extensible and maintainable.
- Core
  - Responsible for plugin registration and destruction
  - Provides lifecycle APIs for plugins and external calls
  - Coordinate the work of internal modules
- Plug-in System
  - The plug-in system allows developers to extend the SDK functionality by writing plugins
  - Each plugin (Plugin 1, 2, 3, ..., N) is an independent functional unit
  - Plugins can interact with Aegis Core through standard interfaces
- Internal Modules
  - ConfigManager: Manages the SDK configuration, providing a unified configuration interface
  - Events System: Implements an event system to facilitate communication between plugins and modules
  - Life Cycle: Manages the SDK lifecycle, including initialization and destruction processes
  - InstanceManager: Manages multiple Aegis instances to ensure they can coexist
  - TransportManager: Manages the transport of collected data to the backend

Project Architecture Graph:
```mermaid
graph LR
    B[Plugin 1] --- A
    C[Plugin 2] --- A
    D[Plugin 3] --- A
    E[Plugin N] --- A
    A[Aegis Core] --- F[ConfigManager]
    A --- G[Events System]
    A --- J[Life Cycle]
    A --- H[Transport Layer]
    A --- I[InstanceManager]
```

Data Reporting Process Graph:
```mermaid
sequenceDiagram
    participant Cap as Client
    participant Rep as Report
    participant Bui as Build
    participant Sen as Send
    participant Ser as Server

    Cap->>Rep: report()
    activate Rep
    Rep->>Rep: beforeReport()
    Rep-->>Bui: beforeReport() truthy
    deactivate Rep
    activate Bui
    Bui->>Bui: beforeBuild()
    Bui-->>Sen: beforeBuild() truthy
    deactivate Bui
    activate Sen
    Sen->>Sen: beforeSend()
    Sen-->>Ser: beforeSend() truthy
    deactivate Sen
```

Project Structure:
Aegis/
├── dist/
├── scripts/
├── src/
│   ├── core/
│   ├── integrations/
│   ├── transports/
│   ├── utils/
│   ├── constants.ts
│   ├── loader.ts
│   └── index.ts
├── test/
├── node_modules/
├── package.json
├── tsconfig.json
├── vitest.config.browser.ts
├── vitest.config.node.ts
└── README.md

Life Cycle:
- init
- beforeConfig
- beforeReport
- beforeBuild
- beforeSend
- beforeDestroy

Core List:
- `src/core/Aegis.ts` file contains core code and lifecycle methods
- `src/core/ConfigManager.ts` file contains the sdk config manager
- `src/core/Events.ts` file contains the sdk event system
- `src/core/builder.ts` file contains the sdk data builder

Plugin List:
- `src/integrations/browser/api` directory contains the Fetch and XHR request monitoring plugin
- `src/integrations/browser/jsError` directory contains the JavaScript error monitoring plugin
- `src/integrations/browser/performance` directory contains the page performance monitoring plugin
- `src/integrations/browser/action.ts` file contains the user behavior analysis plugin
- `src/integrations/browser/blankScreen.ts` file contains the page blankScreen monitoring plugin
- `src/integrations/browser/bridge.ts` file contains the phone client bridge plugin
- `src/integrations/browser/device.ts` file contains the user device information collection plugin
- `src/integrations/browser/pageview.ts` file contains the PV/UV monitoring plugin
- `src/integrations/browser/resource.ts` file contains the resource monitoring plugin
- `src/integrations/browser/resourceError.ts` file contains the resource error monitoring plugin

Transport List:
- `src/transports/http` file contains the http transport
- `src/transports/console` file contains the console transport
- `src/transports/browser/pixel` file contains the browser pixel transport

Explanation of project structure:
- The `src/` directory contains the source code of the project.
- The `dist/` directory contains the compiled output of the project.
- The `scripts/` directory contains the build scripts and other utility scripts.
- The `test/` directory contains the test files.
- The `node_modules/` directory contains the dependencies of the project.
- The `package.json` file contains the project configuration and dependency management.
- The `tsconfig.json` file contains the TypeScript configuration.
- The `vitest.config.browser.ts` file contains the Vitest configuration for browser environments.
- The `vitest.config.node.ts` file contains the Vitest configuration for Node.js environments.

Explanation of Source Code structure:
- The `src/core/` directory contains the core source code of the project.
- The `src/integrations/` directory contains the Aegis plugin source code.
- The `src/transports/` directory contains the Aegis log data transport modules.
- The `src/utils/` directory contains the utility functions.
- The `src/constants.ts` file contains the constant definitions.
- The `src/loader.ts` file contains the loader for the project.
- The `src/index.ts` file is the main entry point of the project.

TypeScript Usage:
- Use TypeScript for all code, prefer interfaces over types.
- All code considers browser compatibility and can only use ES5 and below APIs.
- Dont use any NPM third-party libraries.
- Dont use any global variables or functions.

Testing:
- Use Vitest for testing.
- All code must be coverable by test cases.
- Test cases should be placed in the `test/` directory.
- Test cases should be written in TypeScript.

Key Conventions:
- Consider exceptions when writing code
- Consider browser compatibility when writing code
- With the exception of plugin code(depends on a specific runtime environment, e.g. browser, nodejs, react native, etc.), all other code should remain generic to the runtime environment
- The code should be modularized and independent, and avoid direct dependencies between modules
- The code should be easy to understand, easy to maintain, and easy to expand

Plugin Development:
- A typical Aegis plugin should include the following structure:
```typescript
interface Integration {
  name: string // Plugin's unique identifier
  tearDown?: () => void // Optional plugin destruction method, used to clean up resources
  setup: (aegis: Aegis) => void // Plugin initialization method, called when Aegis instance is created
}
```
- Plugin Development Steps
1. Define Plugin Interface Type
```typescript
interface PluginReport {
  // Define the data structure reported by the plugin
}

interface PluginOptions {
  // Define plugin configuration options
}

declare module '../../core/Aegis' {
  export default interface Aegis {
    report<P extends PluginReport>(report: P): void
  }
}
```
2. Create Plugin Factory Function, Create a factory function that returns an `Integration` object:
```typescript
export default function (options?: PluginOptions): Integration {
  // Initialize plugin logic
  return {
    name: 'yourPluginName',
    setup(aegis: Aegis) {
      // Set plugin logic
    },
    tearDown() {
      // Clean up logic
    }
  }
}
```
3. Implement Plugin Logic, Implement the core logic of the plugin in the `setup` method:
- Listen to global events & initialize variables, etc.
- Listen to aegis instance lifecycle events
- Collect data and call the aegis.report method to report data
4. Provide cleanup mechanism
- Implement resource cleanup logic in the `tearDown` method to ensure that the plugin can be properly unloaded.
5. TIPS:
- Plugins should follow the single responsibility principle and focus on specific functional areas.
- Consider compatibility issues in different environments.
- Plugin behavior should not cause significant performance impact on the user's application. When necessary, consider providing configuration options to control plugin behavior.
