import { defineConfig } from 'rollup'
import { readFileSync } from 'node:fs'
import json from '@rollup/plugin-json'
import clean from 'rollup-plugin-delete'
import babel from '@rollup/plugin-babel'
import terser from '@rollup/plugin-terser'
import replace from '@rollup/plugin-replace'
import commonjs from '@rollup/plugin-commonjs'
import { DEFAULT_EXTENSIONS } from '@babel/core'
import typescript from 'rollup-plugin-typescript2'
import externals from 'rollup-plugin-node-externals'
import { nodeResolve, DEFAULTS } from '@rollup/plugin-node-resolve'

const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'))

export default defineConfig([
  {
    input: 'src/index.ts',
    output: {
      format: 'cjs',
      dir: 'dist/cjs',
      preserveModules: true,
      preserveModulesRoot: 'src'
    },
    plugins: [
      clean({ targets: ['dist/cjs', 'types/*'] }),
      externals({ deps: true }),
      nodeResolve({
        extensions: [...DEFAULTS.extensions, '.ts', '.tsx']
      }),
      commonjs(),
      json(),
      replace({
        preventAssignment: true,
        __SDK_VERSION__: pkg.version,
        'process.env.NODE_ENV': JSON.stringify('production'),
        __SDK_NAME__: `${pkg.name.charAt(0).toUpperCase()}${pkg.name.slice(1)}`
      }),
      typescript({ useTsconfigDeclarationDir: true }),
      babel({
        babelHelpers: 'runtime',
        exclude: '**/node_modules/**',
        extensions: [...DEFAULT_EXTENSIONS, '.ts', '.tsx']
      }),
      terser()
    ]
  }
])
